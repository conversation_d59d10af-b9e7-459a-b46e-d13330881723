# Multi-stage build para máxima otimização
FROM --platform=linux/amd64 python:3.8-slim as builder

# Instalar dependências de build
RUN apt-get update && apt-get install -y \
    libmariadb-dev \
    build-essential \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Instalar dependências Python
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# Estágio final
FROM --platform=linux/amd64 python:3.8-slim

# Instalar apenas dependências de runtime
RUN apt-get update && apt-get install -y \
    libmariadb3 \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# Copiar dependências Python do builder
COPY --from=builder /root/.local /root/.local

# Adicionar ao PATH
ENV PATH=/root/.local/bin:$PATH

# Definir diretório de trabalho
WORKDIR /app

# Copiar código da aplicação
COPY manage.py .
COPY associado/ ./associado/
COPY associacao/ ./associacao/
COPY mailapp/ ./mailapp/
COPY templates/ ./templates/
COPY static/ ./static/
COPY samples/ ./samples/
COPY docker-entrypoint.sh .

# Dar permissão ao script
RUN chmod +x docker-entrypoint.sh

# Expor porta
EXPOSE 8000

# Comando padrão
CMD ["./docker-entrypoint.sh"]
