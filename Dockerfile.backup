# Versão mais simples e rápida
FROM python:3.8-slim

# Instalar dependências mínimas
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    netcat-openbsd \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copiar e instalar requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código
COPY . .

EXPOSE 8000

# Comando simples
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
