# Alternativa usando Ubuntu com Python 3.7
FROM --platform=linux/amd64 ubuntu:18.04

# Instalar Python 3.7 e dependências
RUN apt-get update && apt-get install -y \
    python3.7 \
    python3.7-dev \
    python3-pip \
    python3.7-venv \
    default-libmysqlclient-dev \
    build-essential \
    pkg-config \
    netcat \
    && rm -rf /var/lib/apt/lists/*

# Criar link simbólico para python
RUN ln -s /usr/bin/python3.7 /usr/bin/python

# Atualizar pip
RUN python -m pip install --upgrade pip

# Definir diretório de trabalho
WORKDIR /app

# Copiar requirements
COPY requirements.txt .

# Instalar dependências Python
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código da aplicação
COPY . .

# Copiar e dar permissão ao script de entrada
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Expor porta
EXPOSE 8000

# Comando padrão
CMD ["docker-entrypoint.sh"]
