from django.db import models

# Create your models here.


class MailTemplate(models.Model):
    """
    main class form mail templates
    """
    SITUACAO_EMAIL = (
        ("pagamento","Pagamento"),
        ("cancelamento","Cancelamento"),
        ("controle", "<PERSON>e"),
        ("assinatura", "Assinatura"),
    )
    returnpath = models.EmailField(null=True, blank=True)
    bcc = models.EmailField(null=True, blank=True)
    cc = models.EmailField(null=True, blank=True)
    subject = models.CharField(max_length=200, null=True, blank=True)
    from_email = models.EmailField(null=True, blank=True)
    reply_to = models.EmailField(null=True, blank=True)
    variables = models.CharField(max_length=200, null=True, blank=True)
    html = models.CharField(max_length=150)
    txt = models.CharField(max_length=150)
    traducao = models.CharField(max_length=150, choices=[('pt-br', 'Portugues'), ('en', 'Inglês'), ('es', 'Espanhol')],
                                default='pt-br')
    situacao = models.CharField(
        max_length=50, choices=SITUACAO_EMAIL, default="pagamento")

    def __str__(self):
        return "Template of: {0} [{1}]".format(
            self.situacao,
            self.html,
            self.traducao
        )


class Mail(models.Model):
    """
    all sent mail
    """
    mailtemplate = models.ForeignKey(MailTemplate, on_delete=models.CASCADE)
    to = models.EmailField()
    from_email = models.EmailField()
    created = models.DateTimeField(auto_now_add=True)
    sent = models.BooleanField(default=False)
    body_txt = models.TextField()
    body_html = models.TextField()

    def __str__(self):
        if self.sent:
            return "Sent to: {0}, date:{1}".format(
                self.to,
                self.created,
            )
        else:
            return "Not sent to: {0}, date:{1}".format(
                self.to,
                self.created,
            )