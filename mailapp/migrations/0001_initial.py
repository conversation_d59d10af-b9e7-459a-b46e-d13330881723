# -*- coding: utf-8 -*-
# Generated by Django 1.9.5 on 2018-03-22 16:11
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Mail',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('to', models.EmailField(max_length=254)),
                ('from_email', models.EmailField(max_length=254)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('sent', models.BooleanField(default=False)),
                ('body_txt', models.TextField()),
                ('body_html', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='MailTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('returnpath', models.EmailField(blank=True, max_length=254, null=True)),
                ('bcc', models.EmailField(blank=True, max_length=254, null=True)),
                ('cc', models.EmailField(blank=True, max_length=254, null=True)),
                ('subject', models.CharField(blank=True, max_length=200, null=True)),
                ('from_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('reply_to', models.EmailField(blank=True, max_length=254, null=True)),
                ('variables', models.CharField(blank=True, max_length=200, null=True)),
                ('html', models.CharField(max_length=150)),
                ('txt', models.CharField(max_length=150)),
            ],
        ),
        migrations.AddField(
            model_name='mail',
            name='mailtemplate',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mailapp.MailTemplate'),
        ),
    ]
