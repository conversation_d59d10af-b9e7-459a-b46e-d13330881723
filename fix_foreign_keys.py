#!/usr/bin/env python3
"""
Script para corrigir ForeignKey e OneToOneField sem on_delete
"""
import re
import os

def fix_foreign_keys_in_file(filepath):
    """Corrige ForeignKey e OneToOneField em um arquivo"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Padrões para ForeignKey sem on_delete
        fk_pattern = r'(models\.ForeignKey\([^)]+)(\))'
        fk_matches = re.finditer(fk_pattern, content)
        
        for match in reversed(list(fk_matches)):
            full_match = match.group(0)
            if 'on_delete' not in full_match:
                # Adicionar on_delete=models.CASCADE antes do )
                new_match = full_match[:-1] + ', on_delete=models.CASCADE)'
                content = content[:match.start()] + new_match + content[match.end():]
        
        # Padrões para OneToOneField sem on_delete
        o2o_pattern = r'(models\.OneToOneField\([^)]+)(\))'
        o2o_matches = re.finditer(o2o_pattern, content)
        
        for match in reversed(list(o2o_matches)):
            full_match = match.group(0)
            if 'on_delete' not in full_match:
                # Adicionar on_delete=models.CASCADE antes do )
                new_match = full_match[:-1] + ', on_delete=models.CASCADE)'
                content = content[:match.start()] + new_match + content[match.end():]
        
        # Salvar apenas se houve mudanças
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Corrigido: {filepath}")
            return True
        else:
            print(f"Nenhuma correção necessária: {filepath}")
            return False
            
    except Exception as e:
        print(f"Erro ao processar {filepath}: {e}")
        return False

def main():
    """Função principal"""
    # Arquivos para corrigir
    files_to_fix = [
        'associacao/models.py',
        'mailapp/models.py'
    ]
    
    total_fixed = 0
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            if fix_foreign_keys_in_file(filepath):
                total_fixed += 1
        else:
            print(f"Arquivo não encontrado: {filepath}")
    
    print(f"\nTotal de arquivos corrigidos: {total_fixed}")

if __name__ == '__main__':
    main()
