var FormWizard = function() {
    return {
        init: function() {
            function e(e) {
                return e.id ? "<img class='flag' src='../../assets/global/img/flags/" + e.id.toLowerCase() + ".png'/>&nbsp;&nbsp;" + e.text : e.text
            }
            if (jQuery().bootstrapWizard) {
                $("#id_tipo").select2({
                    placeholder: "Select",
                    allowClear: !0,
                    formatResult: e,
                    width: "auto",
                    formatSelection: e,
                    escapeMarkup: function(e) {
                        return e
                    }
                });
                var r = $("#submit_form")
                  , t = $(".alert-danger", r)
                  , i = $(".alert-success", r);
                r.validate({
                    doNotHideMessage: !0,
                    errorElement: "span",
                    errorClass: "help-block help-block-error",
                    focusInvalid: !1,
                    rules: {
                        id_nome: {
                            minlength: 5,
                            required: !0
                        },

                        id_parcelas: {
                            required: !0
                        },
                        id_montante: {
                            required: !0
                        },

                        id_data_inicio: {
                            required: !0
                        },

                    },
                    messages: {
                        "id_tipo[]": {
                            required: "Please select at least one option",
                            minlength: jQuery.validator.format("Please select at least one option")
                        }
                    },
                    errorPlacement: function(e, r) {
                        "gender" == r.attr("name") ? e.insertAfter("#form_gender_error") : "payment[]" == r.attr("name") ? e.insertAfter("#form_payment_error") : e.insertAfter(r)
                    },
                    invalidHandler: function(e, r) {
                        i.hide(),
                        t.show(),
                        App.scrollTo(t, -200)
                    },
                    highlight: function(e) {
                        $(e).closest(".form-group").removeClass("has-success").addClass("has-error")
                    },
                    unhighlight: function(e) {
                        $(e).closest(".form-group").removeClass("has-error")
                    },
                    success: function(e) {
                        "gender" == e.attr("for") || "payment[]" == e.attr("for") ? (e.closest(".form-group").removeClass("has-error").addClass("has-success"),
                        e.remove()) : e.addClass("valid").closest(".form-group").removeClass("has-error").addClass("has-success")
                    },
                    submitHandler: function(e) {
                        i.show(),
                        t.hide(),
                            $('#submit_form')[0].submit(function (event) {
                                alert('estou submetendo');

                            })


                    }
                });
                var a = function() {
                    $("#tab4 .form-control-static", r).each(function() {
                        var e = $('[name="' + $(this).attr("data-display") + '"]', r);
                        if (e.is(":radio") && (e = $('[name="' + $(this).attr("data-display") + '"]:checked', r)),
                        e.is(":text") || e.is("textarea"))
                            $(this).html(e.val());
                        else if (e.is("select"))
                            $(this).html(e.find("option:selected").text());
                        else if (e.is(":radio") && e.is(":checked"))
                            $(this).html(e.attr("data-title"));
                        else if ("payment[]" == $(this).attr("data-display")) {
                            var t = [];
                            $('[name="payment[]"]:checked', r).each(function() {
                                t.push($(this).attr("data-title"))
                            }),
                            $(this).html(t.join("<br>"))
                        }
                    })
                }
                  , o = function(e, r, t) {
                    var i = r.find("li").length
                      , o = t + 1;
                    $(".step-title", $("#submit_form")).text("Step " + (t + 1) + " of " + i),
                    jQuery("li", $("#submit_form")).removeClass("done");
                    for (var n = r.find("li"), s = 0; t > s; s++)
                        jQuery(n[s]).addClass("done");
                    1 == o ? $("#submit_form").find(".button-previous").hide() : $("#submit_form").find(".button-previous").show(),
                    o >= i ? ($("#submit_form").find(".button-next").hide(),
                    $("#submit_form").find(".button-submit").show(),
                    a()) : ($("#submit_form").find(".button-next").show(),
                    $("#submit_form").find(".button-submit").hide()),
                    App.scrollTo($(".page-title"))
                }
                ;
                $("#submit_form").bootstrapWizard({
                    nextSelector: ".button-next",
                    previousSelector: ".button-previous",
                    onTabClick: function(e, r, t, i) {
                        return !1
                    },
                    onNext: function(e, a, n) {
                        return i.hide(),
                        t.hide(),
                        0 == r.valid() ? !1 : void o(e, a, n)
                    },
                    onPrevious: function(e, r, a) {
                        i.hide(),
                        t.hide(),
                        o(e, r, a)
                    },
                    onTabShow: function(e, r, t) {
                        var i = r.find("li").length
                          , a = t + 1
                          , o = a / i * 100;
                        $("#submit_form").find(".progress-bar").css({
                            width: o + "%"
                        })
                    }
                }),
                $("#submit_form").find(".button-previous").hide(),
                $("#submit_form .button-submit").click(function() {
                    $('#submit_form').submit();


                    return false;



                    //$("#submit_form").trigger("submit");
                    alert("Finished! Hope you like it :)")
                }).hide(),
                $("#id_tipo", r).change(function() {
                    r.validate().element($(this))
                })
            }
        }
    }
}();




jQuery(document).ready(function() {
   //Metronic.init(); // init metronic core componets
   Layout.init(); // init layout
   Demo.init(); // init demo features
     FormWizard.init();
    $("#id_categoria").change(function(){
        // Check if current value is "audi"
        if($(this).val() == "Novo"){
            // Show input field
            $("#novo-grupo-form").show();   //This changes display to block
        }else{
            // Hide input field
            $("#novo-grupo-form").hide();
        }
    });

    });

/**
 * Created by superflit2 on 8/22/16.
 */
