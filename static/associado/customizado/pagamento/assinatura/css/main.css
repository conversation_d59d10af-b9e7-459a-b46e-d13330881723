body{
    background-color:black
}
.page-header-fixed .page-container {
	margin-top: 0;
	padding-top: 0;
}
.page-content-wrapper .page-content {
    margin-left: auto;
    min-height:auto;
}
.page-content-wrapper {
	float: none;
	width: 1170px;
	margin: 0 auto;
}
.bread {
    width: 100%;
    margin-bottom: 20px;
}
.table{
    margin-top:20px
}
.table.table-striped.table-hover th {
	color: white;
}
.invoice-block{
    text-align:center
}
.invoice-block a {
    display: block;
}
.invoice-block a img {
    width: auto;
    margin-bottom: -35px;
}

  .right-block h4 {
    font-weight: bold;
    margin-bottom: -10px;
    margin-top: 0;
  }
  .right-block p{
      margin-bottom:0;
  }
  
.what.text-center {
    color: #DA041C;
  }
  .what.text-center h4 {
    font-size: 35px;
    font-weight: bold;
    color: white;
    line-height: 45px;
    border-top: 5px solid #DA041C;
    padding: 20px 0;
    padding-bottom:0
  }
  .rpice {
	border-bottom: 5px solid #DA041C;
	padding-bottom: 20px;
	font-size: 30px;
	font-weight: bold;
	color: white;
}
  .rpice b {
    color: #DA041C;
  }
  .what.text-center h4 span {
    color: #DA041C;
  }
  .what.text-center h4 b {
    color: #DA041C;
  }
  
.top {
    color: white;
    font-size: 20px;
    font-weight: bold;
  }
  .top {
  }
  .top a {
    display: block;
    background-color: #DA041C;
    color: white;
    text-transform: uppercase;
    padding: 10px;
    margin-top: 10px;
    width: 180px;
    margin: 0 auto;
    margin-top: 15px;
  }
  .top {
    border-bottom: 5px solid #DA041C;
    padding-bottom: 20px;
  }
  .mid {
    font-weight: bold;
    font-size: 22px;
    padding-bottom: 20px;
  }

.right-block a {
  background-color: #DA041C !important;
  display: block !important;
  border-radius: 0;
}
.right-block h4 {
  font-size: 20px;
  line-height: 26px;
  font-weight: 800;
}
.right-block p {
  font-weight: 400 !important;
}

.right-block a {
  font-weight: bold;
  text-transform: uppercase;
  font-size: 22px;
  border: 0;
}