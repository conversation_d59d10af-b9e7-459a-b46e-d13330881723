/*!
 * Bootstrap-select v1.6.3 (http://silviomoreto.github.io/bootstrap-select/)
 *
 * Copyright 2013-2014 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
(function ($) {
  $.fn.selectpicker.defaults = {
    noneSelectedText: 'Nu a fost selectat nimic',
    noneResultsText: 'Nu exista niciun rezultat',
    countSelectedText: '{0} din {1} selectat(e)',
    maxOptionsText: ['<PERSON>ita a fost atinsa ({n} {var} max)', '<PERSON>ita de grup a fost atinsa ({n} {var} max)', ['iteme', 'item']],
    multipleSeparator: ', '
  };
}(jQuery));
