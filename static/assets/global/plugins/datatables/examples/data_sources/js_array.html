<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>DataTables example - Javascript sourced data</title>
	<link rel="stylesheet" type="text/css" href="../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<style type="text/css" class="init">

	</style>
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">


var dataSet = [
	['Trident','Internet Explorer 4.0','Win 95+','4','X'],
	['Trident','Internet Explorer 5.0','Win 95+','5','C'],
	['Trident','Internet Explorer 5.5','Win 95+','5.5','A'],
	['Trident','Internet Explorer 6','Win 98+','6','A'],
	['Trident','Internet Explorer 7','Win XP SP2+','7','A'],
	['Trident','AOL browser (AOL desktop)','Win XP','6','A'],
	['Gecko','Firefox 1.0','Win 98+ / OSX.2+','1.7','A'],
	['Gecko','Firefox 1.5','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Firefox 2.0','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Firefox 3.0','Win 2k+ / OSX.3+','1.9','A'],
	['Gecko','Camino 1.0','OSX.2+','1.8','A'],
	['Gecko','Camino 1.5','OSX.3+','1.8','A'],
	['Gecko','Netscape 7.2','Win 95+ / Mac OS 8.6-9.2','1.7','A'],
	['Gecko','Netscape Browser 8','Win 98SE+','1.7','A'],
	['Gecko','Netscape Navigator 9','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Mozilla 1.0','Win 95+ / OSX.1+',1,'A'],
	['Gecko','Mozilla 1.1','Win 95+ / OSX.1+',1.1,'A'],
	['Gecko','Mozilla 1.2','Win 95+ / OSX.1+',1.2,'A'],
	['Gecko','Mozilla 1.3','Win 95+ / OSX.1+',1.3,'A'],
	['Gecko','Mozilla 1.4','Win 95+ / OSX.1+',1.4,'A'],
	['Gecko','Mozilla 1.5','Win 95+ / OSX.1+',1.5,'A'],
	['Gecko','Mozilla 1.6','Win 95+ / OSX.1+',1.6,'A'],
	['Gecko','Mozilla 1.7','Win 98+ / OSX.1+',1.7,'A'],
	['Gecko','Mozilla 1.8','Win 98+ / OSX.1+',1.8,'A'],
	['Gecko','Seamonkey 1.1','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Epiphany 2.20','Gnome','1.8','A'],
	['Webkit','Safari 1.2','OSX.3','125.5','A'],
	['Webkit','Safari 1.3','OSX.3','312.8','A'],
	['Webkit','Safari 2.0','OSX.4+','419.3','A'],
	['Webkit','Safari 3.0','OSX.4+','522.1','A'],
	['Webkit','OmniWeb 5.5','OSX.4+','420','A'],
	['Webkit','iPod Touch / iPhone','iPod','420.1','A'],
	['Webkit','S60','S60','413','A'],
	['Presto','Opera 7.0','Win 95+ / OSX.1+','-','A'],
	['Presto','Opera 7.5','Win 95+ / OSX.2+','-','A'],
	['Presto','Opera 8.0','Win 95+ / OSX.2+','-','A'],
	['Presto','Opera 8.5','Win 95+ / OSX.2+','-','A'],
	['Presto','Opera 9.0','Win 95+ / OSX.3+','-','A'],
	['Presto','Opera 9.2','Win 88+ / OSX.3+','-','A'],
	['Presto','Opera 9.5','Win 88+ / OSX.3+','-','A'],
	['Presto','Opera for Wii','Wii','-','A'],
	['Presto','Nokia N800','N800','-','A'],
	['Presto','Nintendo DS browser','Nintendo DS','8.5','C/A<sup>1</sup>'],
	['KHTML','Konqureror 3.1','KDE 3.1','3.1','C'],
	['KHTML','Konqureror 3.3','KDE 3.3','3.3','A'],
	['KHTML','Konqureror 3.5','KDE 3.5','3.5','A'],
	['Tasman','Internet Explorer 4.5','Mac OS 8-9','-','X'],
	['Tasman','Internet Explorer 5.1','Mac OS 7.6-9','1','C'],
	['Tasman','Internet Explorer 5.2','Mac OS 8-X','1','C'],
	['Misc','NetFront 3.1','Embedded devices','-','C'],
	['Misc','NetFront 3.4','Embedded devices','-','A'],
	['Misc','Dillo 0.8','Embedded devices','-','X'],
	['Misc','Links','Text only','-','X'],
	['Misc','Lynx','Text only','-','X'],
	['Misc','IE Mobile','Windows Mobile 6','-','C'],
	['Misc','PSP browser','PSP','-','C'],
	['Other browsers','All others','-','-','U']
];

$(document).ready(function() {
	$('#demo').html( '<table cellpadding="0" cellspacing="0" border="0" class="display" id="example"></table>' );

	$('#example').dataTable( {
		"data": dataSet,
		"columns": [
			{ "title": "Engine" },
			{ "title": "Browser" },
			{ "title": "Platform" },
			{ "title": "Version", "class": "center" },
			{ "title": "Grade", "class": "center" }
		]
	} );	
} );


	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>Javascript sourced data</span></h1>

			<div class="info">
				<p>At times you will wish to be able to create a table from dynamic information passed directly to
				DataTables, rather than having it read from the document. This is achieved using the <a href=
				"//datatables.net/reference/option/data"><code class="option" title=
				"DataTables initialisation option">data<span>DT</span></code></a> option in the initialisation object,
				passing in an array of data to be used (like all other DataTables handled data, this can be arrays or
				objects using the <a href="//datatables.net/reference/option/columns.data"><code class="option" title=
				"DataTables initialisation option">columns.data<span>DT</span></code></a> option).</p>

				<p>A <code>&lt;table&gt;</code> must be available on the page for DataTables to use. This examples
				shows the element being added by Javascript and then initialising the DataTable with a set of data from
				a Javascript array.</p>
			</div>

			<div id="demo"></div>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this
					example:</p><code class="multiline brush: js;">var dataSet = [
	['Trident','Internet Explorer 4.0','Win 95+','4','X'],
	['Trident','Internet Explorer 5.0','Win 95+','5','C'],
	['Trident','Internet Explorer 5.5','Win 95+','5.5','A'],
	['Trident','Internet Explorer 6','Win 98+','6','A'],
	['Trident','Internet Explorer 7','Win XP SP2+','7','A'],
	['Trident','AOL browser (AOL desktop)','Win XP','6','A'],
	['Gecko','Firefox 1.0','Win 98+ / OSX.2+','1.7','A'],
	['Gecko','Firefox 1.5','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Firefox 2.0','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Firefox 3.0','Win 2k+ / OSX.3+','1.9','A'],
	['Gecko','Camino 1.0','OSX.2+','1.8','A'],
	['Gecko','Camino 1.5','OSX.3+','1.8','A'],
	['Gecko','Netscape 7.2','Win 95+ / Mac OS 8.6-9.2','1.7','A'],
	['Gecko','Netscape Browser 8','Win 98SE+','1.7','A'],
	['Gecko','Netscape Navigator 9','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Mozilla 1.0','Win 95+ / OSX.1+',1,'A'],
	['Gecko','Mozilla 1.1','Win 95+ / OSX.1+',1.1,'A'],
	['Gecko','Mozilla 1.2','Win 95+ / OSX.1+',1.2,'A'],
	['Gecko','Mozilla 1.3','Win 95+ / OSX.1+',1.3,'A'],
	['Gecko','Mozilla 1.4','Win 95+ / OSX.1+',1.4,'A'],
	['Gecko','Mozilla 1.5','Win 95+ / OSX.1+',1.5,'A'],
	['Gecko','Mozilla 1.6','Win 95+ / OSX.1+',1.6,'A'],
	['Gecko','Mozilla 1.7','Win 98+ / OSX.1+',1.7,'A'],
	['Gecko','Mozilla 1.8','Win 98+ / OSX.1+',1.8,'A'],
	['Gecko','Seamonkey 1.1','Win 98+ / OSX.2+','1.8','A'],
	['Gecko','Epiphany 2.20','Gnome','1.8','A'],
	['Webkit','Safari 1.2','OSX.3','125.5','A'],
	['Webkit','Safari 1.3','OSX.3','312.8','A'],
	['Webkit','Safari 2.0','OSX.4+','419.3','A'],
	['Webkit','Safari 3.0','OSX.4+','522.1','A'],
	['Webkit','OmniWeb 5.5','OSX.4+','420','A'],
	['Webkit','iPod Touch / iPhone','iPod','420.1','A'],
	['Webkit','S60','S60','413','A'],
	['Presto','Opera 7.0','Win 95+ / OSX.1+','-','A'],
	['Presto','Opera 7.5','Win 95+ / OSX.2+','-','A'],
	['Presto','Opera 8.0','Win 95+ / OSX.2+','-','A'],
	['Presto','Opera 8.5','Win 95+ / OSX.2+','-','A'],
	['Presto','Opera 9.0','Win 95+ / OSX.3+','-','A'],
	['Presto','Opera 9.2','Win 88+ / OSX.3+','-','A'],
	['Presto','Opera 9.5','Win 88+ / OSX.3+','-','A'],
	['Presto','Opera for Wii','Wii','-','A'],
	['Presto','Nokia N800','N800','-','A'],
	['Presto','Nintendo DS browser','Nintendo DS','8.5','C/A&lt;sup&gt;1&lt;/sup&gt;'],
	['KHTML','Konqureror 3.1','KDE 3.1','3.1','C'],
	['KHTML','Konqureror 3.3','KDE 3.3','3.3','A'],
	['KHTML','Konqureror 3.5','KDE 3.5','3.5','A'],
	['Tasman','Internet Explorer 4.5','Mac OS 8-9','-','X'],
	['Tasman','Internet Explorer 5.1','Mac OS 7.6-9','1','C'],
	['Tasman','Internet Explorer 5.2','Mac OS 8-X','1','C'],
	['Misc','NetFront 3.1','Embedded devices','-','C'],
	['Misc','NetFront 3.4','Embedded devices','-','A'],
	['Misc','Dillo 0.8','Embedded devices','-','X'],
	['Misc','Links','Text only','-','X'],
	['Misc','Lynx','Text only','-','X'],
	['Misc','IE Mobile','Windows Mobile 6','-','C'],
	['Misc','PSP browser','PSP','-','C'],
	['Other browsers','All others','-','-','U']
];

$(document).ready(function() {
	$('#demo').html( '&lt;table cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; class=&quot;display&quot; id=&quot;example&quot;&gt;&lt;/table&gt;' );

	$('#example').dataTable( {
		&quot;data&quot;: dataSet,
		&quot;columns&quot;: [
			{ &quot;title&quot;: &quot;Engine&quot; },
			{ &quot;title&quot;: &quot;Browser&quot; },
			{ &quot;title&quot;: &quot;Platform&quot; },
			{ &quot;title&quot;: &quot;Version&quot;, &quot;class&quot;: &quot;center&quot; },
			{ &quot;title&quot;: &quot;Grade&quot;, &quot;class&quot;: &quot;center&quot; }
		]
	} );	
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this
					example:</p>

					<ul>
						<li><a href="../../media/js/jquery.js">../../media/js/jquery.js</a></li>
						<li><a href="../../media/js/jquery.dataTables.js">../../media/js/jquery.dataTables.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by
					DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library
						files (below), in order to correctly display the table. The additional CSS used is shown
						below:</p><code class="multiline brush: js;"></code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the
					table:</p>

					<ul>
						<li><a href=
						"../../media/css/jquery.dataTables.css">../../media/css/jquery.dataTables.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data
					will update automatically as any additional data is loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note
					that this is just an example script using PHP. Server-side processing scripts can be written in any
					language, using <a href="//datatables.net/manual/server-side">the protocol described in the
					DataTables documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="../basic_init/index.html">Basic initialisation</a></h3>
						<ul class="toc">
							<li><a href="../basic_init/zero_configuration.html">Zero configuration</a></li>
							<li><a href="../basic_init/filter_only.html">Feature enable / disable</a></li>
							<li><a href="../basic_init/table_sorting.html">Default ordering (sorting)</a></li>
							<li><a href="../basic_init/multi_col_sort.html">Multi-column ordering</a></li>
							<li><a href="../basic_init/multiple_tables.html">Multiple tables</a></li>
							<li><a href="../basic_init/hidden_columns.html">Hidden columns</a></li>
							<li><a href="../basic_init/complex_header.html">Complex headers (rowspan and
							colspan)</a></li>
							<li><a href="../basic_init/dom.html">DOM positioning</a></li>
							<li><a href="../basic_init/flexible_width.html">Flexible table width</a></li>
							<li><a href="../basic_init/state_save.html">State saving</a></li>
							<li><a href="../basic_init/alt_pagination.html">Alternative pagination</a></li>
							<li><a href="../basic_init/scroll_y.html">Scroll - vertical</a></li>
							<li><a href="../basic_init/scroll_x.html">Scroll - horizontal</a></li>
							<li><a href="../basic_init/scroll_xy.html">Scroll - horizontal and vertical</a></li>
							<li><a href="../basic_init/scroll_y_theme.html">Scroll - vertical with jQuery UI
							ThemeRoller</a></li>
							<li><a href="../basic_init/comma-decimal.html">Language - Comma decimal place</a></li>
							<li><a href="../basic_init/language.html">Language options</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../advanced_init/index.html">Advanced initialisation</a></h3>
						<ul class="toc">
							<li><a href="../advanced_init/events_live.html">DOM / jQuery events</a></li>
							<li><a href="../advanced_init/dt_events.html">DataTables events</a></li>
							<li><a href="../advanced_init/column_render.html">Column rendering</a></li>
							<li><a href="../advanced_init/length_menu.html">Page length options</a></li>
							<li><a href="../advanced_init/dom_multiple_elements.html">Multiple table control
							elements</a></li>
							<li><a href="../advanced_init/complex_header.html">Complex headers (rowspan /
							colspan)</a></li>
							<li><a href="../advanced_init/html5-data-attributes.html">HTML5 data-* attributes</a></li>
							<li><a href="../advanced_init/language_file.html">Language file</a></li>
							<li><a href="../advanced_init/defaults.html">Setting defaults</a></li>
							<li><a href="../advanced_init/row_callback.html">Row created callback</a></li>
							<li><a href="../advanced_init/row_grouping.html">Row grouping</a></li>
							<li><a href="../advanced_init/footer_callback.html">Footer callback</a></li>
							<li><a href="../advanced_init/dom_toolbar.html">Custom toolbar elements</a></li>
							<li><a href="../advanced_init/sort_direction_control.html">Order direction sequence
							control</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="../styling/display.html">Base style</a></li>
							<li><a href="../styling/no-classes.html">Base style - no styling classes</a></li>
							<li><a href="../styling/cell-border.html">Base style - cell borders</a></li>
							<li><a href="../styling/compact.html">Base style - compact</a></li>
							<li><a href="../styling/hover.html">Base style - hover</a></li>
							<li><a href="../styling/order-column.html">Base style - order-column</a></li>
							<li><a href="../styling/row-border.html">Base style - row borders</a></li>
							<li><a href="../styling/stripe.html">Base style - stripe</a></li>
							<li><a href="../styling/bootstrap.html">Bootstrap</a></li>
							<li><a href="../styling/foundation.html">Foundation</a></li>
							<li><a href="../styling/jqueryUI.html">jQuery UI ThemeRoller</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./index.html">Data sources</a></h3>
						<ul class="toc active">
							<li><a href="./dom.html">HTML (DOM) sourced data</a></li>
							<li><a href="./ajax.html">Ajax sourced data</a></li>
							<li class="active"><a href="./js_array.html">Javascript sourced data</a></li>
							<li><a href="./server_side.html">Server-side processing</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../api/index.html">API</a></h3>
						<ul class="toc">
							<li><a href="../api/add_row.html">Add rows</a></li>
							<li><a href="../api/multi_filter.html">Individual column searching (text inputs)</a></li>
							<li><a href="../api/multi_filter_select.html">Individual column searching (select
							inputs)</a></li>
							<li><a href="../api/highlight.html">Highlighting rows and columns</a></li>
							<li><a href="../api/row_details.html">Child rows (show extra / detailed
							information)</a></li>
							<li><a href="../api/select_row.html">Row selection (multiple rows)</a></li>
							<li><a href="../api/select_single_row.html">Row selection and deletion (single
							row)</a></li>
							<li><a href="../api/form.html">Form inputs</a></li>
							<li><a href="../api/counter_columns.html">Index column</a></li>
							<li><a href="../api/show_hide.html">Show / hide columns dynamically</a></li>
							<li><a href="../api/api_in_init.html">Using API in callbacks</a></li>
							<li><a href="../api/tabs_and_scrolling.html">Scrolling and jQuery UI tabs</a></li>
							<li><a href="../api/regex.html">Search API (regular expressions)</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../ajax/index.html">Ajax</a></h3>
						<ul class="toc">
							<li><a href="../ajax/simple.html">Ajax data source (arrays)</a></li>
							<li><a href="../ajax/objects.html">Ajax data source (objects)</a></li>
							<li><a href="../ajax/deep.html">Nested object data (objects)</a></li>
							<li><a href="../ajax/objects_subarrays.html">Nested object data (arrays)</a></li>
							<li><a href="../ajax/orthogonal-data.html">Orthogonal data</a></li>
							<li><a href="../ajax/null_data_source.html">Generated content for a column</a></li>
							<li><a href="../ajax/custom_data_property.html">Custom data source property</a></li>
							<li><a href="../ajax/custom_data_flat.html">Flat array data source</a></li>
							<li><a href="../ajax/defer_render.html">Deferred rendering for speed</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../server_side/index.html">Server-side</a></h3>
						<ul class="toc">
							<li><a href="../server_side/simple.html">Server-side processing</a></li>
							<li><a href="../server_side/custom_vars.html">Custom HTTP variables</a></li>
							<li><a href="../server_side/post.html">POST data</a></li>
							<li><a href="../server_side/ids.html">Automatic addition of row ID attributes</a></li>
							<li><a href="../server_side/object_data.html">Object data source</a></li>
							<li><a href="../server_side/row_details.html">Row details</a></li>
							<li><a href="../server_side/select_rows.html">Row selection</a></li>
							<li><a href="../server_side/jsonp.html">JSONP data source for remote domains</a></li>
							<li><a href="../server_side/defer_loading.html">Deferred loading of data</a></li>
							<li><a href="../server_side/pipeline.html">Pipelining data to reduce Ajax calls for
							paging</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../plug-ins/index.html">Plug-ins</a></h3>
						<ul class="toc">
							<li><a href="../plug-ins/api.html">API plug-in methods</a></li>
							<li><a href="../plug-ins/sorting_auto.html">Ordering plug-ins (with type
							detection)</a></li>
							<li><a href="../plug-ins/sorting_manual.html">Ordering plug-ins (no type
							detection)</a></li>
							<li><a href="../plug-ins/range_filtering.html">Custom filtering - range search</a></li>
							<li><a href="../plug-ins/dom_sort.html">Live DOM ordering</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>