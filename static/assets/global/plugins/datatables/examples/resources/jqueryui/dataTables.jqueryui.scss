

 //
 // Colour customisation
 //

// Border between the header (and footer) and the table body
$table-header-border: 1px solid #111;

// Border of rows / cells
$table-body-border: 1px solid #ddd;

// Row background colour (hover, striping etc are all based on this colour and
// calculated automatically)
$table-row-background: #ffffff;

// Row colour, when selected (tr.selected)
$table-row-selected: #B0BED9;

// Text colour of the interaction control elements (info, filter, paging etc)
$table-control-color: #333;

// Highlight colour of the paging button for the current page
$table-paging-button-active: #dcdcdc;

// Hover colour of paging buttons on mouse over
$table-paging-button-hover: #111;



//
// Functions / mixins
//
@function tint( $color, $percent ) {
  @return mix(white, $color, $percent);
}

@function shade( $color, $percent ) {
  @return mix(black, $color, $percent);
}

@mixin gradient( $from, $to ) {
  background-color: $from;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,$from), color-stop(100%,$to)); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top,     $from 0%, $to 100%); /* Chrome10+,Safari5.1+ */
  background:    -moz-linear-gradient(top,     $from 0%, $to 100%); /* FF3.6+ */
  background:     -ms-linear-gradient(top,     $from 0%, $to 100%); /* IE10+ */
  background:      -o-linear-gradient(top,     $from 0%, $to 100%); /* Opera 11.10+ */
  background:         linear-gradient(to bottom, $from 0%, $to 100%); /* W3C */
}


/*
 * Table styles
 */
table.dataTable {
  width: 100%;
  margin: 0 auto;
  clear: both;
  border-collapse: separate;
  border-spacing: 0;

  /*
   * Header and footer styles
   */
  thead,
  tfoot {
    th,
    td {
      padding: 4px 10px;
    }

    th {
      font-weight: bold;
    }
  }

  thead th,
  thead td {
    &:active {
      outline: none;
    }
  }

  // Sorting
  thead {
    .sorting_asc,
    .sorting_desc,
    .sorting {
      cursor: pointer;
      *cursor: hand;
    }

    th div.DataTables_sort_wrapper {
      position: relative;
      padding-right: 10px;

      span {
        position: absolute;
        top: 50%;
        margin-top: -8px;
        right: -5px;
      }
    }

    th.ui-state-default {
      border-right-width: 0;

      &:last-child {
        border-right-width: 1px;
      }
    }
  }


  /*
   * Body styles
   */
  tbody {
    tr {
      background-color: $table-row-background;

      &.selected {
        background-color: $table-row-selected;
      }
    }

    th,
    td {
      padding: 8px 10px;
    }
  }

  th.center,
  td.center,
  td.dataTables_empty {
    text-align: center;
  }

  th.right,
  td.right {
    text-align: right;
  }


  // Stripe classes - add "row-border" class to the table to activate
  &.row-border tbody,
  &.display tbody {
    th, td {
      border-top: $table-body-border;
    }

    tr:first-child th,
    tr:first-child td {
      border-top: none;
    }
  }


  // Stripe classes - add "cell-border" class to the table to activate
  &.cell-border tbody {
    th, td {
      border-top: $table-body-border;
      border-right: $table-body-border;
    }

    tr th:first-child,
    tr td:first-child {
      border-left: $table-body-border;
    }

    tr:first-child th,
    tr:first-child td {
      border-top: none;
    }
  }


  // Stripe classes - add "stripe" class to the table to activate
  &.stripe tbody,
  &.display tbody {
    tr.odd {
      background-color: shade($table-row-background, 2.35%); // shade by f9

      &.selected {
        background-color: shade($table-row-selected, 2.35%);
      }
    }
  }


  // Hover classes - add "hover" class to the table to activate
  &.hover tbody,
  &.display tbody {
    tr:hover,
    tr.odd:hover,
    tr.even:hover {
      background-color: shade($table-row-background, 3.6%); // shade by f5

      &.selected {
        background-color: shade($table-row-selected, 3.6%);
      }
    }
  }


  // Sort column highlighting - add "hover" class to the table to activate
  &.order-column,
  &.display {
    tbody {
      tr>.sorting_1,
      tr>.sorting_2,
      tr>.sorting_3 {
        background-color: shade($table-row-background, 2%); // shade by fa
      }

      tr.selected>.sorting_1,
      tr.selected>.sorting_2,
      tr.selected>.sorting_3 {
        background-color: shade($table-row-selected, 2%);
      }
    }
  }

  &.display tbody,
  &.order-column.stripe tbody {
    tr.odd {
      >.sorting_1 { background-color: shade($table-row-background, 5.4%); } // shade by f1
      >.sorting_2 { background-color: shade($table-row-background, 4.7%); } // shade by f3
      >.sorting_3 { background-color: shade($table-row-background, 3.9%); } // shade by f5

      &.selected {
        >.sorting_1 { background-color: shade($table-row-selected, 5.4%); }
        >.sorting_2 { background-color: shade($table-row-selected, 4.7%); }
        >.sorting_3 { background-color: shade($table-row-selected, 3.9%); }
      }
    }

    tr.even {
      >.sorting_1 { background-color: shade($table-row-background, 2%); } // shade by fa
      >.sorting_2 { background-color: shade($table-row-background, 1.2%); } // shade by fc
      >.sorting_3 { background-color: shade($table-row-background, 0.4%); } // shade by fe
      
      &.selected {
        >.sorting_1 { background-color: shade($table-row-selected, 2%); }
        >.sorting_2 { background-color: shade($table-row-selected, 1.2%); }
        >.sorting_3 { background-color: shade($table-row-selected, 0.4%); }
      }
    }
  }

  &.display tbody,
  &.order-column.hover tbody {
    tr:hover,
    tr.odd:hover,
    tr.even:hover {
      >.sorting_1 { background-color: shade($table-row-background, 8.2%); } // shade by ea
      >.sorting_2 { background-color: shade($table-row-background, 7.5%); } // shade by ec
      >.sorting_3 { background-color: shade($table-row-background, 6.3%); } // shade by ef

      &.selected {
        >.sorting_1 { background-color: shade($table-row-selected, 8.2%); }
        >.sorting_2 { background-color: shade($table-row-selected, 7.5%); }
        >.sorting_3 { background-color: shade($table-row-selected, 6.3%); }
      }
    }
  }
}

// Its not uncommon to use * {border-box} now, but it messes up the column width
// calculations, so use content-box for the table and cells
table.dataTable,
table.dataTable th,
table.dataTable td {
  -webkit-box-sizing: content-box;
     -moz-box-sizing: content-box;
          box-sizing: content-box;
}



/*
 * Control feature layout
 */
.dataTables_wrapper {
  position: relative;
  clear: both;
  *zoom: 1;

  // Page length options
  .dataTables_length {
    float: left;
  }

  // Filtering input
  .dataTables_filter {
    float: right;
    text-align: right;

    input {
      margin-left: 0.5em;
    }
  }

  // Table info
  .dataTables_info {
    clear: both;
    float: left;
    padding-top: 0.55em;
  }

  // Paging
  .dataTables_paginate {
    float: right;
    text-align: right;

    .fg-button {
      box-sizing: border-box;
      display: inline-block;
      min-width: 1.5em;
      padding: 0.5em;
      margin-left: 2px;
      text-align: center;
      text-decoration: none !important;
      cursor: pointer;
      *cursor: hand;

      color: $table-control-color !important;
      border: 1px solid transparent;

      &:active {
        outline: none;
      }
    }

    .fg-button:first-child {
      border-top-left-radius: 3px;
      border-bottom-left-radius: 3px;
    }

    .fg-button:last-child {
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
    }
  }

  // Processing
  .dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 40px;
    margin-left: -50%;
    margin-top: -25px;
    padding-top: 20px;

    text-align: center;
    font-size: 1.2em;

    background-color: white;
    background: -webkit-gradient(linear, left top, right top, color-stop(0%,rgba($table-row-background, 0)), color-stop(25%,rgba($table-row-background, 0.9)), color-stop(75%,rgba($table-row-background, 0.9)), color-stop(100%,rgba(255,255,255,0))); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(left,     rgba($table-row-background, 0) 0%, rgba($table-row-background, 0.9) 25%, rgba($table-row-background, 0.9) 75%, rgba($table-row-background, 0) 100%); /* Chrome10+,Safari5.1+ */
    background:    -moz-linear-gradient(left,     rgba($table-row-background, 0) 0%, rgba($table-row-background, 0.9) 25%, rgba($table-row-background, 0.9) 75%, rgba($table-row-background, 0) 100%); /* FF3.6+ */
    background:     -ms-linear-gradient(left,     rgba($table-row-background, 0) 0%, rgba($table-row-background, 0.9) 25%, rgba($table-row-background, 0.9) 75%, rgba($table-row-background, 0) 100%); /* IE10+ */
    background:      -o-linear-gradient(left,     rgba($table-row-background, 0) 0%, rgba($table-row-background, 0.9) 25%, rgba($table-row-background, 0.9) 75%, rgba($table-row-background, 0) 100%); /* Opera 11.10+ */
    background:         linear-gradient(to right, rgba($table-row-background, 0) 0%, rgba($table-row-background, 0.9) 25%, rgba($table-row-background, 0.9) 75%, rgba($table-row-background, 0) 100%); /* W3C */
  }

  .dataTables_length,
  .dataTables_filter,
  .dataTables_info,
  .dataTables_processing,
  .dataTables_paginate {
    color: $table-control-color;
  }

  // Scrolling
  .dataTables_scroll {
    clear: both;
  }

  .dataTables_scrollBody {
    *margin-top: -1px;
    -webkit-overflow-scrolling: touch;
  }


  .ui-widget-header {
    font-weight: normal;
  }

  .ui-toolbar {
    padding: 8px;
  }

  // Self clear the wrapper
  &:after {
    visibility: hidden;
    display: block;
    content: "";
    clear: both;
    height: 0;
  }
  zoom: 1; // Poor old IE
}


