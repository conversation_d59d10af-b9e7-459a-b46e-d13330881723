/*!
 Responsive 1.0.1
 2014 SpryMedia Ltd - datatables.net/license
*/
(function(m,o){var k=function(c,k){var h=function(e,a){if(!k.versionCheck||!k.versionCheck("1.10.1"))throw"DataTables Responsive requires DataTables 1.10.1 or newer";e.responsive||(this.s={dt:new k.Api(e),columns:[]},a&&"string"===typeof a.details&&(a.details={type:a.details}),this.c=c.extend(!0,{},h.defaults,a),e.responsive=this,this._constructor())};h.prototype={_constructor:function(){var e=this,a=this.s.dt;a.settings()[0]._responsive=this;c(m).on("resize.dtr orientationchange.dtr",a.settings()[0].oApi._fnThrottle(function(){e._resize()}));
a.on("destroy.dtr",function(){c(m).off("resize.dtr orientationchange.dtr")});this.c.breakpoints.sort(function(a,b){return a.width<b.width?1:a.width>b.width?-1:0});this._classLogic();this._resizeAuto();this._resize();var b=this.c.details;b.type&&(e._detailsInit(),this._detailsVis(),a.on("column-visibility.dtr",function(){e._detailsVis()}),c(a.table().node()).addClass("dtr-"+b.type))},_columnsVisiblity:function(e){var a=this.s.dt,b=this.s.columns,d,f,j=c.map(b,function(a){return a.auto&&null===a.minWidth?
!1:!0===a.auto?"-":-1!==a.includeIn.indexOf(e)}),g=0;d=0;for(f=j.length;d<f;d++)!0===j[d]&&(g+=b[d].minWidth);a=a.table().container().offsetWidth-g;d=0;for(f=j.length;d<f;d++)b[d].control?a-=b[d].minWidth:"-"===j[d]&&(j[d]=0>a-b[d].minWidth?!1:!0,a-=b[d].minWidth);a=!1;d=0;for(f=b.length;d<f;d++)if(!b[d].control&&!j[d]){a=!0;break}d=0;for(f=b.length;d<f;d++)b[d].control&&(j[d]=a);return j},_classLogic:function(){var e=this,a=this.c.breakpoints,b=this.s.dt.columns().eq(0).map(function(a){return{className:this.column(a).header().className,
includeIn:[],auto:!1,control:!1}}),d=function(a,d){var e=b[a].includeIn;-1===e.indexOf(d)&&e.push(d)},f=function(f,g,c,i){if(c)if("max-"===c){i=e._find(g).width;g=0;for(c=a.length;g<c;g++)a[g].width<=i&&d(f,a[g].name)}else if("min-"===c){i=e._find(g).width;g=0;for(c=a.length;g<c;g++)a[g].width>=i&&d(f,a[g].name)}else{if("not-"===c){g=0;for(c=a.length;g<c;g++)-1===a[g].name.indexOf(i)&&d(f,a[g].name)}}else b[f].includeIn.push(g)};b.each(function(b,d){for(var e=b.className.split(" "),i=!1,h=0,k=e.length;h<
k;h++){var l=c.trim(e[h]);if("all"===l){i=!0;b.includeIn=c.map(a,function(a){return a.name});return}if("none"===l){i=!0;return}if("control"===l){i=!0;b.control=!0;return}c.each(a,function(a,b){var e=b.name.split("-"),c=l.match(RegExp("(min\\-|max\\-|not\\-)?("+e[0]+")(\\-[_a-zA-Z0-9])?"));c&&(i=!0,c[2]===e[0]&&c[3]==="-"+e[1]?f(d,b.name,c[1],c[2]+c[3]):c[2]===e[0]&&!c[3]&&f(d,b.name,c[1],c[2]))})}i||(b.auto=!0)});this.s.columns=b},_detailsInit:function(){var e=this,a=this.s.dt,b=this.c.details;"inline"===
b.type&&(b.target="td:first-child");var d=b.target;c(a.table().body()).on("click","string"===typeof d?d:"td",function(){if(c(a.table().node()).hasClass("collapsed")){if(typeof d==="number"){var b=d<0?a.columns().eq(0).length+d:d;if(a.cell(this).index().column!==b)return}b=a.row(c(this).closest("tr"));if(b.child.isShown()){b.child(false);c(b.node()).removeClass("parent")}else{var j=e.c.details.renderer(a,b[0]);b.child(j,"child").show();c(b.node()).addClass("parent")}}})},_detailsVis:function(){var e=
this,a=this.s.dt,b=a.columns(":hidden").indexes().flatten(),d=!0;if(0===b.length||1===b.length&&this.s.columns[b[0]].control)d=!1;d?(c(a.table().node()).addClass("collapsed"),a.rows().eq(0).each(function(b){b=a.row(b);if(b.child()){var c=e.c.details.renderer(a,b[0]);!1===c?b.child.hide():b.child(c,"child").show()}})):(c(a.table().node()).removeClass("collapsed"),a.rows().eq(0).each(function(b){a.row(b).child.hide()}))},_find:function(c){for(var a=this.c.breakpoints,b=0,d=a.length;b<d;b++)if(a[b].name===
c)return a[b]},_resize:function(){for(var e=this.s.dt,a=c(m).width(),b=this.c.breakpoints,d=b[0].name,f=b.length-1;0<=f;f--)if(a<=b[f].width){d=b[f].name;break}var h=this._columnsVisiblity(d);e.columns().eq(0).each(function(a,b){e.column(a).visible(h[b])})},_resizeAuto:function(){var e=this.s.dt,a=this.s.columns;if(this.c.auto&&-1!==c.inArray(!0,c.map(a,function(a){return a.auto}))){e.table().node();var b=e.table().node().cloneNode(!1),d=c(e.table().header().cloneNode(!1)).appendTo(b),f=c(e.table().body().cloneNode(!1)).appendTo(b);
e.rows({page:"current"}).indexes().each(function(a){var b=e.row(a).node().cloneNode(!0);e.columns(":hidden").flatten().length&&c(b).append(e.cells(a,":hidden").nodes().to$().clone());c(b).appendTo(f)});var h=e.columns().header().to$().clone(!1).wrapAll("tr").appendTo(d),b=c("<div/>").css({width:1,height:1,overflow:"hidden"}).append(b).insertBefore(e.table().node());e.columns().eq(0).each(function(b){a[b].minWidth=h[b].offsetWidth||0});b.remove()}}};h.breakpoints=[{name:"desktop",width:Infinity},{name:"tablet-l",
width:1024},{name:"tablet-p",width:768},{name:"mobile-l",width:480},{name:"mobile-p",width:320}];h.defaults={breakpoints:h.breakpoints,auto:!0,details:{renderer:function(e,a){var b=e.cells(a,":hidden").eq(0).map(function(a){var b=c(e.column(a.column).header());return b.hasClass("control")?"":'<li><span class="dtr-title">'+b.text()+':</span> <span class="dtr-data">'+e.cell(a).data()+"</span></li>"}).toArray().join("");return b?c("<ul/>").append(b):!1},target:0,type:"inline"}};var n=c.fn.dataTable.Api;
n.register("responsive()",function(){return this});n.register("responsive.recalc()",function(){this.iterator("table",function(c){c._responsive&&(c._responsive._resizeAuto(),c._responsive._resize())})});h.version="1.0.1";c.fn.dataTable.Responsive=h;c.fn.DataTable.Responsive=h;c(o).on("init.dt.dtr",function(e,a){if(c(a.nTable).hasClass("responsive")||c(a.nTable).hasClass("dt-responsive")||a.oInit.responsive){var b=a.oInit.responsive;!1!==b&&new h(a,c.isPlainObject(b)?b:{})}});return h};"function"===
typeof define&&define.amd?define(["jquery","datatables"],k):"object"===typeof exports?k(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.Responsive&&k(jQuery,jQuery.fn.dataTable)})(window,document);
