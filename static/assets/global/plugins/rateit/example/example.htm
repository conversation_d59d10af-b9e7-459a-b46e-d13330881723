<!DOCTYPE html>
<html>
<head>
    <title>RateIt</title>
    <link href="../src/rateit.css" rel="stylesheet" type="text/css">

    <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js" type="text/javascript"></script>

    <style>
        body
        {
            font-family: Tahoma;
            font-size: 12px;
        }
        h1
        {
            font-size: 1.7em;
        }
        h2
        {
            font-size: 1.5em;
        }
        h3
        {
            font-size: 1.2em;
        }
        ul.nostyle
        {
            list-style: none;
        }
        ul.nostyle h3
        {
            margin-left: -20px;
        }
    </style>
    <!-- alternative styles -->
    <link href="content/bigstars.css" rel="stylesheet" type="text/css">
    <link href="content/antenna.css" rel="stylesheet" type="text/css">
    <!-- syntax highlighter -->
    <link href="sh/shCore.css" rel="stylesheet" type="text/css">
    <link href="sh/shCoreDefault.css" rel="stylesheet" type="text/css">
</head>
<body>
    <h1>
        RateIt plugin</h1>
    <h2>
        Prerequisites</h2>
    <p>
        In order to use RateIt you'll need:
        <ul>
            <li><a target="_blank" href="http://www.jquery.com">jQuery 1.6.0</a> or newer.</li>
            <li>jquery.rateit.min.js, rateit.css, delete.gif, star.gif - <a href="http://rateit.codeplex.com/releases">
                Download RateIt</a></li>
            <li>Place a reference to the rateit.css stylesheet into the page head.</li>
            <li>Place a script reference to jquery.rateit.js before your &lt;/body&gt; tag.
            </li>
        </ul>
    </p>
    <h2>
        Options</h2>
    Go to the <a href="http://rateit.codeplex.com">RateIt project homepage</a> to view
    the documentation.
    <h2>
        Examples</h2>
    <div style="font-weight: bold">
        <span id="toc"></span>
    </div>
    <ul class="nostyle" id="examples">
        <li id="ex_a1">
            <h3>
                1a) Basic usage</h3>
            <div class="rateit">
            </div>
            <pre class="brush: xml">
            &lt;div class="rateit">
            &lt;/div>
            </pre>
        </li>
          <li id="ex_1b">
            <h3>
                1a) Basic usage (inline element)</h3>
            <span class="rateit">
            </span>
            <pre class="brush: xml">
            &lt;span class="rateit">
            &lt;/span>
            </pre>
        </li>
        <li id="ex_2a">
            <h3>
                2a) Progressive enhancement (using HTML5 range input type)
            </h3>
            <input type="range" min="0" max="7" value="0" step="0.5" id="backing2">
            <div class="rateit" data-rateit-backingfld="#backing2">
            </div>
            <pre class="brush: xml">
            &lt;input type="range" min="0" max="7" value="0" step="0.5" id="backing2">
            &lt;div class="rateit" data-rateit-backingfld="#backing2">&lt;/div>
            </pre>
            <button onclick="$('#backing2').toggle()">
                Toggle Backing Field visibility</button>
        </li>
        <li id="ex_2b">
            <h3>
                2b) Progressive enhancement (using select)
            </h3>
            <select id="backing2b">
                <option value="0">Bad</option>
                <option value="1">OK</option>
                <option value="2">Great</option>
                <option value="3">Excellent</option>
            </select>
            <div class="rateit" data-rateit-backingfld="#backing2b">
            </div>
            <pre class="brush: xml">
            &lt;select id="backing2b">
                &lt;option value="0">Bad&lt;/option>
                &lt;option value="1">OK&lt;/option>
                &lt;option value="2">Great&lt;/option>
                &lt;option value="3">Excellent&lt;/option>
            &lt;/select>
            &lt;div class="rateit" data-rateit-backingfld="#backing2b">&lt;/div>
            </pre>
        </li>
        <li id="ex_3">
            <h3>
                3) Readonly and preset value</h3>
            <div class="rateit" data-rateit-value="2.5" data-rateit-ispreset="true" data-rateit-readonly="true">
            </div>
            <pre class="brush: xml">
                &lt;div class="rateit" data-rateit-value="2.5" data-rateit-ispreset="true" data-rateit-readonly="true">&lt;/div>
            </pre>
        </li>
        <li id="ex_4">
            <h3>
                4) More settings</h3>
            <input type="range" value="4" step="0.25" id="backing4">
            <div class="rateit" data-rateit-backingfld="#backing4" data-rateit-resetable="false" data-rateit-ispreset="true"
                data-rateit-min="0" data-rateit-max="10">
            </div>
            <pre class="brush: xml">
            &lt;input type="range" value="4" step="0.25" id="backing4">
            &lt;div class="rateit" data-rateit-backingfld="#backing4" data-rateit-resetable="false"  data-rateit-ispreset="true"
                data-rateit-min="0" data-rateit-max="10">
            &lt;/div>

            </pre>
        </li>
        <li id="ex_5">
            <h3>
                5) Javascript interaction</h3>
            <div class="rateit" id="rateit5" data-rateit-min="2">
            </div>
            <div>
                <span id="value5"></span><span id="hover5"></span>
            </div>

            <script type="text/javascript">
                $("#rateit5").bind('rated', function (event, value) { $('#value5').text('You\'ve rated it: ' + value); });
                $("#rateit5").bind('reset', function () { $('#value5').text('Rating reset'); });
                $("#rateit5").bind('over', function (event, value) { $('#hover5').text('Hovering over: ' + value); });
            </script>

            <pre class="brush: xml">
            &lt;div class="rateit" id="rateit5" data-rateit-min="2">
            &lt;/div>
            &lt;div>
                &lt;span id="value5">&lt;/span>
                &lt;span id="hover5">&lt;/span>
            &lt;/div>

            &lt;script type="text/javascript">
                $("#rateit5").bind('rated', function (event, value) { $('#value5').text('You\'ve rated it: ' + value); });
                $("#rateit5").bind('reset', function () { $('#value5').text('Rating reset'); });
                $("#rateit5").bind('over', function (event, value) { $('#hover5').text('Hovering over: ' + value); });
            &lt;/script>            
            </pre>
        </li>
        <li id="ex_6">
            <h3>
                6) Javascript invocation</h3>
            <input type="hidden" id="backing6">
            <div id="rateit6">
            </div>

            <script type="text/javascript">
                $(function () { $('#rateit6').rateit({ max: 20, step: 2, backingfld: '#backing6' }); });
            </script>

            <pre class="brush: xml">
            &lt;input type="hidden" id="backing6">
            &lt;div id="rateit6">
            &lt;/div>

            &lt;script type="text/javascript">
                $(function () { $('#rateit6').rateit({ max: 20, step: 2, backingfld: '#backing6' }); });
            &lt;/script>
            </pre>
        </li>
        <li id="ex_7a">
            <h3>
                7a) Styling</h3>
            <p>
                You can change the styles of the plugin in a two ways.
                <ul>
                    <li>You can change change the star.gif and delete.gif pictures (and perhaps the default
                        starwidth, and starheight values in the javascript file). This will change the style
                        everywhere.</li>
                    <li>You can change individual items, by adding a stylesheet with a few rules.</li>
                </ul>
                Here I've opted for the second option.
            </p>
            <div class="rateit bigstars" data-rateit-starwidth="32" data-rateit-starheight="32">
            </div>
            <h4>
                Inline code</h4>
            <pre class="brush: xml">
            &lt;div class="rateit bigstars" data-rateit-starwidth="32" data-rateit-starheight="32">&lt;/div>
            </pre>
            <h4>
                Style sheet</h4>
            <pre class="brush: css">
            div.bigstars div.rateit-range
            {
                background: url(star-white32.png);
                height: 32px;
            }

            div.bigstars div.rateit-hover
            {
                background: url(star-gold32.png);
            }

            div.bigstars div.rateit-selected
            {
                background: url(star-red32.png);
            }

            div.bigstars div.rateit-reset
            {
                background: url(star-black32.png);
                width: 32px;
                height: 32px;
            }

            div.bigstars div.rateit-reset:hover
            {
                background: url(star-white32.png);
            }
            </pre>
        </li>
        <li id="ex_7b">
            <h3>
                7b) More styling</h3>
            <p>
                Here we use one big image (actually 3), with different dimensions. Not very pretty,
                but it conveys the message I hope.</p>
            <div class="rateit antenna" data-rateit-starwidth="11" data-rateit-starheight="25">
            </div>
            <h4>
                Inline code</h4>
            <pre class="brush: xml">
&lt;div class="rateit antenna" data-rateit-starwidth="11" data-rateit-starheight="25">&lt;/div>            
            </pre>
            <h4>
                Style sheet</h4>
            <pre class="brush: css">
div.antenna div.rateit-range
{
    background: url(antenna-black.png) no-repeat;
    height: 25px;
}

div.antenna div.rateit-hover
{
    background: url(antenna-yellow.png) no-repeat;
}

div.antenna div.rateit-selected
{
    background: url(antenna-red.png) no-repeat;
}            
            </pre>
        </li>
        <li id="ex_8">
            <h3>
                8) RTL support</h3>
            <div style="direction: rtl">
                Now we are in RTL mode. No changes needed to make this work.
                <div class="rateit">
                </div>
            </div>
        </li>
        <li id="ex_9">
            <h3>
                9) Setting and Getting values</h3>
            <p>
                All properties can also be set on the fly. Here are a few examples:</p>
            <div class="rateit" id="rateit9">
            </div>
            <div>
                <button onclick="alert($('#rateit9').rateit('value'))">
                    Get value</button>
                <button onclick="$('#rateit9').rateit('value', prompt('Input numerical value'))">
                    Set value</button>
            </div>
            <div>
                <button onclick="alert($('#rateit9').rateit('max'))">
                    Get max value</button>
                <button onclick="$('#rateit9').rateit('max', prompt('Input numerical value'))">
                    Set max value</button>
            </div>
             <div>
                <button onclick="alert($('#rateit9').rateit('min'))">
                    Get min value</button>
                <button onclick="$('#rateit9').rateit('min', prompt('Input numerical value'))">
                    Set min value</button>
            </div>
            <div>
                <button onclick="alert($('#rateit9').rateit('step'))">
                    Get step size</button>
                <button onclick="$('#rateit9').rateit('step', prompt('Input numerical value'))">
                    Set step size</button>
            </div>
            <div>
                <button onclick="alert($('#rateit9').rateit('readonly'))">
                    Get readonly value</button>
                <button onclick="$('#rateit9').rateit('readonly',!$('#rateit9').rateit('readonly'))">
                    Toggle readonly</button>
            </div>
            <div>
                <button onclick="alert($('#rateit9').rateit('ispreset'))">
                    Get ispreset value</button>
                <button onclick="$('#rateit9').rateit('ispreset',!$('#rateit9').rateit('ispreset'))">
                    Toggle ispreset</button>
            </div>
          <div>
            <button onclick="$('#rateit9').rateit('reset')">
              Reset
            </button>
          </div>


            <pre class="brush: xml">
	            &lt;div class="rateit" id="rateit9">
                &lt;/div>
                &lt;div>
                    &lt;button onclick="alert($('#rateit9').rateit('value'))">Get value&lt;/button>
                    &lt;button onclick="$('#rateit9').rateit('value', prompt('Input numerical value'))">Set value&lt;/button>
                &lt;/div>
                &lt;div>
                    &lt;button onclick="alert($('#rateit9').rateit('max'))">Get max value&lt;/button>
                    &lt;button onclick="$('#rateit9').rateit('max', prompt('Input numerical value'))">Set max value&lt;/button>
                &lt;/div>
                &lt;div>
                    &lt;button onclick="alert($('#rateit9').rateit('step'))">Get step size&lt;/button>
                    &lt;button onclick="$('#rateit9').rateit('step', prompt('Input numerical value'))">Set step size&lt;/button>
                &lt;/div>
                &lt;div>
                    &lt;button onclick="alert($('#rateit9').rateit('readonly'))">Get readonly value&lt;/button>
                    &lt;button onclick="$('#rateit9').rateit('readonly',!$('#rateit9').rateit('readonly'))">Toggle readonly&lt;/button>
                &lt;/div>   
                &lt;div>
                    &lt;button onclick="alert($('#rateit9').rateit('ispreset'))">Get ispreset value&lt;/button>
                    &lt;button onclick="$('#rateit9').rateit('ispreset',!$('#rateit9').rateit('ispreset'))">Toggle ispreset&lt;/button>
                &lt;/div>   
                &lt;div>
                    &lt;button onclick="$('#rateit9').rateit('reset')">Reset&lt;/button>
                &lt;/div>                           
            </pre>
        </li>
        <li id="ex_10">
            <h3>
                10) Tooltip example</h3>
            <p>
                Using tooltips is easy. Just bind to the hover event, and do your thing. Shown here
                is the basic tooltip, but of course you can use any tooltip you'd like.</p>
            <h4>
                Basic numbers</h4>
            <div class="rateit" id="rateit10">
            </div>

            <script type="text/javascript">
                $("#rateit10").bind('over', function (event, value) { $(this).attr('title', value); });
            </script>

            <pre class="brush: xml">
            &lt;div class="rateit" id="rateit10">
            &lt;/div>

            &lt;script type="text/javascript">
                $("#rateit10").bind('over', function (event,value) { $(this).attr('title', value); });
            &lt;/script>       
            </pre>
            <h4>
                Custom text</h4>
            <div class="rateit" id="rateit10b" data-rateit-step="1">
            </div>

            <script type="text/javascript">
                var tooltipvalues = ['bad', 'poor', 'ok', 'good', 'super'];
                $("#rateit10b").bind('over', function (event, value) { $(this).attr('title', tooltipvalues[value - 1]); });
            </script>

            <pre class="brush: xml">
            &lt;div class="rateit" id="rateit10b" data-rateit-step="1">
            &lt;/div>

            &lt;script type="text/javascript">
                var tooltipvalues = ['bad', 'poor', 'ok', 'good', 'super'];
                $("#rateit10b").bind('over', function (event, value) { $(this).attr('title', tooltipvalues[value-1]); });
            &lt;/script>
            </pre>
        </li>
        <li id="ex_11">
            <h3>
                11) AJAX example</h3>
            <p>
                Most times RateIt will be used using some Ajax. There are different ways of implementing
                it (with/without backing field, using data-* attributes etc.)</p>
            <div id="products">
                <div style="float: right; width: 350px; border: 1px solid #ccc; padding: 1em;">
                    <strong>Server response:</strong>
                    <ul id="response">
                    </ul>
                </div>
                <ul>
                    <li>
                        <h4>
                            Product X (id: 312)</h4>
                        RateIt:
                        <div data-productid="312" class="rateit">
                        </div>
                    </li>
                    <li>
                        <h4>
                            Product Y (id: 423)</h4>
                        RateIt:
                        <div data-productid="423" class="rateit">
                        </div>
                    </li>
                    <li>
                        <h4>
                            Product Z (id: 653)</h4>
                        RateIt:
                        <div data-productid="653" class="rateit">
                        </div>
                    </li>
                </ul>
            </div>

            <script type="text/javascript">
                //we bind only to the rateit controls within the products div
                $('#products .rateit').bind('rated reset', function (e) {
                    var ri = $(this);

                    //if the use pressed reset, it will get value: 0 (to be compatible with the HTML range control), we could check if e.type == 'reset', and then set the value to  null .
                    var value = ri.rateit('value');
                    var productID = ri.data('productid'); // if the product id was in some hidden field: ri.closest('li').find('input[name="productid"]').val()

                    //maybe we want to disable voting?
                    ri.rateit('readonly', true);

                    $.ajax({
                        url: 'rateit.aspx', //your server side script
                        data: { id: productID, value: value }, //our data
                        type: 'POST',
                        success: function (data) {
                            $('#response').append('<li>' + data + '</li>');

                        },
                        error: function (jxhr, msg, err) {
                            $('#response').append('<li style="color:red">' + msg + '</li>');
                        }
                    });
                });
            </script>

            <h4>
                Client</h4>
            <pre class="brush: xml">
           &lt;div id="products">
              &lt;div style="float:right; width:350px; border:1px solid #ccc; padding:1em;">
                &lt;strong>Server response:&lt;/strong>
                &lt;ul id="response">
                
                &lt;/ul>
                &lt;/div>
                &lt;ul>
                    &lt;li>&lt;h4>Product X (id: 312)&lt;/h4>
                    RateIt: &lt;div data-productid="312" class="rateit">&lt;/div>
                    &lt;/li>
                    &lt;li>&lt;h4>Product Y (id: 423)&lt;/h4>
                    RateIt: &lt;div data-productid="423" class="rateit">&lt;/div>&lt;/li>
                    &lt;li>&lt;h4>Product Z (id: 653)&lt;/h4>
                    RateIt: &lt;div data-productid="653" class="rateit">&lt;/div>
                    &lt;/li>
                &lt;/ul>
              
            &lt;/div>

            &lt;script type ="text/javascript">
                //we bind only to the rateit controls within the products div
                $('#products .rateit').bind('rated reset', function (e) {
                    var ri = $(this);

                    //if the use pressed reset, it will get value: 0 (to be compatible with the HTML range control), we could check if e.type == 'reset', and then set the value to  null .
                    var value = ri.rateit('value');
                    var productID = ri.data('productid'); // if the product id was in some hidden field: ri.closest('li').find('input[name="productid"]').val()

                    //maybe we want to disable voting?
                    ri.rateit('readonly', true);

                    $.ajax({
                        url: 'rateit.aspx', //your server side script
                        data: { id: productID, value: value }, //our data
                        type: 'POST',
                        success: function (data) {
                            $('#response').append('&lt;li>' + data + '&lt;/li>');

                        },
                        error: function (jxhr, msg, err) {
                            $('#response').append('&lt;li style="color:red">' + msg + '&lt;/li>');
                        }
                    });
                });
            &lt;/script>

             </pre>
            <h4>
                Server (ASPX) super simplified</h4>
            <pre class="brush: c-sharp">
&lt;%@ Page Language="C#" %>
&lt;%
    //Get value
    float value = float.Parse(Request.Form["value"]);
    int productID = int.Parse(Request.Form["id"]);

    Response.Write(string.Format("You voted {0} on product: {1}.&lt;br/>Time on server: {2}", value, productID, DateTime.Now.ToString()));

%>



             </pre>
        </li>

      <li id="ex_12">
        <h3>
          12) Resizing
        </h3>
        <p>
          Sometimes we would like to resize the rating controls (perhaps based on screen size). <br />
          This is as easy as<br />
          a) adding a class to the rateit control, which would change the background image.<br/>
          b) modifying the starwidth and starheigth properties.
          <br /><br />
          The alternative styling is taken from example <a href="#ex_7a">7a) styling</a>.
        </p>

        <div class="rateit" id="rateit12">
        </div>
        <p>Pick a size: <select id="size_12"><option data-class="" data-size="16" selected>normal</option><option data-class="bigstars" data-size="32">big</option></select></p>

        <script type="text/javascript">
          $('#size_12').change(function () {
            var option = $(this.options[this.selectedIndex]);
            $('#rateit12').removeClass('bigstars')
                          .addClass(option.data('class'))
                          .rateit('starwidth', option.data('size'))
                          .rateit('starheight', option.data('size'));

          });
        </script>  
        <pre class="brush: xml">
          &lt;div class=&quot;rateit&quot; id=&quot;rateit12&quot;&gt;&lt;/div&gt;
          &lt;p&gt;Pick a size: 
            &lt;select id=&quot;size_12&quot;&gt;
                &lt;option data-class=&quot;&quot; data-size=&quot;16&quot; selected&gt;normal&lt;/option&gt;
                &lt;option data-class=&quot;bigstars&quot; data-size=&quot;32&quot;&gt;big&lt;/option&gt;
            &lt;/select&gt;
          &lt;/p&gt;

          &lt;script type=&quot;text/javascript&quot;&gt;
            $(&#39;#size_12&#39;).change(function () {
              var option = $(this.options[this.selectedIndex]);
              $(&#39;#rateit12&#39;).removeClass(&#39;bigstars&#39;) //remove old class
                            .addClass(option.data(&#39;class&#39;))  //add new class
                            .rateit(&#39;starwidth&#39;, option.data(&#39;size&#39;)) //change width
                            .rateit(&#39;starheight&#39;, option.data(&#39;size&#39;)); //change height

            });
        &lt;/script&gt;  
</pre>
      </li>
    </ul>

    <script src="../src/jquery.rateit.js" type="text/javascript"></script>

    <script>
        //build toc
        var toc = [];
        $('#examples > li').each(function (i, e) {


            if (i > 0)
                toc.push(', ');
            toc.push('<a href="#');
            toc.push(e.id);
            toc.push('">');
            var title = $(e).find('h3:first').text();
            title = title.substring(title.indexOf(')') + 2);
            toc.push(title);
            toc.push('</a>');

        });

        $('#toc').html(toc.join(''));

    </script>

    <!-- syntax highlighter -->

    <script src="sh/shCore.js" type="text/javascript"></script>

    <script src="sh/shBrushJScript.js" type="text/javascript"></script>

    <script src="sh/shBrushXml.js" type="text/javascript"></script>

    <script src="sh/shBrushCss.js" type="text/javascript"></script>

    <script src="sh/shBrushCSharp.js" type="text/javascript"></script>

    <script type="text/javascript">
        SyntaxHighlighter.all()
    </script>

</body>
</html>
