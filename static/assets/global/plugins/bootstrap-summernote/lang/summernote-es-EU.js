(function ($) {
  $.extend($.summernote.lang, {
    'es-EU': {
      font: {
        name:'Tipografia',
        bold: 'Lodia',
        italic: 'Etz<PERSON>',
        underline: 'Azpimarratua',
        strikethrough: '<PERSON><PERSON><PERSON>',
        clear: 'Estiloa kendu',
        height: 'Lerro altuera',
        size: 'Letren neurria'
      },
      image: {
        image: '<PERSON><PERSON><PERSON>', 
        insert: 'I<PERSON>i bat txertatu', 
        resizeFull: 'Jatorrizko neurrira aldatu', 
        resizeHalf: 'Neurria erdira aldatu', 
        resizeQuarter: 'Neurria laurdenera aldatu',
        floatLeft: 'Ezkerrean kokatu',
        floatRight: 'Eskuinean kokatu', 
        floatNone: 'Kokapenik ez ezarri',
        dragImageHere: 'Irudi bat ezarri hemen', 
        selectFromFiles: 'Zure fitxategi bat aukeratu',
        url: 'Irudiaren URL helbidea' 
      },
      link: {
        link: '<PERSON>ste<PERSON>',
        insert: '<PERSON>ste<PERSON> bat txertatu', 
        unlink: 'Este<PERSON> ezabatu', 
        edit: 'Editatu',
        textToDisplay: '<PERSON>stekaren testua', 
        url: 'Estekaren URL helbidea',
        openInNewWindow: 'Leiho berri batean ireki' 
      },
      video: {
        video: 'Bideoa',
        videoLink: 'Bideorako esteka',
        insert: 'Bideo berri bat txertatu',
        url: 'Bideoaren URL helbidea',
        providers: '(YouTube, Vimeo, Vine, Instagram, edo DailyMotion)' 
      },
      table: {
        table: 'Taula' //Tabla
      },
      hr: {
        insert: 'Marra horizontala txertatu' //Insertar línea horizontal
      },
      style: {
        style: 'Estiloa',
        normal: 'Normal',
        blockquote: 'Aipamena',
        pre: 'Kodea',
        h1: '1. izenburua',
        h2: '2. izenburua',
        h3: '3. izenburua',
        h4: '4. izenburua',
        h5: '5. izenburua',
        h6: '6. izenburua'
      },
      lists: {
        unordered: 'Ordenatu gabeko zerrenda',
        ordered: 'Zerrenda ordenatua'
      },
      options: {
        help: 'Laguntza',
        fullscreen: 'Pantaila osoa',
        codeview: 'Kodea ikusi'
      },
      paragraph: {
        paragraph: 'Paragrafoa',
        outdent: 'Koska txikiagoa',
        indent: 'Koska handiagoa',
        left: 'Ezkerrean kokatu',
        center: 'Erdian kokatu',
        right: 'Eskuinean kokatu',
        justify: 'Justifikatu'
      },
      color: {
        recent: 'Azken kolorea',
        more: 'Kolore gehiago',
        background: 'Atzeko planoa',
        foreground: 'Aurreko planoa',
        transparent: 'Gardena',
        setTransparent: 'Gardendu',
        reset: 'Lehengoratu',
        resetToDefault: 'Berrezarri lehenetsia'
      },
      shortcut: {
        shortcuts: 'Lasterbideak',
        close: 'Itxi',
        textFormatting: 'Testuaren formatua',
        action: 'Ekintza',
        paragraphFormatting: 'Paragrafoaren formatua',
        documentStyle: 'Dokumentuaren estiloa'
      },
      history: {
        undo: 'Desegin',
        redo: 'Berregin'
      }
    }
  });
})(jQuery);
