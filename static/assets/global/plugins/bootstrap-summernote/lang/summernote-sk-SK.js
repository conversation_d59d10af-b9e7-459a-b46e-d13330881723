(function ($) {
  $.extend($.summernote.lang, {
    'sk-SK': {
      font: {
        bold: 'Tučn<PERSON>',
        italic: '<PERSON><PERSON><PERSON><PERSON>',
        underline: 'Podtržené',
        strikethrough: 'Preškrtnuté',
        clear: 'Odstr<PERSON>iť štýl písma',
        height: 'Výška riadku',
        size: 'Veľkosť písma'
      },
      image: {
        image: 'Obr<PERSON><PERSON><PERSON>',
        insert: 'Vložiť obrázok',
        resizeFull: 'Pôvodná veľkosť',
        resizeHalf: 'Polovič<PERSON> veľkosť',
        resizeQuarter: 'Štvrtinová veľkosť',
        floatLeft: 'Umiestniť doľava',
        floatRight: 'Umiestniť doprava',
        floatNone: 'Bez zarovnania',
        dragImageHere: 'Pretiahnuť sem obrázok',
        selectFromFiles: 'Vybrať súbor',
        url: 'URL obrázku'
      },
      link: {
        link: 'Odkaz',
        insert: '<PERSON>yt<PERSON><PERSON><PERSON> odkaz',
        unlink: '<PERSON><PERSON><PERSON><PERSON><PERSON> odkaz',
        edit: 'Upravi<PERSON>',
        textToDisplay: 'Zobrazovaný text',
        url: 'Na akú URL adresu má tento odkaz viesť?',
        openInNewWindow: 'Otvoriť v novom okne'
      },
      video: {
        video: 'Video',
        videoLink: 'Odkaz videa',
        insert: 'Vložiť video',
        url: 'URL videa?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion nebo Youku)'
      },
      table: {
        table: 'Tabuľka'
      },
      hr: {
        insert: 'Vložit vodorovnú čiaru'
      },
      style: {
        style: 'Štýl',
        normal: 'Normálny',
        blockquote: 'Citácia',
        pre: 'Kód',
        h1: 'Nadpis 1',
        h2: 'Nadpis 2',
        h3: 'Nadpis 3',
        h4: 'Nadpis 4',
        h5: 'Nadpis 5',
        h6: 'Nadpis 6'
      },
      lists: {
        unordered: 'Odrážkový zoznam',
        ordered: 'Číselný zoznam'
      },
      options: {
        help: 'Pomoc',
        fullscreen: 'Celá obrazovka',
        codeview: 'HTML kód'
      },
      paragraph: {
        paragraph: 'Odstavec',
        outdent: 'Zvečiť odsadenie',
        indent: 'Zmenšiť odsadenie',
        left: 'Zarovnať doľava',
        center: 'Zarovnať na stred',
        right: 'Zarovnať doprava',
        justify: 'Zarovnať obojstranne'
      },
      color: {
        recent: 'Aktuálna farba',
        more: 'Dalšie farby',
        background: 'Farba pozadia',
        foreground: 'Farba písma',
        transparent: 'Priehľednosť',
        setTransparent: 'Nastaviť priehľadnosť',
        reset: 'Obnoviť',
        resetToDefault: 'Obnoviť prednastavené'
      },
      shortcut: {
        shortcuts: 'Klávesové skratky',
        close: 'Zavrieť',
        textFormatting: 'Formátovanie textu',
        action: 'Akcia',
        paragraphFormatting: 'Formátovánie odstavca',
        documentStyle: 'Štýl dokumentu'
      },
      history: {
        undo: 'Krok vzad',
        redo: 'Krok dopredu'
      }
    }
  });
})(jQuery);
