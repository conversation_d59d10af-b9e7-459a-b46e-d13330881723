/* LayerSlider Light Skin */



.ls-lightskin {
	padding: 5px;
	background: #f7f7f7;
	box-shadow: 0px 3px 15px -5px #000;
	-moz-box-shadow: 0px 3px 15px -5px #000;
	-webkit-box-shadow: 0px 3px 15px -5px #000;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
}

.ls-lightskin .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-lightskin .ls-playvideo,
.ls-lightskin .ls-nav-sides,
.ls-lightskin .ls-bottom-slidebuttons a,
.ls-lightskin .ls-nav-prev,
.ls-lightskin .ls-nav-next,
.ls-lightskin .ls-nav-start,
.ls-lightskin .ls-nav-stop,
.ls-lightskin .ls-fullscreen,
.ls-lightskin .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-lightskin .ls-playvideo {
	background-position: -300px -150px;
}

.ls-lightskin .ls-playvideo:hover,
.ls-lightskin .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-lightskin .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-lightskin .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-lightskin .ls-nav-next {
	background-position: -150px 0px;
}

.ls-lightskin .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-lightskin .ls-nav-start {
	background-position: -300px 0px;
}

.ls-lightskin .ls-nav-start:hover,
.ls-lightskin .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-lightskin .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-lightskin .ls-nav-stop:hover,
.ls-lightskin .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-lightskin .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-lightskin .ls-bottom-slidebuttons a.ls-nav-active,
.ls-lightskin .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-lightskin .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-lightskin .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-lightskin .ls-nav-prev,
.ls-lightskin .ls-nav-next {
	width: 27px;
	height: 75px;
	z-index: 10000;
	top: 50%;
	margin-top: -37px;	
	position: absolute;
}

.ls-lightskin .ls-nav-prev {
	left: 5px;	
}

.ls-lightskin .ls-nav-next {
	right: 5px;	
}



.ls-lightskin .ls-bottom-slidebuttons,
.ls-lightskin .ls-bottom-slidebuttons a,
.ls-lightskin .ls-nav-start,
.ls-lightskin .ls-nav-stop,
.ls-lightskin .ls-nav-sides {
	height: 28px;
}

.ls-lightskin .ls-bottom-slidebuttons,
.ls-lightskin .ls-bottom-slidebuttons a,
.ls-lightskin .ls-nav-start,
.ls-lightskin .ls-nav-stop,
.ls-lightskin .ls-nav-sides {
	display: inline-block;
}

.ls-lightskin .ls-bottom-slidebuttons,
.ls-lightskin .ls-nav-start,
.ls-lightskin .ls-nav-stop,
.ls-lightskin .ls-nav-sides {
	top: -28px;
}

.ls-lightskin .ls-nav-start,
.ls-lightskin .ls-nav-stop {
	width: 54px;
}

.ls-lightskin .ls-bottom-slidebuttons a {
	width: 18px;
}

.ls-lightskin .ls-nav-sides {
	width: 44px;
}



.ls-lightskin .ls-thumbnail-hover {
	padding: 4px;	
}

.ls-lightskin .ls-thumbnail-hover {
	bottom: 40px;
	margin-left: 1px;
}

.ls-lightskin .ls-thumbnail-hover-bg {
	background: #f7f7f7;
	box-shadow: 0px 2px 12px -4px black;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
}

.ls-lightskin .ls-thumbnail-hover span {
	border: 5px solid #f7f7f7;
	margin-left: -5px;
}



.ls-lightskin .ls-thumbnail {
	padding-top: 15px;
}

.ls-lightskin .ls-thumbnail-inner {
	padding: 5px;
	margin-left: -5px;
	background: #f7f7f7;
	box-shadow: 0px 1px 15px -5px #000;
	-moz-box-shadow: 0px 1px 15px -5px #000;
	-webkit-box-shadow: 0px 1px 15px -5px #000;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
}

.ls-lightskin .ls-thumbnail-slide a {
	margin-right: 5px;
}

.ls-lightskin .ls-nothumb {
	background: #e3e3e3;
}



.ls-lightskin .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;	
}

.ls-lightskin .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);	
}



.ls-lightskin .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-lightskin .ls-fullscreen:hover {
	background-position: -525px -190px;
}