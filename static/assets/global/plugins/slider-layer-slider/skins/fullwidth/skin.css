/* LayerSlider Full Width Skin */



.ls-fullwidth .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-fullwidth .ls-playvideo,
.ls-fullwidth .ls-nav-sides,
.ls-fullwidth .ls-bottom-slidebuttons a,
.ls-fullwidth .ls-nav-prev,
.ls-fullwidth .ls-nav-next,
.ls-fullwidth .ls-nav-start,
.ls-fullwidth .ls-nav-stop,
.ls-fullwidth .ls-fullscreen,
.ls-fullwidth .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-fullwidth .ls-playvideo {
	background-position: -300px -150px;
}

.ls-fullwidth .ls-playvideo:hover,
.ls-fullwidth .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-fullwidth .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-fullwidth .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-fullwidth .ls-nav-next {
	background-position: -150px 0px;
}

.ls-fullwidth .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-fullwidth .ls-nav-start {
	background-position: -300px 0px;
}

.ls-fullwidth .ls-nav-start:hover,
.ls-fullwidth .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-fullwidth .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-fullwidth .ls-nav-stop:hover,
.ls-fullwidth .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-fullwidth .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-fullwidth .ls-bottom-slidebuttons a.ls-nav-active,
.ls-fullwidth .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-fullwidth .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-fullwidth .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-fullwidth .ls-nav-prev,
.ls-fullwidth .ls-nav-next {
	width: 40px;
	height: 40px;
	z-index: 10000;
	top: 50%;
	margin-top: -20px;	
	position: absolute;
}

.ls-fullwidth .ls-nav-prev {
	left: 10px;	
}

.ls-fullwidth .ls-nav-next {
	right: 10px;	
}



.ls-fullwidth .ls-bottom-slidebuttons,
.ls-fullwidth .ls-bottom-slidebuttons a,
.ls-fullwidth .ls-nav-start,
.ls-fullwidth .ls-nav-stop,
.ls-fullwidth .ls-nav-sides {
	height: 20px;
}

.ls-fullwidth .ls-bottom-slidebuttons,
.ls-fullwidth .ls-bottom-slidebuttons a,
.ls-fullwidth .ls-nav-start,
.ls-fullwidth .ls-nav-stop,
.ls-fullwidth .ls-nav-sides {
	display: inline-block;
}

.ls-fullwidth .ls-bottom-slidebuttons,
.ls-fullwidth .ls-nav-start,
.ls-fullwidth .ls-nav-stop,
.ls-fullwidth .ls-nav-sides {
	top: -30px;
}

.ls-fullwidth .ls-nav-start,
.ls-fullwidth .ls-nav-stop {
	width: 25px;
}

.ls-fullwidth .ls-bottom-slidebuttons a {
	width: 20px;
}

.ls-fullwidth .ls-nav-sides {
	width: 0px;	
}



.ls-fullwidth .ls-thumbnail-hover {
	bottom: 30px;
	padding: 2px;
	margin-left: 1px;
}

.ls-fullwidth .ls-thumbnail-hover-bg {
	background: #eee;
}

.ls-fullwidth .ls-thumbnail-hover span {
	border: 5px solid #eee;
	margin-left: -5px;
}



.ls-fullwidth .ls-thumbnail {
	top: 10px;
}

.ls-fullwidth .ls-thumbnail-inner {
	padding: 2px;
	margin-left: -2px;
	background: #fff;
}

.ls-fullwidth .ls-thumbnail-slide a {
	margin-right: 2px;
}

.ls-fullwidth .ls-nothumb {
	background: #eee;
}



.ls-fullwidth .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;	
}

.ls-fullwidth .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);	
}



.ls-fullwidth .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-fullwidth .ls-fullscreen:hover {
	background-position: -525px -190px;
}