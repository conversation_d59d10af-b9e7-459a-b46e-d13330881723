/* LayerSlider Dark Skin */



.ls-darkskin {
	padding: 5px;
	background: #222;
	box-shadow: 0px 3px 15px -5px #000;
	-moz-box-shadow: 0px 3px 15px -5px #000;
	-webkit-box-shadow: 0px 3px 15px -5px #000;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
}

.ls-darkskin .ls-ct-half {
	background: #444;
}

.ls-darkskin .ls-ct-center {
	background: white;
}

.ls-darkskin .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-darkskin .ls-playvideo,
.ls-darkskin .ls-nav-sides,
.ls-darkskin .ls-bottom-slidebuttons a,
.ls-darkskin .ls-nav-prev,
.ls-darkskin .ls-nav-next,
.ls-darkskin .ls-nav-start,
.ls-darkskin .ls-nav-stop,
.ls-darkskin .ls-fullscreen,
.ls-darkskin .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-darkskin .ls-playvideo {
	background-position: -300px -150px;
}

.ls-darkskin .ls-playvideo:hover,
.ls-darkskin .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-darkskin .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-darkskin .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-darkskin .ls-nav-next {
	background-position: -150px 0px;
}

.ls-darkskin .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-darkskin .ls-nav-start {
	background-position: -300px 0px;
}

.ls-darkskin .ls-nav-start:hover,
.ls-darkskin .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-darkskin .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-darkskin .ls-nav-stop:hover,
.ls-darkskin .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-darkskin .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-darkskin .ls-bottom-slidebuttons a.ls-nav-active,
.ls-darkskin .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-darkskin .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-darkskin .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-darkskin .ls-nav-prev,
.ls-darkskin .ls-nav-next {
	width: 27px;
	height: 75px;
	z-index: 10000;
	top: 50%;
	margin-top: -37px;	
	position: absolute;
}

.ls-darkskin .ls-nav-prev {
	left: 5px;	
}

.ls-darkskin .ls-nav-next {
	right: 5px;	
}



.ls-darkskin .ls-bottom-slidebuttons,
.ls-darkskin .ls-bottom-slidebuttons a,
.ls-darkskin .ls-nav-start,
.ls-darkskin .ls-nav-stop,
.ls-darkskin .ls-nav-sides {
	height: 28px;
}

.ls-darkskin .ls-bottom-slidebuttons,
.ls-darkskin .ls-bottom-slidebuttons a,
.ls-darkskin .ls-nav-start,
.ls-darkskin .ls-nav-stop,
.ls-darkskin .ls-nav-sides {
	display: inline-block;
}

.ls-darkskin .ls-bottom-slidebuttons,
.ls-darkskin .ls-nav-start,
.ls-darkskin .ls-nav-stop,
.ls-darkskin .ls-nav-sides {
	top: -28px;
}

.ls-darkskin .ls-nav-start,
.ls-darkskin .ls-nav-stop {
	width: 54px;
}

.ls-darkskin .ls-bottom-slidebuttons a {
	width: 18px;
}

.ls-darkskin .ls-nav-sides {
	width: 44px;
}



.ls-darkskin .ls-thumbnail-hover {
	padding: 4px;	
}

.ls-darkskin .ls-thumbnail-hover {
	bottom: 40px;
	margin-left: 1px;
}

.ls-darkskin .ls-thumbnail-hover-bg {
	background: #222;
	box-shadow: 0px 2px 12px -4px black;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
}

.ls-darkskin .ls-thumbnail-hover span {
	border: 5px solid #222;
	margin-left: -5px;
}



.ls-darkskin .ls-thumbnail {
	padding-top: 15px;
}

.ls-darkskin .ls-thumbnail-inner {
	padding: 5px;
	margin-left: -5px;
	background: #222;
	box-shadow: 0px 3px 15px -5px #000;
	-moz-box-shadow: 0px 3px 15px -5px #000;
	-webkit-box-shadow: 0px 3px 15px -5px #000;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
}

.ls-darkskin .ls-thumbnail-slide a {
	margin-right: 5px;
}

.ls-darkskin .ls-nothumb {
	background: #333;
}



.ls-darkskin .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;	
}

.ls-darkskin .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);	
}



.ls-darkskin .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-darkskin .ls-fullscreen:hover {
	background-position: -525px -190px;
}