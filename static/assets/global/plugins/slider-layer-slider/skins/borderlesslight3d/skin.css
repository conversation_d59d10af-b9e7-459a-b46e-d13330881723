/* LayerSlider Borderless Light 3D Skin */



.ls-borderlesslight3d .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-borderlesslight3d .ls-playvideo,
.ls-borderlesslight3d .ls-nav-sides,
.ls-borderlesslight3d .ls-bottom-slidebuttons a,
.ls-borderlesslight3d .ls-nav-prev,
.ls-borderlesslight3d .ls-nav-next,
.ls-borderlesslight3d .ls-nav-start,
.ls-borderlesslight3d .ls-nav-stop,
.ls-borderlesslight3d .ls-fullscreen,
.ls-borderlesslight3d .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-borderlesslight3d .ls-playvideo {
	background-position: -300px -150px;
}

.ls-borderlesslight3d .ls-playvideo:hover,
.ls-borderlesslight3d .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-borderlesslight3d .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-borderlesslight3d .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-borderlesslight3d .ls-nav-next {
	background-position: -150px 0px;
}

.ls-borderlesslight3d .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-borderlesslight3d .ls-nav-start {
	background-position: -300px 0px;
}

.ls-borderlesslight3d .ls-nav-start:hover,
.ls-borderlesslight3d .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-borderlesslight3d .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-borderlesslight3d .ls-nav-stop:hover,
.ls-borderlesslight3d .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-borderlesslight3d .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-borderlesslight3d .ls-bottom-slidebuttons a.ls-nav-active,
.ls-borderlesslight3d .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-borderlesslight3d .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-borderlesslight3d .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-borderlesslight3d .ls-nav-prev,
.ls-borderlesslight3d .ls-nav-next {
	width: 40px;
	height: 40px;
	z-index: 10000;
	top: 50%;
	margin-top: -20px;	
	position: absolute;
}

.ls-borderlesslight3d .ls-nav-prev {
	left: 10px;	
}

.ls-borderlesslight3d .ls-nav-next {
	right: 10px;	
}



.ls-borderlesslight3d .ls-bottom-slidebuttons,
.ls-borderlesslight3d .ls-bottom-slidebuttons a,
.ls-borderlesslight3d .ls-nav-start,
.ls-borderlesslight3d .ls-nav-stop,
.ls-borderlesslight3d .ls-nav-sides {
	height: 20px;
}

.ls-borderlesslight3d .ls-bottom-slidebuttons,
.ls-borderlesslight3d .ls-bottom-slidebuttons a,
.ls-borderlesslight3d .ls-nav-start,
.ls-borderlesslight3d .ls-nav-stop,
.ls-borderlesslight3d .ls-nav-sides {
	display: inline-block;
}

.ls-borderlesslight3d .ls-bottom-slidebuttons,
.ls-borderlesslight3d .ls-nav-start,
.ls-borderlesslight3d .ls-nav-stop,
.ls-borderlesslight3d .ls-nav-sides {
	top: -30px;
}

.ls-borderlesslight3d .ls-nav-start,
.ls-borderlesslight3d .ls-nav-stop {
	width: 25px;
}

.ls-borderlesslight3d .ls-bottom-slidebuttons a {
	width: 20px;
}

.ls-borderlesslight3d .ls-nav-sides {
	width: 0px;	
}



.ls-borderlesslight3d .ls-thumbnail-hover {
	bottom: 30px;
	padding: 3px;
}

.ls-borderlesslight3d .ls-thumbnail-hover-bg {
	background: #eee;
	box-shadow: 0px 2px 12px -4px black;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;	
}

.ls-borderlesslight3d .ls-thumbnail-hover span {
	border: 5px solid #eee;
	margin-left: -5px;
}



.ls-borderlesslight3d .ls-thumbnail {
	top: 10px;
}

.ls-borderlesslight3d .ls-thumbnail-inner {
	padding: 3px;
	margin-left: -3px;
	background: #fff;
	box-shadow: 0px 3px 15px -5px #000;
	-moz-box-shadow: 0px 3px 15px -5px #000;
	-webkit-box-shadow: 0px 3px 15px -5px #000;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;	
}

.ls-borderlesslight3d .ls-thumbnail-slide a {
	margin-right: 3px;
}

.ls-borderlesslight3d .ls-nothumb {
	background: #eee;
}



.ls-borderlesslight3d .ls-shadow {
	display: block;
}



.ls-borderlesslight3d .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;	
}

.ls-borderlesslight3d .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);	
}



.ls-borderlesslight3d .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-borderlesslight3d .ls-fullscreen:hover {
	background-position: -525px -190px;
}