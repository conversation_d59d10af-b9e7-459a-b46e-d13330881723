/* LayerSlider Full Width Skin */



.ls-v5 .ls-playvideo {
	width: 60px;
	height: 60px;
	margin-left: -30px;
	margin-top: -30px;
}

.ls-v5 .ls-playvideo,
.ls-v5 .ls-nav-sides,
.ls-v5 .ls-bottom-slidebuttons a,
.ls-v5 .ls-nav-prev,
.ls-v5 .ls-nav-next,
.ls-v5 .ls-nav-start,
.ls-v5 .ls-nav-stop,
.ls-v5 .ls-fullscreen,
.ls-v5 .ls-loading-container {
	background-image: url(skin.png);
}

.ls-v5 .ls-playvideo {
	background-position: -300px -150px;
}

.ls-v5 .ls-playvideo:hover,
.ls-v5 .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-v5 .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-v5 .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-v5 .ls-nav-next {
	background-position: -150px 0px;
}

.ls-v5 .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-v5 .ls-nav-start {
	background-position: -300px 0px;
}

.ls-v5 .ls-nav-start:hover,
.ls-v5 .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-v5 .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-v5 .ls-nav-stop:hover,
.ls-v5 .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-v5 .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-v5 .ls-bottom-slidebuttons a.ls-nav-active,
.ls-v5 .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-v5 .ls-nav-sideleft {
	background-position: -150px -150px;
}

.ls-v5 .ls-nav-sideright {
	background-position: -225px -150px;
}



.ls-v5 .ls-nav-prev,
.ls-v5 .ls-nav-next {
	width: 40px;
	height: 50px;
	z-index: 10000;
	top: 50%;
	margin-top: -25px;
	position: absolute;
}

.ls-v5 .ls-nav-prev {
	left: 10px;
}

.ls-v5 .ls-nav-next {
	right: 10px;
}



.ls-v5 .ls-bottom-slidebuttons,
.ls-v5 .ls-bottom-slidebuttons a,
.ls-v5 .ls-nav-start,
.ls-v5 .ls-nav-stop,
.ls-v5 .ls-nav-sides {
	height: 20px;
}

.ls-v5 .ls-bottom-slidebuttons,
.ls-v5 .ls-bottom-slidebuttons a,
.ls-v5 .ls-nav-start,
.ls-v5 .ls-nav-stop,
.ls-v5 .ls-nav-sides {
	display: inline-block;
}

.ls-v5 .ls-bottom-slidebuttons,
.ls-v5 .ls-nav-start,
.ls-v5 .ls-nav-stop,
.ls-v5 .ls-nav-sides {
	top: -30px;
}

.ls-v5 .ls-nav-start,
.ls-v5 .ls-nav-stop {
	width: 25px;
}

.ls-v5 .ls-bottom-slidebuttons a {
	width: 20px;
}

.ls-v5 .ls-nav-sides {
	width: 0px;
}



.ls-v5 .ls-thumbnail-hover {
	bottom: 30px;
	padding: 2px;
	margin-left: 1px;
}

.ls-v5 .ls-thumbnail-hover-bg {
	background: #eee;
}

.ls-v5 .ls-thumbnail-hover span {
	border: 5px solid #eee;
	margin-left: -5px;
}



.ls-v5 .ls-thumbnail {
	top: 10px;
}

.ls-v5 .ls-thumbnail-inner {
	padding: 2px;
	margin-left: -2px;
	background: #fff;
}

.ls-v5 .ls-thumbnail-slide a {
	margin-right: 2px;
}

.ls-v5 .ls-nothumb {
	background: #eee;
}



.ls-v5 .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;
}

.ls-v5 .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);
}



.ls-v5 .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-v5 .ls-fullscreen:hover {
	background-position: -525px -190px;
}
