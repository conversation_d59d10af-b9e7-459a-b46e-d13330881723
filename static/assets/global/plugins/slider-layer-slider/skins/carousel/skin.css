/* LayerSlider Carousel Skin */



.ls-carousel .ls-bottom-nav-wrapper {
	margin: 10px auto;
}

.ls-carousel .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-carousel .ls-playvideo,
.ls-carousel .ls-nav-sides,
.ls-carousel .ls-bottom-slidebuttons a,
.ls-carousel .ls-nav-prev,
.ls-carousel .ls-nav-next,
.ls-carousel .ls-nav-start,
.ls-carousel .ls-nav-stop,
.ls-carousel .ls-fullscreen,
.ls-carousel .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-carousel .ls-playvideo {
	background-position: -300px -150px;
}

.ls-carousel .ls-playvideo:hover,
.ls-carousel .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-carousel .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-carousel .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-carousel .ls-nav-next {
	background-position: -150px 0px;
}

.ls-carousel .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-carousel .ls-nav-start {
	background-position: -300px 0px;
}

.ls-carousel .ls-nav-start:hover,
.ls-carousel .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-carousel .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-carousel .ls-nav-stop:hover,
.ls-carousel .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-carousel .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-carousel .ls-bottom-slidebuttons a.ls-nav-active,
.ls-carousel .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-carousel .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-carousel .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-carousel .ls-nav-prev,
.ls-carousel .ls-nav-next {
	width: 50px;
	height: 50px;
	z-index: 10000;
	top: 50%;
	margin-top: -22px;	
	position: absolute;
}

.ls-carousel .ls-nav-prev {
	left: 10px;	
}

.ls-carousel .ls-nav-next {
	right: 10px;	
}



.ls-carousel .ls-bottom-slidebuttons,
.ls-carousel .ls-bottom-slidebuttons a,
.ls-carousel .ls-nav-start,
.ls-carousel .ls-nav-stop,
.ls-carousel .ls-nav-sides {
	height: 36px;
}

.ls-carousel .ls-bottom-slidebuttons,
.ls-carousel .ls-bottom-slidebuttons a,
.ls-carousel .ls-nav-start,
.ls-carousel .ls-nav-stop,
.ls-carousel .ls-nav-sides {
	display: inline-block;
}

.ls-carousel .ls-nav-start,
.ls-carousel .ls-nav-stop {
	width: 24px;
}

.ls-carousel .ls-bottom-slidebuttons a {
	width: 20px;
}

.ls-carousel .ls-nav-sides {
	width: 13px;	
}



.ls-carousel .ls-thumbnail-hover {
	bottom: 45px;
	padding: 3px;
	margin-left: 1px;
}

.ls-carousel .ls-thumbnail-hover-bg {
	background: #eee;
	box-shadow: 0px 2px 12px -4px black;
	border-radius: 4px;
}

.ls-carousel .ls-thumbnail-hover span {
	border: 5px solid #eee;
	margin-left: -5px;
}



.ls-carousel .ls-thumbnail {
	top: 10px;
}

.ls-carousel .ls-thumbnail-inner {
	padding: 4px;
	margin-left: -4px;
	background: #eee;
	border-radius: 4px;
}

.ls-carousel .ls-thumbnail-slide a {
	margin-right: 4px;
}

.ls-carousel .ls-nothumb {
	background: #eee;
}



.ls-carousel .ls-above-thumbnails {
	display: none;
}

.ls-carousel .ls-below-thumbnails {
	display: block;
	margin-top: 10px;
}



.ls-carousel .ls-loading-container {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -22px;
	background-position: -450px -150px;	
}

.ls-carousel .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 11px;
	background-image: url(loading.gif);	
}



.ls-carousel .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-carousel .ls-fullscreen:hover {
	background-position: -525px -190px;
}