<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Owl Carousel</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="jQuery Responsive Carousel - Owl Carusel">
    <meta name="author" content="<PERSON><PERSON><PERSON>">
    <meta name="keywords" content="HTML,CSS,JSON,JavaScript, jQuery, Responsive, Design, Owl, Carousel, Free">

    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,600,700' rel='stylesheet' type='text/css'>
    <link href="assets/css/bootstrapTheme.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">

    <!-- Owl Carousel Assets -->
    <link href="owl-carousel/owl.carousel.css" rel="stylesheet">
    <link href="owl-carousel/owl.theme.css" rel="stylesheet">

    <!-- Prettify -->
    <link href="assets/js/google-code-prettify/prettify.css" rel="stylesheet">

    <!-- Le fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="assets/ico/favicon.png">
  </head>
  <body>
      <div id="top-nav" class="navbar navbar-fixed-top">
        <div class="navbar-inner">
          <div class="container">
            <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
            <div class="nav-collapse collapse">
            <ul class="nav pull-right">
              <li><a href="#demo">Demo</a></li>
              <li><a href="#more-demos">More Demos</a></li>
              <li><a href="#how-to">How To</a></li>
              <li><a href="#customizing">Customizing</a></li>
              <li><a href="#faq">FAQ</a></li>
              <li><a href="owl.carousel.zip" class="download" data-spy="affix" data-offset-top="450">Download</a></li>
            </ul>
            </div>
          </div>
        </div>
      </div>

      <div id="header">
        <div class="container">
          <div class="row">
            <div class="span5">
                <img class="logo" src="assets/img/owl-logo.png" alt="Owl Logo">
              </div>
            <div class="span7">
              <h1>OWL Carousel </h1>
              <h3>Touch enabled jQuery plugin that lets you create beautiful responsive carousel slider.</h3>
              <a class="btn btn-success btn-large" href="owl.carousel.zip">Download for FREE</a>
              <p class="muted"><a class="muted" href="https://github.com/OwlFonk/OwlCarousel">Github</a> / v1.3.2 / <a class="muted" href="changelog.html">Changelog</a></p> 
            </div>
          </div>
        </div>
      </div>

      <div id="demo">
        <div class="container">
          <div class="row">
            <div class="span12">
              <h1>Demo</h1>
            </div>
          </div>

          <div class="row">
            <div class="span12">

              <div id="owl-example" class="owl-carousel">

                <div class="item darkCyan">
                  <img src="assets/img/demo-slides/touch.png" alt="Touch">
                    <h3>Touch</h3>
                    <h4>Can touch this</h4>
                </div>
                <div class="item forestGreen">
                  <img src="assets/img/demo-slides/grab.png" alt="Grab">
                    <h3>Grab</h3>
                    <h4>Can grab this</h4>
                </div>
                <div class="item orange">
                  <img src="assets/img/demo-slides/responsive.png" alt="Responsive">
                    <h3>Responsive</h3>
                    <h4>Fully responsive!</h4>
                </div>

                <div class="item yellow">
                  <img src="assets/img/demo-slides/css3.png" alt="CSS3">
                    <h3>CSS3</h3>
                    <h4>3D Acceleration.</h4>
                </div>

                <div class="item dodgerBlue">
                  <img src="assets/img/demo-slides/multi.png" alt="Multi">
                    <h3>Multiply</h3>
                    <h4>Owls on page.</h4>
                </div>

                <div class="item skyBlue">
                  <img src="assets/img/demo-slides/modern.png" alt="Modern Browsers">
                    <h3>Modern</h3>
                    <h4>Browsers Compatibility</h4>
                </div>

                <div class="item zombieGreen">
                  <img src="assets/img/demo-slides/zombie.png" alt="Zombie Browsers - old ones">
                    <h3>Zombie</h3>
                    <h4>Browsers Compatibility</h4>
                </div>

                <div class="item violet">
                  <img src="assets/img/demo-slides/controls.png" alt="Take Control">
                    <h3>Take Control</h3>
                    <h4>The way you like</h4>
                </div>

                <div class="item yellowLight">
                  <img src="assets/img/demo-slides/feather.png" alt="Light">
                    <h3>Light</h3>
                    <h4>As a feather</h4>
                </div>

                <div class="item steelGray">
                  <img src="assets/img/demo-slides/tons.png" alt="Tons of Opotions">
                    <h3>Tons</h3>
                    <h4>of options</h4>
                </div>

              </div>


            </div>
          </div>

        </div>
      </div>

    <div id="more-demos">
      <div class="container">

        <div class="row">
          <div class="span12">
            <h1>More Demos</h1>
            <h2>Awesome!</h2>
            <p>Check more demos here! See what Owl can do.</p>
          </div>
        </div>

        <div class="row demos-row">
          <div class="span3">
            <a href="demos/images.html" class="demo-box">
              <div class="demo-wrapper demo-images clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Images</h3>
            </a>
          </div>

          <div class="span3">
            <a href="demos/custom.html" class="demo-box">
              <div class="demo-wrapper demo-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="demos/itemsCustom.html" class="demo-box">
              <div class="demo-wrapper demo-full clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom 2</h3>
            </a>
          </div>

          <div class="span3">
            <a href="demos/one.html" class="demo-box">
              <div class="demo-wrapper demo-one clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>One Slide</h3>
            </a>
          </div>

        </div>
        <div class="row demos-row">
          <div class="span3">
            <a href="demos/json.html" class="demo-box">
              <div class="demo-wrapper demo-Json clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON</h3>
            </a>
          </div>

          <div class="span3">
            <a href="demos/customJson.html" class="demo-box">
              <div class="demo-wrapper demo-Json-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="demos/lazyLoad.html" class="demo-box">
              <div class="demo-wrapper demo-lazy clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Lazy Load</h3>
            </a>
          </div>

          <div class="span3">
            <a href="demos/autoHeight.html" class="demo-box">
              <div class="demo-wrapper demo-height clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Auto Height</h3>
            </a>
          </div>

          
        </div>

        <div class="row">
          <div class="span12">
            <h3>Hey wanna see more demos?</h3>
            <ul>
              <li>
                <a href="demos/click.html">Click events inside items</a>
              </li>
              <li>
                <a href="demos/randomOrder.html">Randomize items and buttons outside slider.</a>
              </li>
              <li>
                <a href="demos/navOnTop.html">Navigation on top by custom events</a>
              </li>
              <li>
                <a href="demos/navOnTop2.html">Navigation on top by afterInit callback</a>
              </li>
              <li>
                <a href="demos/progressBar.html">Progress Bar</a>
              </li>
              <li>
                <a href="demos/transitions.html">CSS3 Transitions</a>
              </li>
              <li>
                <a href="demos/manipulations.html">Content manipulations: destroy, reinit, addItem, removeItem</a>
              </li>
              <li>
                <a href="demos/scaleup.html">Auto scale up comparsion demo</a>
              </li>
              <li>
                <a href="demos/sync.html">Synced Owls</a>
              </li>
              <li>
                <a href="demos/owlStatus.html">How to retrieve basic information from plugin (current, prev, all items, visible items etc.)</a>
              </li>
            </ul>
            
          </div>
        </div>


      </div>
    </div>

    <div id="how-to">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>How To Use</h1>
            <h2>1. Load jQuery and include Owl Carousel plugin files</h2>
            <p>To use Owl Carousel, you’ll need to make sure both the Owl and jQuery 1.7 or higher scripts are included.</p>
<pre class="pre-show prettyprint linenums">
&lt;!-- Important Owl stylesheet --&gt;
&lt;link rel="stylesheet" href="owl-carousel/owl.carousel.css"&gt;

&lt;!-- Default Theme --&gt;
&lt;link rel="stylesheet" href="owl-carousel/owl.theme.css"&gt;

&lt;!--  jQuery 1.7+  --&gt;
&lt;script src="jquery-1.9.1.min.js"&gt;&lt;/script&gt;

&lt;!-- Include js plugin --&gt;
&lt;script src="assets/owl-carousel/owl.carousel.js"&gt;&lt;/script&gt;
</pre>
          <h2>2. Set up your HTML</h2>
          <p>You don't need any special markup. All you need is to wrap your divs(owl works with any type element) inside the container element &lt;div class="owl-carousel"&gt;. Class "owl-carousel" is mandatory to apply proper styles that come from owl.carousel.css file.</p>
<pre class="pre-show prettyprint linenums">
&lt;div id="owl-example" class="owl-carousel"&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  ...
&lt;/div>
</pre>    
          <h2>3. Call the plugin</h2>
          <p>Now call the Owl initializer function and your carousel is ready.</p>
<pre class="pre-show prettyprint linenums">
$(document).ready(function() {

  $("#owl-example").owlCarousel();

});
</pre>  
          </div>
        </div>
      </div>
    </div>


    <div id="customizing">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Customizing</h1>
            <h2>1. Options</h2>
            <p>All of the options below are available to customize Owl Carousel.</p> 
            <table class="table hp-table table-bordered table-striped">
              <thead>
                <tr>
                  <th>Variable</th>
                  <th>Default</th>
                  <th>Type</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><span class="text-emp">items</span></td>
                  <td>5</td>
                  <td>int</td>
                  <td>This variable allows you to set the maximum amount of items displayed at a time with the widest browser width</td>
                </tr>
                 <tr>
                  <td><span class="text-emp">itemsDesktop</span></td>
                  <td>[1199,4]</td>
                  <td>array</td>
                  <td>This allows you to preset the number of slides visible with a particular browser width. The format is [x,y] whereby x=browser width and y=number of slides displayed. For example [1199,4] means that if(window&lt;=1199){ show 4 slides per page}
                    Alternatively use <code>itemsDesktop: false</code> to override these settings. Check my <a href="demos/custom.html">Custom Demo</a></td>
                </tr>
                <tr>
                  <td><span class="text-emp">itemsDesktopSmall</span></td>
                  <td>[979,3]</td>
                  <td>array</td>
                  <td>As above.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">itemsTablet</span></td>
                  <td>[768,2]</td>
                  <td>array</td>
                  <td>As above.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">itemsTabletSmall</span></td>
                  <td>false</td>
                  <td>array</td>
                  <td>As above. Default value is disabled.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">itemsMobile</span></td>
                  <td>[479,1]</td>
                  <td>array</td>
                  <td>As above</td>
                </tr>
                <tr>
                  <td><span class="text-emp">itemsCustom</span></td>
                  <td>false</td>
                  <td>array</td>
                  <td>
                    This allow you to add custom variations of items depending from the width
                    If this option is set, itemsDeskop, itemsDesktopSmall, itemsTablet, itemsMobile etc. are disabled
                    For better preview, order the arrays by screen size, but it's not mandatory
                    Don't forget to include the lowest available screen size, otherwise it will take the default one for screens lower than lowest available.
                    <br />Example:<br /> [[0, 2], [400, 4], [700, 6], [1000, 8], [1200, 10], [1600, 16]]<br />
                    For more information about structure of the internal arrays see itemsDesktop. Check my <a href="demos/custom.html">Custom Demo</a></td>
                </tr>
                <tr>
                  <td><span class="text-emp">singleItem</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Display only one item. <a href="demos/one.html">See demo</a></td>
                </tr>
                <tr>
                  <td><span class="text-emp">itemsScaleUp</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Option to not stretch items when it is less than the supplied items. <a href="demos/scaleup.html">See demo</a></td>
                </tr>
                <tr>
                  <td><span class="text-emp">slideSpeed</span></td>
                  <td>200</td>
                  <td>int</td>
                  <td>Slide speed in milliseconds</td>
                </tr>
                <tr>
                  <td><span class="text-emp">paginationSpeed</span></td>
                  <td>800</td>
                  <td>int</td>
                  <td>Pagination speed in milliseconds</td>
                </tr>
                <tr>
                  <td><span class="text-emp">rewindSpeed</span></td>
                  <td>1000</td>
                  <td>int</td>
                  <td>Rewind speed in milliseconds</td>
                </tr>
                <tr>
                  <td><span class="text-emp">autoPlay</span></td>
                  <td>false</td>
                  <td>int/boolean</td>
                  <td>Change to any integrer for example <code>autoPlay : 5000</code> to play every 5 seconds. If you set <code>autoPlay: true</code> default speed will be 5 seconds.</td>
                </tr>
                <tr>
                <tr>
                  <td><span class="text-emp">stopOnHover</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Stop autoplay on mouse hover</td>
                </tr>
                <tr>
                <tr>
                  <td><span class="text-emp">navigation</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Display "next" and "prev" buttons.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">navigationText</span></td>
                  <td>["prev","next"]</td>
                  <td>array</td>
                  <td>You can cusomize your own text for navigation. To get empty buttons use <code>navigationText : false</code>. Also HTML can be used here</td>
                </tr>
                <tr>
                  <td><span class="text-emp">rewindNav</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>Slide to first item. Use <code>rewindSpeed</code> to change animation speed. </td>
                </tr>
                <tr>
                  <td><span class="text-emp">scrollPerPage</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Scroll per page not per item. This affect next/prev buttons and mouse/touch dragging.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">pagination</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>Show pagination.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">paginationNumbers</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Show numbers inside pagination buttons</td>
                </tr>
                <tr>
                  <td><span class="text-emp">responsive</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>You can use Owl Carousel on desktop-only websites too! Just change that to "false" to disable resposive capabilities</td>
                </tr>
                <tr>
                  <td><span class="text-emp">responsiveRefreshRate</span></td>
                  <td>200</td>
                  <td>int</td>
                  <td>Check window width changes every 200ms for responsive actions</td>
                </tr>
                <tr>
                  <td><span class="text-emp">responsiveBaseWidth</span></td>
                  <td>window</td>
                  <td>jQuery selector</td>
                  <td>Owl Carousel check window for browser width changes. You can use any other jQuery element to check width changes for example ".owl-demo". Owl will change only if ".owl-demo" get new width.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">baseClass</span></td>
                  <td>"owl-carousel"</td>
                  <td>string</td>
                  <td>Automaticly added class for base CSS styles. Best not to change it if you don't need to.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">theme</span></td>
                  <td>"owl-theme"</td>
                  <td>string</td>
                  <td>Default Owl CSS styles for navigation and buttons. Change it to match your own theme</td>
                </tr>
                <tr>
                  <td><span class="text-emp">lazyLoad</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Delays loading of images. Images outside of viewport won't be loaded before user scrolls to them. Great for mobile devices to speed up page loadings. IMG need special markup <code>class="lazyOwl"</code> and <code>data-src="your img path"</code>. <a href="demos/lazyLoad.html">See example.</a></td>
                </tr>
                <tr>
                  <td><span class="text-emp">lazyFollow</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>When pagination used, it skips loading the images from pages that got skipped. It only loads the images that get displayed in viewport. If set to false, all images get loaded when pagination used. It is a sub setting of the lazy load function.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">lazyEffect</span></td>
                  <td>"fade"</td>
                  <td>boolean / string</td>
                  <td>Default is fadeIn on 400ms speed. Use false to remove that effect.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">autoHeight</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Add height to owl-wrapper-outer so you can use diffrent heights on slides. Use it only for one item per page setting.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">jsonPath</span></td>
                  <td>false</td>
                  <td>string</td>
                  <td>Allows you to load directly from a jSon file. The JSON structure you use needs to match the owl JSON structure used here. To use custom JSON structure see jsonSuccess option.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">jsonSuccess</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>Success callback for $.getJSON build in into carousel. See demo with custom JSON structure <a href="demos/customJson.html">here</a>. </td>
                </tr>
                <tr>
                  <td><span class="text-emp">dragBeforeAnimFinish</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>Ignore whether a transition is done or not (only dragging).</td>
                </tr>
                <tr>
                  <td><span class="text-emp">mouseDrag</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>Turn off/on mouse events.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">touchDrag</span></td>
                  <td>true</td>
                  <td>boolean</td>
                  <td>Turn off/on touch events.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">addClassActive</span></td>
                  <td>false</td>
                  <td>boolean</td>
                  <td>Add "active" classes on visible items. Works with any numbers of items on screen.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">transitionStyle</span></td>
                  <td>false</td>
                  <td>string</td>
                  <td>Add CSS3 transition style. Works only with one item on screen. <a href="demos/transitions.html">See Demo</a></td>
                </tr>
              </tbody>
            </table>
            <h2>2. Callbacks</h2>
            <table class="table hp-table table-bordered table-striped">
              <thead>
                <tr>
                  <th>Variable</th>
                  <th>Default</th>
                  <th>Type</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><span class="text-emp">beforeUpdate</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>Before responsive update callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">afterUpdate</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>After responsive update callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">beforeInit</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>Before initialization callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">afterInit</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>After initialization callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">beforeMove</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>Before move callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">afterMove</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>After move callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">afterAction</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>After startup, move and update callback</td>
                </tr>
                <tr>
                  <td><span class="text-emp">startDragging</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>Call this function while dragging.</td>
                </tr>
                <tr>
                  <td><span class="text-emp">afterLazyLoad</span></td>
                  <td>false</td>
                  <td>function</td>
                  <td>Call this function after lazyLoad.</td>
                </tr>
              </tbody>
            </table>

              <h2>3. Defaults</h2>
              <p>Carousel default settings</p>
<pre class="pre-show prettyprint linenums">
$("#owl-example").owlCarousel({

    // Most important owl features
    items : 5,
    itemsCustom : false,
    itemsDesktop : [1199,4],
    itemsDesktopSmall : [980,3],
    itemsTablet: [768,2],
    itemsTabletSmall: false,
    itemsMobile : [479,1],
    singleItem : false,
    itemsScaleUp : false,

    //Basic Speeds
    slideSpeed : 200,
    paginationSpeed : 800,
    rewindSpeed : 1000,

    //Autoplay
    autoPlay : false,
    stopOnHover : false,

    // Navigation
    navigation : false,
    navigationText : ["prev","next"],
    rewindNav : true,
    scrollPerPage : false,

    //Pagination
    pagination : true,
    paginationNumbers: false,

    // Responsive 
    responsive: true,
    responsiveRefreshRate : 200,
    responsiveBaseWidth: window,

    // CSS Styles
    baseClass : "owl-carousel",
    theme : "owl-theme",

    //Lazy load
    lazyLoad : false,
    lazyFollow : true,
    lazyEffect : "fade",

    //Auto height
    autoHeight : false,

    //JSON 
    jsonPath : false, 
    jsonSuccess : false,

    //Mouse Events
    dragBeforeAnimFinish : true,
    mouseDrag : true,
    touchDrag : true,

    //Transitions
    transitionStyle : false,

    // Other
    addClassActive : false,

    //Callbacks
    beforeUpdate : false,
    afterUpdate : false,
    beforeInit: false, 
    afterInit: false, 
    beforeMove: false, 
    afterMove: false,
    afterAction: false,
    startDragging : false
    afterLazyLoad : false

})
</pre>
              <h2>4. Custom Events</h2>
              <p>Owl Carousel handles a few basic events. Mainly use them for custom navigation. <a href="demos/custom.html">See Demo</a></p>

<pre class="pre-show prettyprint linenums">
"owl.prev" //Go to previous
"owl.next" //Go to next
"owl.play" //Autoplay, also this event accept autoPlay speed as second parameter
"owl.stop" //Stop
"owl.goTo" //goTo provided item
"owl.jumpTo" //jumpTo provided item. Without slide animation.
</pre>

              <h2>5. Owl Data methods</h2>
              <p>To use Owl Carousel $.data use delegate function.</p>
              
<pre class="pre-show prettyprint linenums">

//Initialize Plugin
$(".owl-carousel").owlCarousel()

//get carousel instance data and store it in variable owl
var owl = $(".owl-carousel").data('owlCarousel');

//Public methods
owl.next()   // Go to next slide
owl.prev()   // Go to previous slide
owl.goTo(x)  // Go to x slide
owl.jumpTo(x)  // Go to x slide without slide animation

//Auto Play
owl.play() // Autoplay
owl.stop() // Autoplay Stop

//Manipulation methods. <a href="demos/manipulations.html">See demo.</a>
owl.addItem(htmlString [,targetPosition])
owl.removeItem(targetPosition)
owl.destroy()
owl.reinit(newOptions)
</pre>
          </div>
        </div>
      </div>
    </div>

    <div id="faq" class="container">
      <div class="row">
        <div class="span12">
          <h1>FAQ</h1>
          <dl>
            <dt>Can i use it for free?</dt>
            <dd>Yes!</dd>
            <dt>Can i use it for ecommerce?</dt>
            <dd>Yes!</dd>
            <dt>Has it any licence?</dt>
            <dd>MIT</dd>
            <dt>Can i ask for a new functionality?</dt>
            <dd>Yes! Use <a href="https://github.com/OwlFonk/OwlCarousel">Github</a></dd>
            <dt>I need help!</dt>
            <dd>Send me an <a href="mailto:<EMAIL>?subject=Hey Owl! I need help">email</a>, visit <a href="https://github.com/OwlFonk/OwlCarousel">Github</a> or add comment <a href="#disqus">below.</a><br>Don't forget to add link to your demo/example website!</dd>
            <dt>Does it has infinity scroll/circle/loop slides?</dt>
            <dd>Sorry, no.</dd>
            <dt>What's new in latest release?</dt>
            <dd>See <a href="changelog.html">Changelog</a></dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="container disqus">
        <div class="row">
          <div class="span12">
            <h1>Disqus</h1>

    <div id="disqus_thread"></div>
    <noscript>Please enable JavaScript to view the <a href="http://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
    <a href="http://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>
      </div>
    </div>
  </div>

    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h5>Bartosz Wojciechowski 2013 / @OwlFonk / 
            <a href="mailto:<EMAIL>?subject=Hey Owl!">email</a> / 
            <a href="changelog.html">changelog</a> /
            <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=EFSGXZS7V2U9N">donate</a> / 
            <a href="https://twitter.com/share" class="twitter-share-button" data-url="http://owlgraphic.com/owlcarousel/" data-text="Awesome jQuery Owl Carousel Responsive Plugin" data-via="OwlFonk" data-count="none" data-hashtags="owlcarousel"></a>
            <script>
            var owldomain = window.location.hostname.indexOf("owlgraphic");
            if(owldomain !== -1){
              !function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');
            }
            </script>
            </h5>
          </div>
        </div>
      </div>
    </div>


    <script src="assets/js/jquery-1.9.1.min.js"></script>
    <script src="owl-carousel/owl.carousel.min.js"></script>

    <!-- Frontpage Demo -->
    <script>

    $(document).ready(function($) {
      $("#owl-example").owlCarousel();
    });


    $("body").data("page", "frontpage");

    </script>
    <script src="assets/js/bootstrap-collapse.js"></script>
    <script src="assets/js/bootstrap-transition.js"></script>

    <script src="assets/js/google-code-prettify/prettify.js"></script>
	  <script src="assets/js/application.js"></script>

    <script type="text/javascript">
    jQuery(function($){
      var disqus_loaded = false;
      var top = $("#faq").offset().top; 
      var owldomain = window.location.hostname.indexOf("owlgraphic");
      var comments = window.location.href.indexOf("comment");

      if(owldomain !== -1){
        function check(){
          if ( (!disqus_loaded && $(window).scrollTop() + $(window).height() > top) || (comments !== -1) ){
            $(window).off( "scroll" );
            disqus_loaded = true;
            /* * * CONFIGURATION VARIABLES: EDIT BEFORE PASTING INTO YOUR WEBPAGE * * */
            var disqus_shortname = 'owlcarousel'; // required: replace example with your forum shortname
            var disqus_identifier = 'OWL Carousel';
            //var disqus_url = 'http://owlgraphic.com/owlcarousel/';
            /* * * DON'T EDIT BELOW THIS LINE * * */
            (function() {
                var dsq = document.createElement('script'); dsq.type = 'text/javascript'; dsq.async = true;
                dsq.src = '//' + disqus_shortname + '.disqus.com/embed.js';
                (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(dsq);
            })();
          }
        }
        $(window).on( "scroll", check );
        check();
      } else {
        $('.disqus').hide();
      }
    });
    </script>

    <script>
    var owldomain = window.location.hostname.indexOf("owlgraphic");
    if(owldomain !== -1){
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

      ga('create', 'UA-41541058-1', 'owlgraphic.com');
      ga('send', 'pageview');
    }
    </script>

  </body>
</html>