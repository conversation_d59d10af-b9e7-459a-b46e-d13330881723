!function(a,b,c,d){"use strict";var e="datepaginator",f=function(b,c){this._element=b,this.$element=a(b),this._init(c)};f.defaults={fillWidth:!0,highlightSelectedDate:!0,highlightToday:!0,hint:"dddd, Do MMMM YYYY",injectStyle:!0,itemWidth:35,navItemWidth:20,offDays:"Sat,Sun",offDaysFormat:"ddd",onSelectedDateChanged:null,selectedDate:moment().clone().startOf("day"),selectedDateFormat:"YYYY-MM-DD",selectedItemWidth:140,showCalendar:!0,showOffDays:!0,showStartOfWeek:!0,size:d,startOfWeek:"Mon",startOfWeekFormat:"ddd",squareEdges:!1,text:"ddd<br/>Do",textSelected:"dddd<br/>Do, MMMM YYYY",useBootstrap2:!1,width:0},f.prototype={setSelectedDate:function(a,b){this._setSelectedDate(moment(a,b?b:this.options.selectedDateFormat)),this._render()},remove:function(){this._destroy(),a.removeData(this,"plugin_"+e)},_init:function(b){this.options=a.extend({},f.defaults,b),this.options.width?this.options.fillWidth=!1:(this.options.width=this.$element.width(),this.options.fillWidth=!0),"string"==typeof this.options.selectedDate&&(this.options.selectedDate=moment(this.options.selectedDate,this.options.selectedDateFormat).clone().startOf("day")),"small"===this.options.size?this.options.size="sm":"large"===this.options.size&&(this.options.size="lg"),this._destroy(),this._subscribeEvents(),this._render()},_unsubscribeEvents:function(){this.$element.off("click"),this.$element.off("selectedDateChanged")},_subscribeEvents:function(){this._unsubscribeEvents(),this.$element.on("click",a.proxy(this._clickedHandler,this)),"function"==typeof this.options.onSelectedDateChanged&&this.$element.on("selectedDateChanged",this.options.onSelectedDateChanged),this.options.fillWidth&&a(b).on("resize",a.proxy(this._resize,this))},_destroy:function(){this.initialized&&(this.$calendar&&this.$calendar.datepicker("remove"),this.$wrapper.remove(),this.$wrapper=null,this._unsubscribeEvents()),this.initialized=!1},_clickedHandler:function(b){b.preventDefault();var c=a(b.target),d=c.attr("class");-1!=d.indexOf("dp-nav-left")?this._back():-1!=d.indexOf("dp-nav-right")?this._forward():-1!=d.indexOf("dp-item")&&this._select(c.attr("data-moment"))},_setSelectedDate:function(a){!1===a.isSame(this.options.selectedDate)&&(this.options.selectedDate=a.startOf("day"),this.$element.trigger("selectedDateChanged",[a.clone()]))},_back:function(){this._setSelectedDate(this.options.selectedDate.clone().subtract("day",1)),this._render()},_forward:function(){this._setSelectedDate(this.options.selectedDate.clone().add("day",1)),this._render()},_select:function(a){this._setSelectedDate(moment(a,this.options.selectedDateFormat)),this._render()},_calendarSelect:function(a){this._setSelectedDate(moment(a.date)),this._render()},_resize:function(){this.options.width=this.$element.width(),this._render()},_render:function(){var b=this;this.initialized?this.$calendar&&this.$calendar.datepicker("remove"):(this.$element.addClass(this.options.useBootstrap2?"pagination":"").removeClass("datepaginator datepaginator-sm datepaginator-lg").addClass("sm"===this.options.size?"datepaginator-sm":"lg"===this.options.size?"datepaginator-lg":"datepaginator"),this.$wrapper=a(this._template.list),this.$leftNav=a(this._template.listItem).append(a(this._template.navItem).addClass("dp-nav-left").addClass("sm"===this.options.size?"dp-nav-sm":"lg"===this.options.size?"dp-nav-lg":"").addClass(this.options.squareEdges?"dp-nav-square-edges":"").append(a(this._template.icon).addClass("fa-angle-left").addClass("dp-nav-left")).width(this.options.navItemWidth)),this.$rightNav=a(this._template.listItem).append(a(this._template.navItem).addClass("dp-nav-right").addClass("sm"===this.options.size?"dp-nav-sm":"lg"===this.options.size?"dp-nav-lg":"").addClass(this.options.squareEdges?"dp-nav-square-edges":"").append(a(this._template.icon).addClass("fa-angle-right").addClass("dp-nav-right")).width(this.options.navItemWidth)),this.$calendar=this.options.showCalendar?a(this._template.calendar):d,this._injectStyle(),this.initialized=!0),this.$element.empty().append(this.$wrapper.empty()),this.$wrapper.append(this.$leftNav),a.each(this._buildData(),function(c,d){var e=a(b._template.dateItem).attr("data-moment",d.m).attr("title",d.hint).width(d.itemWidth);d.isSelected&&b.options.highlightSelectedDate&&e.addClass("dp-selected"),d.isToday&&b.options.highlightToday&&e.addClass("dp-today"),d.isStartOfWeek&&b.options.showStartOfWeek&&e.addClass("dp-divider"),d.isOffDay&&b.options.showOffDays&&e.addClass("dp-off"),d.isSelected&&b.options.showCalendar&&e.append(b.$calendar),"sm"===b.options.size?e.addClass("dp-item-sm"):"lg"===b.options.size&&e.addClass("dp-item-lg"),e.append(d.text),b.$wrapper.append(a(b._template.listItem).append(e))}),this.$wrapper.append(this.$rightNav),this.$calendar&&this.$calendar.datepicker({autoclose:!0,forceParse:!0,startView:0,minView:0,todayHighlight:!0}).datepicker("update",this.options.selectedDate.toDate()).on("changeDate",a.proxy(this._calendarSelect,this))},_injectStyle:function(){this.options.injectStyle&&!c.getElementById("bootstrap-datepaginator-style")&&a("<style type='text/css' id='bootstrap-datepaginator-style'> "+this._css+" </style>").appendTo("head")},_buildData:function(){for(var a=this.options.width-(this.options.selectedItemWidth-this.options.itemWidth+2*this.options.navItemWidth),b=Math.floor(a/this.options.itemWidth),c=parseInt(b/2),d=Math.floor(a/b),e=Math.floor(this.options.selectedItemWidth+(a-b*d)),f=moment().startOf("day"),g=this.options.selectedDate.clone().subtract("days",c),h=this.options.selectedDate.clone().add("days",b-c),i=new Array,j=g;j.isBefore(h);j.add("days",1))i[i.length]={m:j.clone().format(this.options.selectedDateFormat),isSelected:j.isSame(this.options.selectedDate)?!0:!1,isToday:j.isSame(f)?!0:!1,isOffDay:-1!==this.options.offDays.split(",").indexOf(j.format(this.options.offDaysFormat))?!0:!1,isStartOfWeek:-1!==this.options.startOfWeek.split(",").indexOf(j.format(this.options.startOfWeekFormat))?!0:!1,text:j.isSame(this.options.selectedDate)?j.format(this.options.textSelected):j.format(this.options.text),hint:j.format(this.options.hint),itemWidth:j.isSame(this.options.selectedDate)?e:d};return i},_template:{list:'<ul class="pagination"></ul>',listItem:"<li></li>",navItem:'<a href="#" class="dp-nav"></a>',dateItem:'<a href="#" class="dp-item"></a>',icon:'<i class="fa"></i>',calendar:'<i id="dp-calendar" class="glyphicon glyphicon-calendar"></i>'},_css:".datepaginator{font-size:12px;height:60px}.datepaginator-sm{font-size:10px;height:40px}.datepaginator-lg{font-size:14px;height:80px}.pagination{margin:0;padding:0;white-space:nowrap}.dp-nav{height:60px;padding:22px 0!important;width:20px;margin:0!important;text-align:center}.dp-nav-square-edges{border-radius:0!important}.dp-item{height:60px;padding:13px 0!important;width:35px;margin:0!important;border-left-style:hidden!important;text-align:center}.dp-item-sm{height:40px!important;padding:5px!important}.dp-item-lg{height:80px!important;padding:22px 0!important}.dp-nav-sm{height:40px!important;padding:11px 0!important}.dp-nav-lg{height:80px!important;padding:33px 0!important}a.dp-nav-right{border-left-style:hidden!important}.dp-divider{border-left:2px solid #ddd!important}.dp-off{background-color:#F0F0F0!important}.dp-today{background-color:#88B5DB!important;color:#fff!important}.dp-selected{background-color:#428bca!important;color:#fff!important;width:140px}#dp-calendar{padding:3px 5px 0 0!important;position:absolute;right:0;top:10}"};var g=function(a){b.console&&b.console.error(a)};a.fn[e]=function(b,c){return this.each(function(){var d=a.data(this,"plugin_"+e);"string"==typeof b?d?a.isFunction(d[b])&&"_"!==b.charAt(0)?("string"==typeof c&&(c=[c]),d[b].apply(d,c)):g("No such method : "+b):g("Not initialized, can not call method : "+b):d?d._init(b):a.data(this,"plugin_"+e,new f(this,b))})}}(jQuery,window,document);