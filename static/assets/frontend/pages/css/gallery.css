/* gallery page */
.gallery-item{
  padding-top: 15px;
  padding-bottom: 15px;
}
.gallery-item a {
  display: block;
  position: relative;
}
.gallery-item .zoomix {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;  
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  z-index: 90;
  background: #000;
  opacity: 0;
  filter: alpha(opacity = 0);

  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  
  -webkit-backface-visibility: hidden;
}
.ie7 .gallery-item .zoomix{
  clear: both;
}
.gallery-item a:hover .zoomix{
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity:0.4;  
  filter: alpha(opacity = 50);
}
.gallery-item .zoomix .fa-search {
  font-size: 25px;
  line-height: 25px;
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -12px 0 0 -12px;
}