a {
	color: #E02222;
}
a:hover {
	color: #E02222;
}
.pre-header a:hover {
	color: #E02222;
}
.shop-currencies a.current {
	color: #E02222;
}
.header-navigation ul > li.active > a,
.header-navigation ul > li > a:hover,
.header-navigation ul > li > a:focus,
.header-navigation ul > li.open > a,
.header-navigation ul > li.open > a:hover,
.header-navigation ul > li.open > a:focus {
	color: #e02222;
}
.header-navigation li.menu-search i:hover {
	color: #e02222;
}
.sidebar a:hover {
	color: #E02222;
}
.sidebar .dropdown.open .dropdown-toggle:hover {
	color: #E02222;
}
.sidebar-menu .dropdown-menu li > a:hover, .sidebar-menu .dropdown-menu li > a:focus, .sidebar-menu li.active > a, .sidebar-menu li.active > a:hover {
	color: #E02222;
}
.content-page a:hover, 
.sidebar2 a:hover {
	color: #E02222;
}
.content-page .link, .content-page .link:hover, .content-page .link:active {
	color: #E02222;
}
.page-404 .number,
.page-500 .number {
	color: #E02222;
}
.content-form-page a:hover {
	color: #E02222;
}
.quote-v1 a.btn-transparent:hover {
  background: #E02222;
}
.recent-work h2 a:hover {
	color: #E02222;
}
.recent-work .recent-work-item .fa:hover {
	color: #E02222;
}
.our-clients h2 a:hover {
	color: #E02222;
}
.front-team h3 strong {
  color: #E02222;
}
.ecommerce .header-navigation ul > li.active > a,
.ecommerce .header-navigation ul > li > a:hover,
.ecommerce .header-navigation ul > li > a:focus,
.ecommerce .header-navigation ul > li.open > a,
.ecommerce .header-navigation ul > li.open > a:hover,
.ecommerce .header-navigation ul > li.open > a:focus {
	color: #e02222;
}
.product-item h3 a:hover {
	color: #E02222;
}
.checkout-page a:hover {
	color: #E02222;
}


.langs-block-others:after { 
	border-bottom: 8px solid #e6400c;
}
.header-navigation > ul > li.dropdown:hover > a:after { 
  border-bottom: 8px solid #e6400c;
}
.header-navigation .dropdown-menu > li > a:hover,
.header-navigation .dropdown-menu > li.active > a,
.header-navigation .header-navigation-content .header-navigation-col li > a:hover,
.header-navigation .header-navigation-content .header-navigation-col li.active > a {
  background: #e6400c;
  color: #fff;
}
.header-navigation .dropdown-menu .header-navigation-content-ext li > a:hover,
.header-navigation .dropdown-menu .header-navigation-content-ext li.active > a {
  background: #fff;
  color: #e6400c;
}
.header-navigation .search-box:after { 
	border-bottom: 8px solid #e6400c;
}
.header-navigation .search-box {
	border-top: solid 2px #ea4c1d;
}
.title-wrapper h1 span {
	color: #e6400c;
}
.breadcrumb > .active {
	color: #e6400c;
}
.form-info h2 em {
	color: #e6400c;
}
.nav-tabs {
	border-color: #e6400c;
}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
	background: #e6400c;
}
.content-search h1 em {
	color: #e6400c;
}
.recent-work .recent-work-item:hover a.recent-work-description {
	background: #E6400C;
}
.testimonials-v1 blockquote:after { 
  background-color: #E6400C;
}
.testimonials-v1 span.testimonials-name {
	color: #E6400C;
}
.search-result-item h4 a {
	color: #E6400C;
}
.top-cart-content:after { 
	border-bottom: 8px solid #e6400c;
}
.goods-data a,
.checkout-page .checkout-description a {
	color: #e6400c;
}
.product-page .review a {
	color: #e6400c;
}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
    background: #E6400C;
    color: #fff;
}
.list-view-sorting a {
	background: #fff;
	color: #E6400C;
}

::-moz-selection {
  color: #fff;
  background: #e45000;
}
::selection {
  color: #fff;
  background: #e45000;
}
.steps-block ::-moz-selection {
  color: #e45000;
  background: #fff;
}
.steps-block ::selection {
  color: #e45000;
  background: #fff;
}

.owl-buttons .owl-prev:hover {
    background-color: #e84d1c;
}
.owl-buttons .owl-next:hover {
    background-color: #e84d1c;
}
.steps-block-red {
	background: #e84d1c;
}
.pre-footer .photo-stream img:hover {
	border-color: #E84D1C;
}
.pre-footer-light dl.f-twitter dd a {
	color: #e84d1c;
}
.pre-footer-light address a {
	color: #e84d1c;
}
.testimonials-v1 .left-btn:hover {
    background-color: #e84d1c;
}
.testimonials-v1 .right-btn:hover {
    background-color: #e84d1c;
}
.blog-tags li i,
.blog-info li i {
  color: #E84D1C;
}
.blog-posts .more,
.blog-sidebar .more {
	color: #E84D1C;
}
.recent-news h3 a {
	color: #E84D1C;
}
.blog-photo-stream li img:hover {
  border-color: #E84D1C;
}
.blog-tags li a:hover {
  color: #fff;
  background: #E84D1C;
}
.blog-tags li a:hover:after {
  border-left-color: #E84D1C;
}
.sidebar-categories li > a:hover,
.sidebar-categories li.active > a,
.sidebar-categories li.active:hover > a {
  color: #E84D1C;
}
.blog-item blockquote {
  border-color: #E84D1C;
}
.blog-item h4.media-heading span a {
    color: #E84D1C;
}
.front-steps-wrapper .front-step1 {
	background: #E84D1C;
}
.pricing-active {
  border: 3px solid #E84D1C;
  box-shadow: 7px 7px rgba(232, 77, 22, 0.2);
}
.pricing:hover {
  border: 3px solid #E84D1C;
}
.pricing:hover h4 {
  color: #E84D1C;
}
.pricing-head h3 {
  background: #E84D1C;
}
.pricing-head-active h4 {
  color: #E84D1C;
}
.pricing-content li i {
  color: #E84D1C;
}
.top-cart-block .fa-shopping-cart {
	background: #e84d1c;
}
.product-item .btn:hover {
	background: #e84d1c;
}
.pi-price {
	color: #e84d1c;
}
.product-item .add2cart:hover {
	color: #fff !important;
	background: #E84D1C !important;
	border-color: #E84D1C;
}
.goods-page-price strong, 
.goods-page-total strong,
.checkout-price strong,
.checkout-total strong {
	color: #e84d1c;
}
.shopping-total strong,
.checkout-total-block strong {
	color: #e84d1c;
}
.compare-item strong {
	color: #E84D1C;
}
.sidebar-products .price {
    color: #E84D1C;
}
.price-availability-block .price strong {
	color: #e84d1c;
}


.require {
	color: #e94d1c;
}
.content-form-page .form-control:focus {
	border: solid 1px #e94d1c;
}
.content-search input:focus {
	border: solid 1px #e94d1c;
}

.btn-primary {
	background: #e94d1c;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
	background: #cc3304;
}

.header-navigation .dropdown-menu > li:first-child {
    border-top: 2px solid #EA4C1D;
}
.front-steps-wrapper .front-step1:after {
    border-left: 15px solid #EC7049;
}

.del-goods:hover,
.add-goods:hover {
	background-color: #E94D1C;
}

.sidebar a:hover > .fa-angle-down {
	background-position: -11px 0;
}
.sidebar .collapsed:hover > .fa-angle-down {
	background-position: -11px -37px;
}
.top-cart-content {
	border-top: solid 2px #ea4c1d;
}

.front-skills .progress-bar {
  background: #EF4D2E;
}

.service-box-v1:hover {
  background: #d73d04;
}

.header .mobi-toggler:hover {
  background-color: #e34f00;
  border-color: #e34f00;
}

@media (max-width: 1024px) {
	.header .header-navigation li > a:hover,
	.header .header-navigation li.active > a,
	.header .header-navigation li.open > a:hover {
		color: #dd4632 !important;
	}
}

.faq-tabbable {
  border-left: solid 2px #e44f00;
}
.faq-tabbable li:hover a,
.faq-tabbable li.active a{
  background: #e44f00;
}
.faq-tabbable li.active:after {
  border-left: 6px solid #e44f00;
}

.mix-filter li:hover, .mix-filter li.active {
    background: #e44f00;
    color: #fff;
}
.mix-grid .mix .mix-details {
	background: #e44f00;
}
.mix-grid .mix a.mix-link, 
.mix-grid .mix a.mix-preview {
  background: #DB3A1B;
}
.langs-block-others {
	border-top: solid 2px #ea4c1d;
}
.brands .owl-buttons .owl-prev:hover {
    background-position: 18px -217px;
}
.brands .owl-buttons .owl-next:hover {
    background-position: -249px -217px;
}
.header-navigation ul > li.active > a/*,
.ecommerce .header-navigation ul > li.active > a*/ {
	border-bottom: 2px solid #e64f00;
}