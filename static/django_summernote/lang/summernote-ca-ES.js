(function ($) {
  $.extend($.summernote.lang, {
    'ca-ES': {
      font: {
        bold: 'Negreta',
        italic: 'Cursiva',
        underline: 'Subratllat',
        strikethrough: 'Ratllat',
        clear: 'Treure estil de lletra',
        height: 'Alçada de línia',
        size: 'Mida de lletra'
      },
      image: {
        image: 'Imatge',
        insert: 'Inserir imatge',
        resizeFull: 'Redimensionar a mida completa',
        resizeHalf: 'Redimensionar a la meitat',
        resizeQuarter: 'Redimensionar a un quart',
        floatLeft: 'Surar a l%27esquerra',
        floatRight: 'Surar a la dreta',
        floatNone: 'No surar',
        dragImageHere: 'Arrossegueu una imatge aquí',
        selectFromFiles: 'Seleccioneu des dels arxius',
        url: 'URL de la imatge'
      },
      link: {
        link: 'Enllaç',
       insert: 'Inserir enllaç',
        unlink: 'Treure enllaç',
        edit: 'Editar',
        textToDisplay: 'Text per mostrar',
        url: 'Cap a quina URL porta l\'enllaç?',
        openInNewWindow: 'Obrir en una finestra nova'
      },
      video: {
       video: 'Video',
        videoLink: 'Enllaç del video',
        insert: 'Inserir video',
        url: 'URL del video?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, o Youku)'
      },
      table: {
        table: 'Taula'
      },
      hr: {
        insert: 'Inserir línia horitzontal'
      },
      style: {
        style: 'Estil',
        normal: 'Normal',
        blockquote: 'Cita',
        pre: 'Codi',
        h1: 'Títol 1',
        h2: 'Títol 2',
        h3: 'Títol 3',
        h4: 'Títol 4',
        h5: 'Títol 5',
        h6: 'Títol 6'
      },
      lists: {
        unordered: 'Llista desendreçada',
        ordered: 'Llista endreçada'
      },
      options: {
        help: 'Ajut',
        fullscreen: 'Pantalla sencera',
        codeview: 'Veure codi font'
      },
      paragraph: {
        paragraph: 'Paràgraf',
        outdent: 'Menys tabulació',
        indent: 'Més tabulació',
        left: 'Alinear a l\'esquerra',
        center: 'Alinear al mig',
        right: 'Alinear a la dreta',
        justify: 'Justificar'
      },
      color: {
        recent: 'Últim color',
        more: 'Més colors',
       background: 'Color de fons',
        foreground: 'Color de lletra',
        transparent: 'Transparent',
        setTransparent: 'Establir transparent',
        reset: 'Restablir',
        resetToDefault: 'Restablir per defecte'
      },
      shortcut: {
        shortcuts: 'Dreceres de teclat',
        close: 'Tancar',
        textFormatting: 'Format de text',
        action: 'Acció',
        paragraphFormatting: 'Format de paràgraf',
        documentStyle: 'Estil del document'
      },
      history: {
        undo: 'Desfer',
        redo: 'Refer'
      }
    }
  });
})(jQuery);
