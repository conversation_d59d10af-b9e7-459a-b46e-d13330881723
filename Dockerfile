# Use Python 3.8 (última versão compatível com Django 1.9.5)
FROM --platform=linux/amd64 python:3.8-slim

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    libmariadb-dev \
    build-essential \
    pkg-config \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# Definir diretório de trabalho
WORKDIR /app

# Copiar apenas requirements primeiro (para cache do Docker)
COPY requirements.txt .

# Instalar dependências Python
RUN pip install --no-cache-dir -r requirements.txt

# Copiar apenas arquivos necessários
COPY manage.py .
COPY associado/ ./associado/
COPY associacao/ ./associacao/
COPY mailapp/ ./mailapp/
COPY templates/ ./templates/
COPY static/ ./static/
COPY samples/ ./samples/
COPY docker-entrypoint.sh .

# Copiar e dar permissão ao script de entrada
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Expor porta
EXPOSE 8000

# Comando padrão
CMD ["docker-entrypoint.sh"]
