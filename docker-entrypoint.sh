#!/bin/bash

# Aguardar o MySQL estar pronto
echo "Aguardando MySQL..."
while ! nc -z db 3306; do
  sleep 1
done
echo "MySQL está pronto!"

# Executar migrações
echo "Executando migrações..."
python manage.py migrate

# Criar superusuário se não existir
echo "Verificando superusuário..."
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superusu<PERSON>rio criado: admin/admin123')
else:
    print('Superusuário já existe')
"

# Executar servidor
echo "Iniciando servidor Django..."
exec python manage.py runserver 0.0.0.0:8000
