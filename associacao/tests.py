from django.test import TestCase, TransactionTestCase, RequestFactory
from .cieloprocessor import CieloProcess
from .PaymentProcessor import PaymentProcess
from .models import CieloData, Pessoa, Conta, Transacoes, Negocio, PlanosdePagamento, EvecomData, \
    Pedido, TemplateOrdemEvecom, IntegraTerceiros
from mailapp.models import Mail, MailTemplate
from django.contrib.auth.models import User
from django.test import Client
from .views import Index, ClientePlanosPagamentos, EditaPerfilProprioAssociado, \
    ClientePagar, Associados, PlanosPagamentos, ExtratoAssociacao, CancelaPagamento
from .forms import CartaoForm
from django.core.urlresolvers import reverse
from allauth.account.models import EmailAddress
from django.core.urlresolvers import reverse_lazy, reverse
import uuid

from .processes import OrderProcess


# Create your tests here.

#commeting because we dont use it anymore
# class CieloProcessTest(TestCase):
#     """
#     will test cielo processing
#     """
#
#     def test_failedpayment(self):
#         c = CieloData()
#         c.chave = '25fbb99741c739dd84d7b06ec78c9bac718838630f30b112d033ce2e621b34f3'
#         c.numero = **********
#         c.sandbox = True
#         c.ativo = True
#         c.producao = True
#         c.save()
#         c = CieloProcess()
#         p = Pedido.objects.create()
#         c.set_payment_data(****************, 201805, 1, 123, 'fulano')
#         c.set_pedido(p, valor=6066)
#         c.set_pagamento('visa', '1')
#         c.set_transacao()
#         x = c.executar_transacao()
#         try:
#             self.assertEquals(x.autorizacao.lr, 66)
#         except:
#             self.assertEquals(x.autorizacao.lr, 999)
#         self.assertEquals(x.autenticacao.codigo, 5)
#         self.assertEquals(c.final_status()['status'], 'Error')
#
#     def test_sucesspayment(self):
#         c = CieloData()
#         c.chave = '25fbb99741c739dd84d7b06ec78c9bac718838630f30b112d033ce2e621b34f3'
#         c.numero = **********
#         c.sandbox = True
#         c.ativo = True
#         c.producao = True
#         c.save()
#         c = CieloProcess()
#         P = Pedido.objects.create()
#         c.set_payment_data(****************, 201805, 1, 123, 'fulano')
#         c.set_pedido(P, valor=6000)
#         c.set_pagamento('visa', '1')
#         c.set_transacao()
#         x = c.executar_transacao()
#         try:
#             self.assertEquals(x.autorizacao.lr, 0)
#             self.assertEquals(x.autenticacao.codigo, 6)
#             self.assertEquals(c.final_status()['status'], 'OK')
#         except:
#             self.assertEquals(x.autorizacao.lr, 999)
#             self.assertEquals(x.autenticacao.codigo, 5)
#             self.assertEquals(c.final_status()['status'], 'Error')




class PaymentProcessorTest(TransactionTestCase):
    """
    will test payment processor
    """

    def setUp(self):
        """
        setup db for tests
        :return:
        """
        q = Negocio.objects.create(name='teste', logradouro='francisco', telefone='5183007878',
                                   complemento='77')
        q.save()

    # def test_PaymentProcessSuccess(self):
    #     """
    #     will test payment process
    #     :return:
    #     """
    #     c = CieloData()
    #     c.chave = '25fbb99741c739dd84d7b06ec78c9bac718838630f30b112d033ce2e621b34f3'
    #     c.numero = **********
    #     c.sandbox = True
    #     c.ativo = True
    #     c.producao = True
    #     c.save()
    #     c = CieloProcess()
    #     c.set_payment_data(****************, 202205, 1, 123, 'fulano')
    #     P = Pedido.objects.create()
    #     c.set_pedido(P, valor=6000)
    #     c.set_pagamento('visa', '1')
    #     c.set_transacao()
    #     x = c.executar_transacao()
    #     savequery = c.save_transaction()
    #     try:
    #         self.assertEquals(x.autorizacao.lr, 0)
    #         self.assertEquals(x.autenticacao.codigo, 6)
    #         self.assertEquals(c.final_status()['status'], 'OK')
    #         self.assertEquals(2, savequery.id)
    #     except:
    #         self.assertEquals(x.autorizacao.lr, 999)
    #         self.assertEquals(x.autenticacao.codigo, 5)
    #         self.assertEquals(c.final_status()['status'], 'Error')
    #         self.assertEquals(1, savequery.id)
    #
    #
    #
    #     user = User.objects.create_user(username='john',
    #                                     email='<EMAIL>',
    #                                     password='glass onion')
    #     user.save()
    #     pessoa = Pessoa()
    #     pessoa.email = user.email
    #     pessoa.complemento = 'super rua'
    #     pessoa.numero = 45
    #     pessoa.save()
    #     y = PaymentProcess()
    #     q = Negocio.objects.create(name='teste', logradouro='francisco', telefone='5183007878',
    #                                complemento='77')
    #     q.save()
    #     z = y.process(pessoa, x, q.id)
    #     self.assertEquals(c.inicializacao, True)
    #     # check operations ok
    #     self.assertEquals(z['status'], 'OK')
    #     # check saldo
    #     q = Conta.objects.get_or_create(usuario=pessoa)[0]
    #     self.assertEquals(q.saldo, 60.00)
    #     # check transactions numbers
    #     self.assertEquals(len(Transacoes.objects.all()), 1)

    def test_PaymentProcessFail(self):
        """
        will test failed payment
        :return:
        """
        x = CieloProcess()
        self.assertEquals(x.inicializacao, False)
        y = PaymentProcess()
        z = y.process(2, 'nada', 1)
        savequery = x.save_transaction()
        self.assertEquals(z['status'], 'Error')
        self.assertEquals(savequery.id, 1)


class AssociadoTest(TestCase):
    """
    main class for testing associado views
    """

    def setUp(self):
        """
        setup db for tests
        :return:
        """
        q = Negocio.objects.create(name='negocio_teste', logradouro='francisco', telefone='5183007878',
                                   complemento='77')
        q.save()
        user = User.objects.create_user(username='john',
                                        email='<EMAIL>',
                                        password='glass onion')
        self.logged_user = user
        user.save()
        pessoa = Pessoa()
        pessoa.email = user.email
        pessoa.complemento = 'super rua'
        pessoa.numero = 45
        pessoa.usuario = user
        pessoa.save()
        self.logged_user_pessoa = pessoa
        EmailAddress.objects.create(user=user, email=user.email, verified=True)

        planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1500,
                                                              nome_plano='SBAD', entidade=q)
        planopagamentotest.membros.add(pessoa)
        planopagamentotest.cielo_data
        self.uuid = uuid.uuid4()
        planopagamentotest.uuid_pagamento=self.uuid
        planopagamentotest.save()
        c = CieloData()
        c.chave = '25fbb99741c739dd84d7b06ec78c9bac718838630f30b112d033ce2e621b34f3'
        c.numero = '**********'
        c.sandbox = True
        c.ativo = True
        c.producao = True
        c.tipo_api = '1.5'
        c.save()
        planopagamentotest.cielo_data = c
        planopagamentotest.cielo_data.save()

    def test_nao_logado(self):
        """
        test not logged
        :return:
        """
        response = self.client.get('/')
        print(response.content)

        self.assertEquals(response.status_code, 200)
        self.assertContains(response, 'Resolvemos')

    def test_associado_login(self):
        """
        test associado login
        :return:
        """
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertEquals(200, response.status_code)
        # self.assertContains(response.content, '2016')
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)

    def test_associado_painel_planos(self):
        """
        test associado painel planos
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/clienteplanospagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePlanosPagamentos.as_view()(response)
        self.assertEquals(200, response.status_code)
        response.render()
        print(response.content)
        self.assertContains(response, 'SBAD')

    def test_associado_edit_profile(self):
        """
        test associado painel planos
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/1/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = EditaPerfilProprioAssociado.as_view()(response, pk=1)
        self.assertEquals(200, response.status_code)
        response.render()
        print(response.content)
        self.assertContains(response, 'super rua')

    def test_pagamento_usuario_mostrar(self):
        """
        will test pagamento fail without cielo test token
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)

    def test_pagamento_valid_form_mastercard(self):
        """
        test pagamento form
        :return:
        """
        pagamento_data = {'numero_cartao': '****************',
                          'expiracao_ano': '2022',
                          'codigo_seguranca': 123,
                          'nome_cartao': 'fulano de tal',
                          'expiracao_mes': '01',
                          'bandeira': 'Visa',
                          'tipo_escolhido': 0,
                          'Email': '<EMAIL>',
                          'CPF': '********',
                          'parcelas': 1}
        formtest = CartaoForm(data=pagamento_data)
        self.assertTrue(formtest.is_valid())

    def test_pagamento_valid_form_amex(self):
        """
        test pagamento form
        :return:
        """
        pagamento_data = {'numero_cartao': '***************',
                          'expiracao_ano': '2022',
                          'codigo_seguranca': 1234,
                          'nome_cartao': 'fulano de tal',
                          'expiracao_mes': '01',
                          'bandeira': 'Amex',
                          'tipo_escolhido': 0,
                          'CPF': '********',
                          'Email': '<EMAIL>',
                          'parcelas': 1}
        formtest = CartaoForm(data=pagamento_data)
        self.assertTrue(formtest.is_valid())

    def test_pagamento_valid_form_diners(self):
        """
        test pagamento form
        :return:
        """
        pagamento_data = {'numero_cartao': '**************',
                          'expiracao_ano': '2022',
                          'codigo_seguranca': 123,
                          'nome_cartao': 'fulano de tal',
                          'expiracao_mes': '01',
                          'bandeira': 'Diners',
                          'tipo_escolhido': 0,
                          'CPF': '********',
                          'Email': '<EMAIL>',
                          'parcelas': 1}
        formtest = CartaoForm(data=pagamento_data)
        self.assertTrue(formtest.is_valid())

    def test_pagamento_valid_form_jcb(self):
        """
        test pagamento form
        :return:
        """
        pagamento_data = {'numero_cartao': '****************',
                          'expiracao_ano': '2022',
                          'codigo_seguranca': 123,
                          'nome_cartao': 'fulano de tal',
                          'expiracao_mes': '01',
                          'bandeira': 'JCB',
                          'tipo_escolhido': 0,
                          'CPF': '********',
                          'Email': '<EMAIL>',
                          'parcelas': 1}
        formtest = CartaoForm(data=pagamento_data)
        self.assertTrue(formtest.is_valid())

    # def test_pagamento_sucesso(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     self.factory = RequestFactory()
    #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
    #                                 follow=True)
    #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
    #     response = self.factory.get('/cliente/pagamento/')
    #     response.user = self.logged_user
    #     response.user.pessoa = self.logged_user_pessoa
    #     response = ClientePagar.as_view()(response, uuid=self.uuid)
    #     self.assertEquals(200, response.status_code)
    #     response = self.client.post('/cliente/pagamento/1/', {'numero_cartao': '****************',
    #                                                           'expiracao_ano': '2022',
    #                                                           'expiracao_mes': '01',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Visa',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '999999"',
    #                                                           'Email': '<EMAIL>'
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')

    # def test_pagamento_sucesso_anonimo(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     # self.factory = RequestFactory()
    #     # response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'})
    #     # self.assertRedirects(response, '/', status_code=302, target_status_code=200)
    #     # response = self.factory.get('/cliente/pagamento/')
    #     # response.user = self.logged_user
    #     # response.user.pessoa = self.logged_user_pessoa
    #     # response = ClientePagar.as_view()(response, uuid=self.uuid)
    #     # self.assertEquals(200, response.status_code)
    #     response = self.client.post('/cliente/pagamento/1/', {'numero_cartao': '****************',
    #                                                           'expiracao_ano': '2022',
    #                                                           'expiracao_mes': '01',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Visa',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '999999"',
    #                                                           'Email': '<EMAIL>'
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')


class AssociacaoTest(TestCase):
    """
    main class for associacao tests
    """

    def setUp(self):
        """
        setup db for tests
        :return:
        """
        q = Negocio.objects.create(name='negocio_teste', logradouro='francisco', telefone='5183007878',
                                   complemento='77')
        q.save()
        self.associacao = q
        user = User.objects.create_user(username='john',
                                        email='<EMAIL>',
                                        password='glass onion')
        associado = User.objects.create_user(username='associado1',
                                             email='<EMAIL>',
                                             password='associado1')
        self.logged_user = user
        user.save()
        EmailAddress.objects.create(user=associado, email=associado.email, verified=True)
        EmailAddress.objects.create(user=user, email=user.email, verified=True)
        pessoa = Pessoa()
        pessoa.email = user.email
        pessoa.complemento = 'super rua'
        pessoa.numero = 45
        pessoa.usuario = user
        pessoa.cpf = '98761013'
        pessoa.admin_negocio = True
        pessoa.save()
        pessoa.negocio_set.add(q)
        pessoa.save()

        pessoa2 = Pessoa()
        pessoa2.email = associado.email
        pessoa2.complemento = 'super rua'
        pessoa2.numero = 45
        pessoa2.usuario = associado
        pessoa2.cpf = 333
        pessoa2.save()
        pessoa2.negocio_set.add(q)
        pessoa2.save()

        self.logged_user_pessoa = pessoa

        self.associacao_user_pessoa = pessoa2
        self.negocio = q
        Conta.objects.create(usuario=pessoa)

        planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1500,
                                                              nome_plano='SBAD', entidade=q)
        planopagamentotest.membros.add(pessoa)
        self.uuid = uuid.uuid4()
        planopagamentotest.uuid_pagamento=self.uuid
        planopagamentotest.save()
        c = CieloData()
        c.chave = '25fbb99741c739dd84d7b06ec78c9bac718838630f30b112d033ce2e621b34f3'
        c.numero = **********
        c.sandbox = True
        c.ativo = True
        c.producao = True
        c.negocio = q
        c.save()

    def test_associacao_index(self):
        """
        test index for associacao
        :return:

        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'associado1'},
                                    follow=True)
        print(response.context)
        self.assertTemplateUsed(response, 'associacao/paineldemo.html')
        #       self.assertRedirects(response, '/', status_code=302, target_status_code=200)

    def test_associacao_planos_mostrar(self):
        """
        will test pagamento fail without cielo test token
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        # #with fail
        # self.assertRaises('AttributeError', self.client.get('/planospagamento/'))
        # self.assertNotContains('SBAD', response.content)
        response = self.factory.get('/planospagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = PlanosPagamentos.as_view()(response)
        content = response.render()
        self.assertEqual(content.context_data['page_description'], 'Planos de Pagamento')

        response = self.factory.get('/planospagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa

        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)

        content = response.render()
        print(content.content)

        self.assertContains(content, 'SBAD')

    def test_associacao_associados_mostrar(self):
        """
        main test to show associados in associacao
        :return:

        """
        self.factory = RequestFactory()
        print(len(EmailAddress.objects.all()))
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'associado1'},
                                    follow=True)

        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/associados/')
        response.user = self.associacao
        response.user.pessoa = self.associacao_user_pessoa
        response.user.pessoa.admin_negocio = True
        response.user.pessoa.negocio_set = [self.negocio]
        response = Associados.as_view()(response)
        # self.assertEquals(200, response.status_code)
        content = response.render()
        print(content.content)
        self.assertContains(content, '<EMAIL>')

    # def test_associacao_extrato_mostrar(self):
    #     """
    #     main test to show associados in associacao
    #     :return:
    #
    #     """
    #     self.factory = RequestFactory()
    #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'associado1'},
    #                                 follow=True)
    #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
    #     response = self.factory.get('/associacaoextrato/')
    #     response.user = self.associacao
    #     response.user.pessoa = self.associacao_user_pessoa
    #     response.user.pessoa.admin_negocio = True
    #     response.user.pessoa.negocio_set = [self.negocio]
    #     response = ExtratoAssociacao.as_view()(response)
    #     # self.assertEquals(200, response.status_code)
    #     content = response.render()
    #     print(content.content)
    #     self.assertNotContains(content, '<EMAIL>')
    #     response = self.client.post('/cliente/pagamento/1/', {'numero_cartao': '****************',
    #                                                           'expiracao_ano': '2022',
    #                                                           'expiracao_mes': '01',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Visa',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '999999"',
    #                                                           'Email': '<EMAIL>'
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')
    #     response = self.factory.get('/associacaoextrato/')
    #     response.user = self.associacao
    #     response.user.pessoa = self.associacao_user_pessoa
    #     response.user.pessoa.admin_negocio = True
    #     response.user.pessoa.negocio_set = [self.negocio]
    #     response = ExtratoAssociacao.as_view()(response)
    #
    #     content = response.render()
    #     print(content.content)
    #     self.assertContains(content, '1500,00')


"""
tests for CIelo Homologation 1.5
"""


# class CieloTest(TestCase):
#     """
#     main class for testing associado views
#     """
#
#     def setUp(self):
#         """
#         setup db for tests
#         :return:
#         """
#         q = Negocio.objects.create(name='negocio_teste', logradouro='francisco', telefone='5183007878',
#                                    complemento='77')
#         q.save()
#         user = User.objects.create_user(username='john',
#                                         email='<EMAIL>',
#                                         password='glass onion')
#         self.logged_user = user
#         user.save()
#         pessoa = Pessoa()
#         pessoa.email = user.email
#         pessoa.complemento = 'super rua'
#         pessoa.numero = 45
#         pessoa.usuario = user
#         pessoa.save()
#         self.logged_user_pessoa = pessoa
#
#         planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1500,
#                                                               nome_plano='SBAD', entidade=q)
#         planopagamentotest.membros.add(pessoa)
#         planopagamentotest.save()
#         c = CieloData()
#         c.chave = '25fbb99741c739dd84d7b06ec78c9bac718838630f30b112d033ce2e621b34f3'
#         c.numero = **********
#         c.sandbox = True
#         c.ativo = True
#         c.producao = True
#         c.negocio = q
#         c.save()
#         EmailAddress.objects.create(user=user, email=user.email, verified=True)
#
#     def test_pagamento_valid_form(self):
#         """
#         test pagamento form
#         :return:
#         """
#         pagamento_data = {'numero_cartao': '****************',
#                           'expiracao_ano': '2022',
#                           'codigo_seguranca': 123,
#                           'nome_cartao': 'fulano de tal',
#                           'expiracao_mes': '01',
#                           'bandeira': 'Master',
#                           'tipo_escolhido': 0,
#                           'CPF': '********',
#                           'Email': '<EMAIL>'}
#         formtest = CartaoForm(data=pagamento_data)
#         self.assertTrue(formtest.is_valid())
#
#     def test_pagamento_sucesso_mastercard(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Master',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_visa(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_elo(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Elo',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_dinners(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '**************',
#                                                               'expiracao_ano': '2017',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Diners',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_amex(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '***************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '1234',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Amex',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         print(response.content)
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_discover(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Discover',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_jcb(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'JCB',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_aura(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/cliente/pagamento/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ClientePagar.as_view()(response, uuid=self.uuid)
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '5078601912345600019',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Aura',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'autorizada')
#
#     def test_pagamento_sucesso_anonimo_mastercard(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Master',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         print(response.content)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#
#     def test_pagamento_sucesso_anonimo_visa(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/', status_code=302)
#
#     def test_pagamento_sucesso_anonimo_amex(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '***************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '1234',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Amex',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#
#     def test_pagamento_sucesso_anonimo_diners(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '**************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Diners',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#
#     def test_pagamento_sucesso_anonimo_elo(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Elo',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#
#     def test_pagamento_sucesso_anonimo_discover(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Discover',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#
#     def test_pagamento_sucesso_anonimo_jcb(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'JCB',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#
#     def test_pagamento_sucesso_anonimo_aura(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '5078601912345600019',
#                                                               'expiracao_ano': '2022',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Aura',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '999999"',
#                                                               'Email': '<EMAIL>'
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')


######
######
#cielo 3.0

class CieloTest30(TestCase):
    """
    main class for testing associado views
    """


    def setUp(self):
        """
        setup db for tests
        :return:
        """
        I = IntegraTerceiros.objects.create(nome='radio',url_destino='127.0.0.1', envia_rest=True)
        q = Negocio.objects.create(name='negocio_teste', logradouro='francisco', telefone='5183007878',
                                   complemento='77', email='<EMAIL>')
        y = TemplateOrdemEvecom.objects.create(nome_plano='World Congress on Brain, Behavior and Emotions',
                                               cor_evento='green-jungle',
                                               logo_evento='/static/media/logos/AF-Marca-brain-2017-6-estrelas.png',
                                               mensagem_rodape='World Congress on Brain, Behavior and Emotions - Verified by Planopago')
        q.template_evecom = y
        q.save()
        user = User.objects.create_user(username='john',
                                        email='<EMAIL>',
                                        password='glass onion')
        self.logged_user = user
        user.save()
        pessoa = Pessoa()
        pessoa.email = user.email
        pessoa.complemento = 'super rua'
        pessoa.numero = 45
        pessoa.usuario = user
        pessoa.save()
        EmailAddress.objects.create(user=user, email=user.email, verified=True)
        self.logged_user_pessoa = pessoa

        planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1500,
                                                              nome_plano='SBAD', entidade=q)
        planopagamentotest.membros.add(pessoa)
        self.uuid = uuid.uuid4()
        planopagamentotest.uuid_pagamento=self.uuid
        planopagamentotest.save()
        c = CieloData()
        c.chave = 'NUOTVDZZHQQYSWWOWPLEFRDTAHGILDICXMBVUFKU'
        c.numero = '58c875db-33d4-4406-8178-ba5db1a07e32'
        c.sandbox = True
        c.ativo = True
        c.producao = False
        c.tipo_api = '3.0'
        c.nome = 'ccm'
        c.save()
        q.admin = pessoa
        q.save()
        pessoa.admin_negocio = True
        pessoa.save()
        planopagamentotest.cielo_data = c
        planopagamentotest.save()
        planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1500,
                                                              nome_plano='SBAD', entidade=q, parcelas=3)
        planopagamentotest.membros.add(pessoa)
        self.uuid2 = uuid.uuid4()
        planopagamentotest.uuid_pagamento = self.uuid2
        planopagamentotest.cielo_data = c

        planopagamentotest.save()

        ####criar mail templates
        m = MailTemplate.objects.create(
            html='mail/mailtemplatebrain.html',
            txt='mail/mailtemplatebrain.txt',
            situacao="pagamento")
        m.subject = 'Obrigado'
        m.from_email = '<EMAIL>'
        m.save()
        m1 = MailTemplate.objects.create(
            html='mail/cancelamento.html',
            txt='mail/cancelamento.txt',
            situacao="cancelamento")
        m1.subject = 'Cancelado'
        m1.from_email = '<EMAIL>'
        m1.save()

    def test_pagamento_valid_form(self):
        """
        test pagamento form
        :return:
        """
        pagamento_data = {'numero_cartao': '****************',
                          'expiracao_ano': '2022',
                          'codigo_seguranca': 123,
                          'nome_cartao': 'fulano de tal',
                          'expiracao_mes': '01',
                          'bandeira': 'Visa',
                          'tipo_escolhido': 0,
                          'CPF': '********',
                          'Email': '<EMAIL>',
                          'parcelas': 3,}
        formtest = CartaoForm(data=pagamento_data)
        self.assertTrue(formtest.is_valid())

    def test_pagamento_sucesso_mastercard(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, ' Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_naoautorizado_mastercard(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Not Authorized')

    def test_pagamento_naoautorizado_cancelado(self):
        """
        test pagamento sucesso
        :return:
        """
        print('testando')
        print('testando', '/cliente/pagamento/{0}/'.format(self.uuid))
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        print('testando','/cliente/pagar/{0}/'.format(self.uuid))
        response = self.client.post('/cliente/pagar/{0}/'.format(self.uuid), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Card Canceled')

    def test_pagamento_naoautorizado_problemacartao(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post('/cliente/pagar/{0}/'.format(self.uuid), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Problems with Creditcard')

    def test_pagamento_naoautorizado_problemacartaobloqueado(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post('/cliente/pagar/{0}/'.format(self.uuid), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Blocked Card')

    def test_pagamento_naoautorizado_expirado(self):
        """
            test pagamento sucesso
            :return:
            """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post('/cliente/pagar/{0}/'.format(self.uuid), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Card Expired')

    def test_pagamento_naoautorizado_timeout(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Timeout')

    def test_pagamento_sucesso_visa(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, ' Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_elo(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Elo',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, ' Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_dinners(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2029',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Diners',
                                                              'tipo_escolhido': '0',
                                                              'Email': '<EMAIL>',
                                                              'CPF': '93991286034',
                                                              'parcelas': 3,
                                                              }, follow=True)
        print(response)
        print(response.content)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, ' Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_amex(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '1234',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Amex',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        print(response.content)
        self.assertContains(response, 'Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_discover(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Discover',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, ' Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_jcb(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'JCB',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_aura(self):
        """
        test pagamento sucesso
        :return:
        """
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/cliente/pagamento/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ClientePagar.as_view()(response, uuid=self.uuid)
        self.assertEquals(200, response.status_code)
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Aura',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        self.assertContains(response, 'Sucesso')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_mastercard(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_visa(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Visa',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_amex(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '1234',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Amex',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_diners(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '**************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '05',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Diners',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_elo(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Elo',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_discover(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Discover',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_jcb(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'JCB',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_pagamento_sucesso_anonimo_aura(self):
        """
        test pagamento sucesso
        :return:
        """
        response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
                                                              'expiracao_ano': '2022',
                                                              'expiracao_mes': '01',
                                                              'codigo_seguranca': '123',
                                                              'nome_cartao': 'fulano',
                                                              'bandeira': 'Aura',
                                                              'tipo_escolhido': '0',
                                                              'CPF': '999999"',
                                                              'Email': '<EMAIL>',
                                                              'parcelas': 3,
                                                              }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_integracao_evecom_visualizar_dados_brain(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = self.client.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data)
        self.assertContains(response, 'FREDERICO')
        self.assertContains(response, '530.00')

    def test_integracao_evecom_pagar_brain_falha(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        c = Client()
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
        self.assertContains(response, 'FREDERICO')
        self.assertContains(response, '530.00')
        print(c.session)
        print(c.cookies)

        response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
159&submit.y=28""", {'numero_cartao': '****************',
                     'expiracao_ano': '2022',
                     'expiracao_mes': '01',
                     'codigo_seguranca': '123',
                     'nome_cartao': 'fulano',
                     'bandeira': 'Visa',
                     'tipo_escolhido': '0',
                     'CPF': '999999"',
                     'Email': '<EMAIL>',
                     'parcelas': 3,
                     }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = EvecomData.objects.all().filter(business='<EMAIL>', paid=True)
        print(q, len(q))
        self.assertEquals(len(q), 0)
        self.assertContains(response, 'Falha')

    def test_extrato_evecom_pagar_brain_sucesso(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        c = Client()
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
        self.assertContains(response, 'FREDERICO')
        self.assertContains(response, '530.00')
        print(c.session)
        print(c.cookies)

        response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
159&submit.y=28""", {'numero_cartao': '****************',
                     'expiracao_ano': '2022',
                     'expiracao_mes': '01',
                     'codigo_seguranca': '123',
                     'nome_cartao': 'fulano',
                     'bandeira': 'Visa',
                     'tipo_escolhido': '0',
                     'CPF': '999999"',
                     'Email': '<EMAIL>',
                     'parcelas': 3,
                     }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = EvecomData.objects.all().filter(business='<EMAIL>')
        print(q, len(q))
        self.assertEquals(len(q), 1)
        self.assertContains(response, 'Sucesso')
        self.assertEquals(q[0].amount, '2.73')
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/associacaoextrato/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ExtratoAssociacao.as_view()(response)

        self.assertEquals(200, response.status_code)
        content = response.render()
        print(content.content)
        self.assertContains(content, '99999')
        self.assertContains(content, '2,73')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
        q = Transacoes.objects.all().last()
        c = self.client.get(reverse('comprovante', kwargs={'pedido': str(q.pedido_set.all()[0].num_pedido)}))
        self.assertContains(c, 'negocio_teste')

    def test_integracao_evecom_pagar_brain_sucesso(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        c = Client()
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)

        self.assertContains(response, 'FREDERICO')
        self.assertContains(response, '530.00')
        print(c.session)
        print(c.cookies)

        response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
159&submit.y=28""", {'numero_cartao': '****************',
                     'expiracao_ano': '2022',
                     'expiracao_mes': '01',
                     'codigo_seguranca': '123',
                     'nome_cartao': 'fulano',
                     'bandeira': 'Visa',
                     'tipo_escolhido': '0',
                     'CPF': '999999"',
                     'Email': '<EMAIL>',
                     'parcelas': 3,
                     }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = EvecomData.objects.all().filter(business='<EMAIL>')
        print(q, len(q))
        self.assertEquals(len(q), 1)
        self.assertContains(response, 'Sucesso')
        self.assertEquals(q[0].amount, '2.73')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')

    def test_cancelamento_evecom_pagar_brain_sucesso(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        c = Client()
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
        self.assertContains(response, 'FREDERICO')
        self.assertContains(response, '530.00')
        self.assertContains(response, 'World Congress on Brain, Behavior and Emotions - Verified by Planopago')
        print(c.session)
        print(c.cookies)

        response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
159&submit.y=28""", {'numero_cartao': '****************',
                     'expiracao_ano': '2022',
                     'expiracao_mes': '01',
                     'codigo_seguranca': '123',
                     'nome_cartao': 'fulano',
                     'bandeira': 'Visa',
                     'tipo_escolhido': '0',
                     'CPF': '999999"',
                     'Email': '<EMAIL>',
                     'parcelas': 1,
                     }, follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = EvecomData.objects.all().filter(business='<EMAIL>')
        print(q, len(q))
        self.assertEquals(len(q), 1)
        self.assertContains(response, 'Sucesso')
        self.assertEquals(q[0].amount, '2.73')
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/associacaoextrato/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ExtratoAssociacao.as_view()(response)

        self.assertEquals(200, response.status_code)
        content = response.render()
        print(content.content)
        self.assertContains(content, '99999')
        self.assertContains(content, '2,73')
        response = self.factory.get('/associacao/pagamento/cancelar/1/', follow=True)
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = CancelaPagamento.as_view()(response, pk=1)

        response = self.factory.get('/associacaoextrato/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ExtratoAssociacao.as_view()(response)
        content = response.render()
        print('content cancelamento', content.content)
        # self.assertRedirects(content, '/associacaoextrato/')
        self.assertContains(content, '2,73')
        self.assertContains(content, 'Cancelada')

    def test_extrato_evecom_pagar_radioterapia_link_unico(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        c = Client()
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = c.get(reverse('clientepagartab', args=[self.uuid]), data=data, follow=True)
        self.assertContains(response, 'SBAD')
        self.assertContains(response, '1500')
        print(c.session)
        print(c.cookies)

        response = c.post("""/cliente/pagamentotab/{0}/""".format(self.uuid),
                          dict(numero_cartao='****************', expiracao_ano='2022', expiracao_mes='01',
                               codigo_seguranca='123', nome_cartao='fulano', bandeira='Visa', tipo_escolhido='0',
                               CPF='999999"', Email='<EMAIL>', parcelas=1, nome_beneficiario=''), follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = EvecomData.objects.all().filter(business='<EMAIL>')
        print(q, len(q))
        self.assertEquals(len(q), 1)
        self.assertContains(response, 'Sucesso')
        self.assertEquals(q[0].amount, '1500.00')
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/associacaoextrato/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ExtratoAssociacao.as_view()(response)

        self.assertEquals(200, response.status_code)
        content = response.render()
        print(content.content)
        self.assertContains(content, '99999')
        self.assertContains(content, '1500,00')
        q = Transacoes.objects.all().last()
        print('chave usada', q)
        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
        self.assertEquals(q.cielo_transactions.parcelas, 1)
        q = Transacoes.objects.all().last()
        c = self.client.get(reverse('comprovante', kwargs={'pedido': str(q.pedido_set.all()[0].num_pedido)}))
        self.assertContains(c, 'negocio_teste')

    def test_extrato_evecom_pagar_radioterapia_link_unico_parcelado(self):
        """
        will check if GET parameter get everything right
        <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
        </form>
        clientepagartabCCM
        :return:
        """
        c = Client()
        data = {'business': '<EMAIL>',
                'invoice': '243361_1',
                'item_name': "243361 - FREDERICO CORREA DA SILVA - "
                             "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
                'amount': '530.00'}
        response = c.get(reverse('clientepagartab', args=[self.uuid]), data=data, follow=True)
        self.assertContains(response, 'SBAD')
        self.assertContains(response, '1500')
        self.assertContains(response, 'parcelas')
        print(c.session)
        print(c.cookies)

        response = c.post("""/cliente/pagamentotab/{0}/""".format(self.uuid),
                          dict(numero_cartao='****************', expiracao_ano='2022', expiracao_mes='01',
                               codigo_seguranca='123', nome_cartao='fulano', bandeira='Visa', tipo_escolhido='0',
                               CPF='999999"', Email='<EMAIL>', parcelas=3, nome_beneficiario=''), follow=True)
        self.assertRedirects(response, '/cliente/resultadopagamento/')
        q = EvecomData.objects.all().filter(business='<EMAIL>')
        print(q, len(q))
        self.assertEquals(len(q), 1)
        self.assertContains(response, 'Sucesso')
        self.assertEquals(q[0].amount, '1500.00')
        self.factory = RequestFactory()
        response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
                                    follow=True)
        self.assertRedirects(response, '/', status_code=302, target_status_code=200)
        response = self.factory.get('/associacaoextrato/')
        response.user = self.logged_user
        response.user.pessoa = self.logged_user_pessoa
        response = ExtratoAssociacao.as_view()(response)

        self.assertEquals(200, response.status_code)
        content = response.render()
        print(content.content)
        self.assertContains(content, '99999')
        self.assertContains(content, '1500,00')
        q = Transacoes.objects.all().last()
        print('chave usada', q)

        self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
        self.assertEquals(q.cielo_transactions.parcelas, 3)
        q = Transacoes.objects.all().last()
        c = self.client.get(reverse('comprovante', kwargs={'pedido': str(q.pedido_set.all()[0].num_pedido)}))
        self.assertContains(c, 'negocio_teste')

