from django.test import TestCase, TransactionTestCase, RequestFactory
from .cieloprocessor import CieloProcess
from .PaymentProcessor import PaymentProcess
from .models import *
from mailapp.models import Mail, MailTemplate
from django.contrib.auth.models import User
from django.test import Client
from .views import Index, ClientePlanosPagamentos, EditaPerfilProprioAssociado, \
    ClientePagar, Associados, PlanosPagamentos, ExtratoAssociacao, CancelaPagamento, ClientePagarTabAssinatura
from .forms import CartaoForm
from django.core.urlresolvers import reverse
from allauth.account.models import EmailAddress
from django.core.urlresolvers import reverse_lazy, reverse
from django.core.management import call_command
from datetime import datetime
import uuid, pagarme

from .processes import OrderProcess


# class CieloTest30Recurrent(TestCase):
#     """
#     main class for testing associado views
#     """
#
#     def setUp(self):
#         """
#         setup db for tests
#         :return:
#         """
#         q = Negocio.objects.create(name='negocio_teste', logradouro='francisco', telefone='**********',
#                                    complemento='77', email='<EMAIL>')
#
#         y = TemplateOrdemEvecom.objects.create(nome_plano='World Congress on Brain, Behavior and Emotions',
#                                                cor_evento='green-jungle',
#                                                logo_evento='/static/media/logos/AF-Marca-brain-2017-6-estrelas.png')
#         q.template_evecom = y
#         q.save()
#         user = User.objects.create_user(username='john',
#                                         email='<EMAIL>',
#                                         password='glass onion')
#         self.logged_user = user
#         user.save()
#         pessoa = Pessoa()
#         pessoa.email = user.email
#         pessoa.complemento = 'super rua'
#         pessoa.numero = 45
#         pessoa.usuario = user
#         pessoa.save()
#         self.logged_user_pessoa = pessoa
#         EmailAddress.objects.create(user=user, email=user.email, verified=True)
#
#         planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1500,
#                                                               nome_plano='SBAD', entidade=q, assinatura=True, assinatura_trial_days=30, trial=True)
#         # plan_data = {
#         #     "amount": "15000",
#         #     "days": "365",
#         #     "name": "BrainTV Anual",
#         # "Installments": "1"
#         # }
#         #pagarme.authentication_key('**************************************')
#         #plan = pagarme.plan.create(plan_data)
#         planopagamentotest.assinatura_no_backend_1x='564077'
#         planopagamentotest.save()
#
#
#         planopagamentotest.membros.add(pessoa)
#         self.uuid = uuid.uuid4()
#         planopagamentotest.uuid_pagamento=self.uuid
#         planopagamentotest.save()
#         c = CieloData()
#         c.chave = 'NUOTVDZZHQQYSWWOWPLEFRDTAHGILDICXMBVUFKU'
#         c.numero = 'ak_test_IsALVWB0We7Qj5bx2Llz43m1peTz8B'
#         c.sandbox = True
#         c.ativo = True
#         c.producao = False
#         c.tipo_api = 'stone'
#         c.nome = 'ccm'
#         c.save()
#         q.admin = pessoa
#         q.save()
#         pessoa.admin_negocio = True
#         pessoa.save()
#         planopagamentotest.cielo_data = c
#         planopagamentotest.save()
#
#         ####criar mail templates
#         m = MailTemplate.objects.create(
#           html='mail/mailtemplatebrain.html',
#           txt='mail/mailtemplatebrain.txt',
#           situacao="pagamento")
#         m.subject = 'Obrigado'
#         m.from_email = '<EMAIL>'
#         m.save()
#
#         m2 = MailTemplate.objects.create(
#             html='mail/confirmacaobraintv.html',
#             txt='mail/confirmacaobraintv.txt',
#             situacao="assinatura")
#         m2.subject = 'Obrigado'
#         m2.from_email = '<EMAIL>'
#         m2.save()
#
#         m1 = MailTemplate.objects.create(
#             html='mail/cancelamento.html',
#             txt='mail/cancelamento.txt',
#             situacao="cancelamento")
#         m1.subject = 'Cancelado'
#         m1.from_email = '<EMAIL>'
#         m1.save()
#
#         # criar outro convenio
#
#         q = Negocio.objects.create(name='negocio_teste2', logradouro='francisco', telefone='**********',
#                                    complemento='77', email='<EMAIL>')
#         y = TemplateOrdemEvecom.objects.create(nome_plano='World Congress on Brain, Behavior and Emotions',
#                                                cor_evento='green-jungle',
#                                                logo_evento='/static/media/logos/AF-Marca-brain-2017-6-estrelas.png')
#         q.template_evecom = y
#         q.save()
#         user = User.objects.create_user(username='john2',
#                                         email='<EMAIL>',
#                                         password='glass onion')
#         user.save()
#         pessoa = Pessoa()
#         pessoa.email = user.email
#         pessoa.complemento = 'super rua'
#         pessoa.numero = 45
#         pessoa.usuario = user
#         pessoa.save()
#         q.admin = pessoa
#         q.save()
#         pessoa.admin_negocio = True
#         pessoa.save()
#         EmailAddress.objects.create(user=user, email=user.email, verified=True)
#         # MerchantId: ad0e4615 - e641 - 42c7 - bd0a - 9f4251f3bf0f
#         # MerchantKey: KUMXSCOXVJJYYOCAESLJMWXMILYHDGSNKLWILRUH
#         c2 = CieloData()
#         c2.chave = 'KUMXSCOXVJJYYOCAESLJMWXMILYHDGSNKLWILRUH'
#         c2.numero = 'ad0e4615-e641-42c7-bd0a-9f4251f3bf0f'
#         c2.sandbox = True
#         c2.ativo = True
#         c2.producao = False
#         c2.tipo_api = '3.0'
#         c2.nome = 'mfm'
#         c2.save()
#         planopagamentotest = PlanosdePagamento.objects.create(frequencia_intervalo='Y', frequencia=1, montante=1600,
#                                                               nome_plano='SBAD2', entidade=q, assinatura=True, trial=False)
#         planopagamentotest.assinatura_no_backend_1x='564078'
#         planopagamentotest.membros.add(pessoa)
#         planopagamentotest.cielo_data = c
#         self.uuid2 = uuid.uuid4()
#         planopagamentotest.uuid_pagamento = self.uuid2
#         planopagamentotest.assinatura = True
#         planopagamentotest.trial= False
#         planopagamentotest.save()
#         ##criar cielo para debito real
#         c = CieloData()
#         c.chave = 'JfAvwOI4POCB8Mmb5XvfpzZuWzIMJHqtwWqQcA9T'
#         c.numero = 'ak_test_IsALVWB0We7Qj5bx2Llz43m1peTz8B'
#         c.sandbox = False
#         c.ativo = True
#         c.producao = True
#         c.tipo_api = 'stone'
#         c.nome = 'ccmd'
#         c.save()
#         self.mes = {1: 'Jan', 2: 'Fev', 3: 'Mar', 4: 'Abr', 5: 'Mai', 6: 'Jun', 7: 'Jul',
#          8: 'Ago', 9: 'Set', 10: 'Out', 11: 'Nov', 12: 'Dez'}
#
#     def test_pagamento_valid_form(self):
#         """
#         test pagamento form
#         :return:
#         """
#         pagamento_data = {'numero_cartao': '****************',
#                           'expiracao_ano': '2026',
#                           'codigo_seguranca': 123,
#                           'nome_cartao': 'fulano de tal',
#                           'expiracao_mes': '01',
#                           'bandeira': 'Visa',
#                           'tipo_escolhido': 0,
#                           'CPF': '***********99',
#                           'Email': '<EMAIL>',
#                           'parcelas': 1}
#         formtest = CartaoForm(data=pagamento_data)
#         self.assertTrue(formtest.is_valid())
#
#     # def test_pagamento_sucesso_mastercard(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     response = self.client.get(reverse_lazy('clientepagarassinatura',args=['ccm', self.uuid]))
#     #     print('aqui vai o reverse',reverse_lazy('clientepagarassinatura',args=['ccm', self.uuid]))
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm', self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Master',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     self.assertContains(response, 'Total')
#     #     self.assertTemplateUsed(response, '../templates/resultadoassinaturas/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #     # agora com o segundo plano na segunda chave
#     #     # go
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, convenio='ccm',plano=self.uuid2)
#     #     self.assertEquals(200, response.status_code)
#     #     content = response.render()
#     #     print('conteudo do primeiro get', content)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid2]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Master',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     print(response.content)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     q = TokenAssinante.objects.last()
#     #     t = q.transacoes.last()
#     #
#     #     print('chave usada', q)
#     #     self.assertEquals(q.nome_gateway, 'ccm')
#     #     self.assertEquals(t.montante, 1600.00)
#     #     self.assertIsNotNone(t.cielo_transactions.recurrentpayment_id)
#     #     print(response.content)
#     #     self.assertContains(response, 'Total')
#     #
#     # def test_pagamento_sucesso_visa(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Visa',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     self.assertContains(response, 'Total')
#     #     self.assertTemplateUsed(response, '../templates/resultadoassinaturas/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagar.as_view()(response, uuid=self.uuid2)
#     #     self.assertEquals(200, response.status_code)
#     #     content = response.render()
#     #     print('conteudo do primeiro get', content)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid2]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano',
#     #                                                           'bandeira': 'Visa',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     print(response.content)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     q = TokenAssinante.objects.last()
#     #     t = q.transacoes.last()
#     #
#     #     print('chave usada', q)
#     #     self.assertEquals(q.nome_gateway, 'ccm')
#     #     self.assertEquals(t.montante, 1600.00)
#     #     self.assertIsNotNone(t.cielo_transactions.recurrentpayment_id)
#     #     print(response.content)
#     #     self.assertContains(response, 'Total')
#
#     # def test_pagamento_sucesso_elo(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano',
#     #                                                           'bandeira': 'Elo',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadopagamento/')
#     #     self.assertContains(response, ' Sucesso')
#     #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #     # segunda chave
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagar.as_view()(response, uuid=self.uuid2)
#     #     self.assertEquals(200, response.status_code)
#     #     content = response.render()
#     #     print('conteudo do primeiro get', content)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid2]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Elo',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     print(response.content)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     q = TokenAssinante.objects.last()
#     #     t = q.transacoes.last()
#     #
#     #     print('chave usada', q)
#     #     self.assertEquals(q.nome_gateway, 'ccm')
#     #     self.assertEquals(t.montante, 1600.00)
#     #     self.assertIsNotNone(t.cielo_transactions.recurrentpayment_id)
#     #     print(response.content)
#     #     self.assertContains(response, 'Total')
#
#     # def test_pagamento_sucesso_dinners(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm',self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2029',
#     #                                                           'expiracao_mes': '05',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Diners',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'CPF': '93991286034',
#     #                                                           'parcelas': 1
#     #                                                           }, follow=True)
#     #     print(response)
#     #     print(response.content)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     self.assertContains(response, 'Total')
#     #     self.assertTemplateUsed(response, '../templates/resultadoassinaturas/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #     # segunda chave
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagar.as_view()(response, uuid=self.uuid2)
#     #     self.assertEquals(200, response.status_code)
#     #     content = response.render()
#     #     print('conteudo do primeiro get', content)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid2]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Diners',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     print(response.content)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     q = TokenAssinante.objects.last()
#     #     t = q.transacoes.last()
#     #
#     #     print('chave usada', q)
#     #     self.assertEquals(q.nome_gateway, 'ccm')
#     #     self.assertEquals(t.montante, 1600.00)
#     #     self.assertIsNotNone(t.cielo_transactions.recurrentpayment_id)
#     #     print(response.content)
#     #     self.assertContains(response, 'Total')
#
#     # def test_pagamento_sucesso_amex(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, convenio='ccm',plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm',self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '05',
#     #                                                           'codigo_seguranca': '1234',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Amex',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     print(response.content)
#     #     self.assertContains(response, 'Total')
#     #     q = Transacoes.objects.all().get(montante=1500.00)
#     #     print(q.status,q.tokenassinante_set)
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #     # segunda chave
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagar.as_view()(response, uuid=self.uuid2)
#     #     self.assertEquals(200, response.status_code)
#     #     content = response.render()
#     #     print('conteudo do primeiro get', content)
#     #     response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid2]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '01',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano de tal',
#     #                                                           'bandeira': 'Amex',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     print(response.content)
#     #     self.assertRedirects(response, '/cliente/resultadoassinatura/')
#     #     #q = TokenAssinante.objects.get(pessoa=self.logged_user_pessoa)
#     #     #TODO testar trial e nao trial
#     #     q = TokenAssinante.objects.last()
#     #     t = q.transacoes.last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.nome_gateway, 'ccm')
#     #     self.assertEquals(t.montante, 1600.00)
#     #     self.assertIsNotNone(t.cielo_transactions.recurrentpayment_id)
#     #     print(response.content)
#     #     self.assertContains(response, 'Total')
#     #
#     # def test_pagamento_sucesso_discover(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '05',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano',
#     #                                                           'bandeira': 'Discover',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadopagamento/')
#     #     self.assertContains(response, ' Sucesso')
#     #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #
#     # def test_pagamento_sucesso_jcb(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '05',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano',
#     #                                                           'bandeira': 'JCB',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadopagamento/')
#     #     self.assertContains(response, 'Sucesso')
#     #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#     #
#     # def test_pagamento_sucesso_aura(self):
#     #     """
#     #     test pagamento sucesso
#     #     :return:
#     #     """
#     #     self.factory = RequestFactory()
#     #     response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#     #                                 follow=True)
#     #     self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#     #     response = self.factory.get('/cliente/pagamento/')
#     #     response.user = self.logged_user
#     #     response.user.pessoa = self.logged_user_pessoa
#     #     response = ClientePagarTabAssinatura.as_view()(response, plano=self.uuid)
#     #     self.assertEquals(200, response.status_code)
#     #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '****************',
#     #                                                           'expiracao_ano': '2026',
#     #                                                           'expiracao_mes': '05',
#     #                                                           'codigo_seguranca': '123',
#     #                                                           'nome_cartao': 'fulano',
#     #                                                           'bandeira': 'Aura',
#     #                                                           'tipo_escolhido': '0',
#     #                                                           'CPF': '***********"',
#     #                                                           'Email': '<EMAIL>',
#     #                                                           'parcelas': 1,
#     #                                                           }, follow=True)
#     #     self.assertRedirects(response, '/cliente/resultadopagamento/')
#     #     self.assertContains(response, 'Sucesso')
#     #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
#     #     q = Transacoes.objects.all().last()
#     #     print('chave usada', q)
#     #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#
#     def test_pagamento_sucesso_anonimo_mastercard(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         c = Client()
#         response = c.post(reverse_lazy('clientepagarassinatura', args=['ccm',self.uuid]), {'numero_cartao': '****************',
#                                                     'expiracao_ano': '2021',
#                                                     'expiracao_mes': '09',
#                                                     'codigo_seguranca': '505',
#                                                     'nome_cartao': 'fulano de tal',
#                                                     'bandeira': 'Visa',
#                                                     'tipo_escolhido': '0',
#                                                     'CPF': '***********"',
#                                                     'Email': '<EMAIL>',
#                                                     'parcelas': 1,
#                                                     }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, 'ak_test_IsALVWB0We7Qj5bx2Llz43m1peTz8B')
#         self.assertEquals(q.montante, 1500.00)
#         # segunda chave
#         c = Client()
#         response = c.post(reverse_lazy('clientepagarassinatura',args=['ccm',self.uuid2]), {'numero_cartao': '****************',
#                                                     'expiracao_ano': '2026',
#                                                     'expiracao_mes': '01',
#                                                     'codigo_seguranca': '123',
#                                                     'nome_cartao': 'fulano de tal',
#                                                     'bandeira': 'Visa',
#                                                     'tipo_escolhido': '0',
#                                                     'CPF': '***********"',
#                                                     'Email': '<EMAIL>',
#                                                     'parcelas': 1
#                                                     }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertTemplateUsed(response, '../templates/resultadoassinaturas/resultadosucesso.html')
#         # q = TokenAssinante.objects.last()
#         t = Transacoes.objects.last()
#
#         print('chave usada')
#         # self.assertEquals(q.nome_gateway, 'ccm')
#         self.assertEquals(t.montante, 1600.00)
#         self.assertIsNotNone(t.cielo_transactions.recurrentpayment_id)
#         print(response.content)
#         self.assertContains(response, 'Total')
#
#     def test_pagamento_sucesso_anonimo_visa(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm',self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '05',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano de tal',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertTemplateUsed(response, '../templates/resultadoassinaturas/resultadosucesso.html')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, 'ak_test_IsALVWB0We7Qj5bx2Llz43m1peTz8B')
#
#
#
#
#     def test_pagamento_naoautorizado_mastercard(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '4***********1111',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '623',
#                                                               'nome_cartao': 'fulano de tal',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas':1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_cancelado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura',args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano de tal',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_problemacartao(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_problemacartaobloqueado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_expirado(self):
#         """
#             test pagamento sucesso
#             :return:
#             """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas':1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_timeout(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_mastercard_parcelado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_cancelado_parcelado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_problemacartao_parcelado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 3,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_problemacartaobloqueado_parcelado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 3,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_expirado_parcelado(self):
#         """
#             test pagamento sucesso
#             :return:
#             """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 1,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')
#
#     def test_pagamento_naoautorizado_timeout_parcelado(self):
#         """
#         test pagamento sucesso
#         :return:
#         """
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.client.get(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         print('aqui vai o reverse', reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]))
#         self.assertEquals(200, response.status_code)
#         response = self.client.post(reverse_lazy('clientepagarassinatura', args=['ccm', self.uuid]), {'numero_cartao': '****************',
#                                                               'expiracao_ano': '2026',
#                                                               'expiracao_mes': '01',
#                                                               'codigo_seguranca': '123',
#                                                               'nome_cartao': 'fulano',
#                                                               'bandeira': 'Visa',
#                                                               'tipo_escolhido': '0',
#                                                               'CPF': '***********"',
#                                                               'Email': '<EMAIL>',
#                                                               'parcelas': 3,
#                                                               }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadoassinatura/')
#         self.assertContains(response, 'recusado')


#TODO: Complete these tests below to integrate full stone
    #
    #
    # def test_pagamento_sucesso_anonimo_amex(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
    #                                                           'expiracao_ano': '2026',
    #                                                           'expiracao_mes': '05',
    #                                                           'codigo_seguranca': '1234',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Amex',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '***********"',
    #                                                           'Email': '<EMAIL>',
    #                                                           'parcelas': 1,
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')
    #     self.assertTemplateUsed(response,'../templates/resultadopagamentos/resultadosucesso.html')
    #     q = Transacoes.objects.all().last()
    #     print('chave usada', q)
    #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
    #
    # def test_pagamento_sucesso_anonimo_diners(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '36490102462661',
    #                                                           'expiracao_ano': '2026',
    #                                                           'expiracao_mes': '05',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Diners',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '***********"',
    #                                                           'Email': '<EMAIL>',
    #                                                           'parcelas': 1,
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')
    #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
    #     q = Transacoes.objects.all().last()
    #     print('chave usada', q)
    #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
    #
    # def test_pagamento_sucesso_anonimo_elo(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
    #                                                           'expiracao_ano': '2026',
    #                                                           'expiracao_mes': '01',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Elo',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '***********"',
    #                                                           'Email': '<EMAIL>',
    #                                                           'parcelas': 1,
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')
    #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
    #     q = Transacoes.objects.all().last()
    #     print('chave usada', q)
    #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
    #
    # def test_pagamento_sucesso_anonimo_discover(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '000000000000001',
    #                                                           'expiracao_ano': '2026',
    #                                                           'expiracao_mes': '01',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'Discover',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '***********"',
    #                                                           'Email': '<EMAIL>',
    #                                                           'parcelas': 1,
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')
    #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
    #     q = Transacoes.objects.all().last()
    #     print('chave usada', q)
    #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
    #
    # def test_pagamento_sucesso_anonimo_jcb(self):
    #     """
    #     test pagamento sucesso
    #     :return:
    #     """
    #     response = self.client.post(reverse_lazy('clientepagar', args=[self.uuid]), {'numero_cartao': '3566007770004971',
    #                                                           'expiracao_ano': '2026',
    #                                                           'expiracao_mes': '01',
    #                                                           'codigo_seguranca': '123',
    #                                                           'nome_cartao': 'fulano',
    #                                                           'bandeira': 'JCB',
    #                                                           'tipo_escolhido': '0',
    #                                                           'CPF': '***********"',
    #                                                           'Email': '<EMAIL>',
    #                                                           'parcelas': 1,
    #                                                           }, follow=True)
    #     self.assertRedirects(response, '/cliente/resultadopagamento/')
    #     self.assertTemplateUsed(response, '../templates/resultadopagamentos/resultadosucesso.html')
    #     q = Transacoes.objects.all().last()
    #     print('chave usada', q)
    #     self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')



#
#
#     def test_estatistica(self):
#         """
#         will test estatisticas return
#         :return:
#         """
#         from associacao.management.commands import create_stats
#         # call_command('create_stats')
#         c = Client()
#         r = c.get(reverse("estatistica", kwargs={'tipo':1}))
#         self.assertEquals(r.status_code, 404)
#         #now logged
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         r = self.client.get(reverse("estatistica", kwargs={'tipo':1}))
#         self.assertEquals(r.status_code, 200)
#         print(r.content)
#         self.assertContains(r, '[]')
#         #logged but with payment
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
#             scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
#             159&submit.y=28""", {'numero_cartao': '****************',
#                                  'expiracao_ano': '2026',
#                                  'expiracao_mes': '01',
#                                  'codigo_seguranca': '123',
#                                  'nome_cartao': 'fulano',
#                                  'bandeira': 'Visa',
#                                  'tipo_escolhido': '0',
#                                  'CPF': '***********"',
#                                  'Email': '<EMAIL>',
#                                  'parcelas': 3,
#                                  }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'Sucesso')
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         call_command('create_stats')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         from associacao.models import Estatisticas
#         print(len(Estatisticas.objects.all()))
#         r = self.client.get(reverse("estatistica", kwargs={'tipo': 1}))
#         self.assertEquals(r.status_code, 200)
#         print('conteudo do r',r.content)
#         # mes = {1: 'Jan', 2: 'Fev', 3: 'Mar', 4: 'Abr', 5: 'Mai', 6: 'Jun', 7: 'Jul',
#         #  8: 'Ago', 9: 'Set', 10: 'Out', 11: 'Nov', 12: 'Dez'}
#         self.assertContains(r, '[["{0}", "2.73"]]'.format(self.mes[datetime.now().month]))
#
#     def test_export_xls_csv(self):
#         """
#         will test exporting data to xls and csv
#         :return:
#         """
#         from associacao.management.commands import create_stats
#         # call_command('create_stats')
#         c = Client()
#         r = c.get(reverse("estatistica", kwargs={'tipo':1}))
#         self.assertEquals(r.status_code, 404)
#         #now logged
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         r = self.client.get(reverse("estatistica", kwargs={'tipo':1}))
#         self.assertEquals(r.status_code, 200)
#         print(r.content)
#         self.assertContains(r, '[]')
#         #logged but with payment
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
#             scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
#             159&submit.y=28""", {'numero_cartao': '****************',
#                                  'expiracao_ano': '2026',
#                                  'expiracao_mes': '01',
#                                  'codigo_seguranca': '123',
#                                  'nome_cartao': 'fulano',
#                                  'bandeira': 'Visa',
#                                  'tipo_escolhido': '0',
#                                  'CPF': '***********"',
#                                  'Email': '<EMAIL>',
#                                  'parcelas': 3,
#                                  }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         self.assertContains(response, 'Sucesso')
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         call_command('create_stats')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         from associacao.models import Estatisticas
#         print(len(Estatisticas.objects.all()))
#         r = self.client.get(reverse("associacaoextratocsv"))
#         self.assertEquals(r.status_code, 200)
#         print(r.content)
#         self.assertContains(r, ',2.73')
#         self.assertContains(r, ',***********')
#         r = self.client.get(reverse("associacaoextratoxls"))
#         self.assertEquals(r.status_code, 200)
#         print(r.content)
#         self.assertEquals(r.status_code, 200)
#
#     def test_integracao_evecom_visualizar_dados_brain(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = self.client.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data)
#         self.assertContains(response, 'FREDERICO')
#         self.assertTemplateUsed(response, '../templates/associado/pagamento_tabbed.html')
#         self.assertContains(response, '530.00')
#
#
#
#     def test_extrato_evecom_pagar_brain_sucesso_multiplos_convenios(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         self.assertTemplateUsed(response, '../templates/associado/pagamento_tabbed.html')
#         print(c.session)
#         print(c.cookies)
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
# scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
# 159&submit.y=28""", {'numero_cartao': '****************',
#                      'expiracao_ano': '2026',
#                      'expiracao_mes': '01',
#                      'codigo_seguranca': '123',
#                      'nome_cartao': 'fulano',
#                      'bandeira': 'Visa',
#                      'tipo_escolhido': '0',
#                      'CPF': '***********"',
#                      'Email': '<EMAIL>',
#                      'parcelas': 1
#                      }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 1)
#         self.assertContains(response, 'Sucesso')
#         self.assertEquals(q[0].amount, '2.73')
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#
#         self.assertEquals(200, response.status_code)
#         content = response.render()
#         print(content.content)
#         self.assertContains(content, '99999')
#         self.assertContains(content, '2,73')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#
#         ##now with another convenio
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'mfm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#         print(response.content)
#
#         response = c.post(
#             """/mfm/pagamento/?business=Inibrain%40ccmew.com&lc=BR&cmd=_xclick&invoice=mfm_244673_1911&item_name=244673+-+fred+-+Reserva+de+hospedagem+para+o+evento+World+Congress+on+Brain%2C+Behavior+and+Emotions+2017&amount=2.73&currency_code=BRL&data_entrada=14%2F06%2F2017&data_saida=17%2F06%2F2017&tipo_apto=SGL&numero_acompanhantes=0&nome_hotel=Master+Express+Dom+Pedro+II&categoria=Standard&id_reserva=1911&check_in=14&check_out=12&submit.x=134&submit.y=24""",
#             {'numero_cartao': '****************',
#              'expiracao_ano': '2026',
#              'expiracao_mes': '01',
#              'codigo_seguranca': '123',
#              'nome_cartao': 'fulano',
#              'bandeira': 'Visa',
#              'tipo_escolhido': '0',
#              'CPF': '***********"',
#              'Email': '<EMAIL>',
#              'parcelas': 1,
#              }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 2)
#         self.assertContains(response, 'Sucesso')
#         self.assertContains(response, 'Master Express')
#         self.assertContains(response, 'SGL')
#         self.assertContains(response, '1911')
#         self.assertEquals(q[1].amount, '2.73')
#         # pedido = Pedido.objects.last()
#         # comprovante = c.get('/comprovante/'+str(pedido.num_pedido))
#         # self.assertAlmostEqual(response.text,comprovante.text)
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#
#         self.assertEquals(200, response.status_code)
#         content = response.render()
#         print(content.content)
#         self.assertContains(content, '99999')
#         self.assertContains(content, '2,73')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, 'ad0e4615-e641-42c7-bd0a-9f4251f3bf0f')
#
#     def test_extrato_evecom_pagar_brain_sucesso(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
# scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
# 159&submit.y=28""", {'numero_cartao': '****************',
#                      'expiracao_ano': '2026',
#                      'expiracao_mes': '01',
#                      'codigo_seguranca': '123',
#                      'nome_cartao': 'fulano',
#                      'bandeira': 'Visa',
#                      'tipo_escolhido': '0',
#                      'CPF': '***********"',
#                      'Email': '<EMAIL>',
#                      'parcelas':1,
#                      }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 1)
#         self.assertContains(response, 'Sucesso')
#         self.assertEquals(q[0].amount, '2.73')
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#         self.assertEquals(200, response.status_code)
#         content = response.render()
#         print(content.content)
#         self.assertContains(content, '99999')
#         self.assertContains(content, '2,73')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#         p = Pedido.objects.last()
#         #testa pedido e transacao ligados
#         self.assertEquals(q, p.transacao)
#         #testa se pedido tem cielo data feito
#         self.assertIsNotNone(p.cielo_data)
#         #testa se pedido tem cielo transaction
#         self.assertIsNotNone(p.cielo_transacao)
#
#     def test_extrato_evecom_pagar_brain_sucesso_parcelado(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
# scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
# 159&submit.y=28""", {'numero_cartao': '****************',
#                      'expiracao_ano': '2026',
#                      'expiracao_mes': '01',
#                      'codigo_seguranca': '123',
#                      'nome_cartao': 'fulano',
#                      'bandeira': 'Visa',
#                      'tipo_escolhido': '0',
#                      'CPF': '***********"',
#                      'Email': '<EMAIL>',
#                      'parcelas': 3,
#                      }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 1)
#         self.assertContains(response, 'Sucesso')
#         self.assertEquals(q[0].amount, '2.73')
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#         self.assertEquals(200, response.status_code)
#         content = response.render()
#         print(content.content)
#         self.assertContains(content, '99999')
#         self.assertContains(content, '2,73')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#         p = Pedido.objects.last()
#         # testa pedido e transacao ligados
#         self.assertEquals(q, p.transacao)
#         # testa se pedido tem cielo data feito
#         self.assertIsNotNone(p.cielo_data)
#         # testa se pedido tem cielo transaction
#         self.assertIsNotNone(p.cielo_transacao)
#
#
#     def test_integracao_evecom_pagar_brain_sucesso(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
# scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
# 159&submit.y=28""", {'numero_cartao': '****************',
#                      'expiracao_ano': '2026',
#                      'expiracao_mes': '01',
#                      'codigo_seguranca': '123',
#                      'nome_cartao': 'fulano',
#                      'bandeira': 'Visa',
#                      'tipo_escolhido': '0',
#                      'CPF': '***********"',
#                      'Email': '<EMAIL>',
#                      'parcelas': 1,
#                      }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 1)
#         self.assertContains(response, 'Sucesso')
#         self.assertEquals(q[0].amount, '2.73')
#         q = Transacoes.objects.all().last()
#         print('chave usada', q)
#         self.assertEquals(q.cielo_data.numero, '58c875db-33d4-4406-8178-ba5db1a07e32')
#
#     def test_cancelamento_evecom_pagar_brain_sucesso(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
# scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
# 159&submit.y=28""", {'numero_cartao': '****************',
#                      'expiracao_ano': '2026',
#                      'expiracao_mes': '01',
#                      'codigo_seguranca': '123',
#                      'nome_cartao': 'fulano',
#                      'bandeira': 'Visa',
#                      'tipo_escolhido': '0',
#                      'CPF': '***********"',
#                      'Email': '<EMAIL>',
#                      'parcelas': 1
#                      }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 1)
#         self.assertContains(response, 'Sucesso')
#         self.assertEquals(q[0].amount, '2.73')
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#
#         self.assertEquals(200, response.status_code)
#         content = response.render()
#         print(content.content)
#         self.assertContains(content, '99999')
#         self.assertContains(content, '2,73')
#         response = self.factory.get('/associacao/pagamento/cancelar/1/', follow=True)
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = CancelaPagamento.as_view()(response, pk=1)
#
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#         content = response.render()
#         print('content cancelamento', content.content)
#         # self.assertRedirects(content, '/associacaoextrato/')
#         self.assertContains(content, '2,73')
#         self.assertContains(content, 'Cancelada')
#
#     def test_cancelamento_evecom_pagar_brain_sucesso_parcelado(self):
#         """
#         will check if GET parameter get everything right
#         <input type="hidden" name="business" value="<EMAIL>">
#                   <input type="hidden" name="lc" value="BR">
#                   <input type="hidden" name="cmd" value="_xclick">
#                   <input type="hidden" name="invoice" value="243361_1">
#                   <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
#                   <input type="hidden" name="amount" value="530.00">
#                   <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
#                   <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
#                   <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
#         </form>
#         clientepagartabCCM
#         :return:
#         """
#         c = Client()
#         data = {'business': '<EMAIL>',
#                 'invoice': '243361_1',
#                 'item_name': "243361 - FREDERICO CORREA DA SILVA - "
#                              "Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia",
#                 'amount': '530.00'}
#         response = c.get(reverse('clientepagartabCCM', kwargs={'convenio': 'ccm'}), data=data, follow=True)
#         self.assertContains(response, 'FREDERICO')
#         self.assertContains(response, '530.00')
#         print(c.session)
#         print(c.cookies)
#
#         response = c.post("""/ccm/pagamento/?business=<EMAIL>&lc=BR&cmd=_xclick&invoice=243361_1&item_name=243361+-+FREDERICO+CORREA+DA+SILVA+-+In
# scri%C3%A7%C3%A3o+no+10%C2%BA+Congresso+Paulista+de+Geriatria+e+Gerontologia&amount=2.73&currency_code=BRL&submit.x=
# 159&submit.y=28""", {'numero_cartao': '****************',
#                      'expiracao_ano': '2026',
#                      'expiracao_mes': '01',
#                      'codigo_seguranca': '123',
#                      'nome_cartao': 'fulano',
#                      'bandeira': 'Visa',
#                      'tipo_escolhido': '0',
#                      'CPF': '***********"',
#                      'Email': '<EMAIL>',
#                      'parcelas': 3
#                      }, follow=True)
#         self.assertRedirects(response, '/cliente/resultadopagamento/')
#         q = EvecomData.objects.all().filter(business='<EMAIL>')
#         print(q, len(q))
#         self.assertEquals(len(q), 1)
#         self.assertContains(response, 'Sucesso')
#         self.assertEquals(q[0].amount, '2.73')
#         self.factory = RequestFactory()
#         response = self.client.post('/accounts/login/', {'login': '<EMAIL>', 'password': 'glass onion'},
#                                     follow=True)
#         self.assertRedirects(response, '/', status_code=302, target_status_code=200)
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#
#         self.assertEquals(200, response.status_code)
#         content = response.render()
#         print(content.content)
#         self.assertContains(content, '99999')
#         self.assertContains(content, '2,73')
#         response = self.factory.get('/associacao/pagamento/cancelar/1/', follow=True)
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = CancelaPagamento.as_view()(response, pk=1)
#
#         response = self.factory.get('/associacaoextrato/')
#         response.user = self.logged_user
#         response.user.pessoa = self.logged_user_pessoa
#         response = ExtratoAssociacao.as_view()(response)
#         content = response.render()
#         print('content cancelamento', content.content)
#         # self.assertRedirects(content, '/associacaoextrato/')
#         self.assertContains(content, '2,73')
#         self.assertContains(content, 'Cancelada')
