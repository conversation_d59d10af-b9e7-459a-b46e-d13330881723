from django.forms import ModelForm,ValidationError,CharField, EmailField,forms, ChoiceField, RadioSelect, DecimalField, IntegerField, DateField, BooleanField
from django.forms.models import inlineformset_factory

from associacao.models import TipoDePagameno, Cobranca, CobrancaGrupo, Negocio
from django.utils.translation import ugettext_lazy as _

# from crispy_forms.helper import FormHelper
# from crispy_forms.layout import Submit, Layout, Field
# from crispy_forms.bootstrap import (
#     PrependedText, PrependedAppendedText, FormActions)

CobrancaFormSet = inlineformset_factory(CobrancaGrupo, Cobranca,can_delete=False, fields='__all__')


class CartaoForm(ModelForm):
    """
    class para pagamento cartao
    """
    CPF = CharField(label=_(u'CPF'))
    Email = EmailField(help_text=_('Email para confirmação'), label=_('Email'))

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['tipo_escolhido'].initial = 0
        a = self
        # for i in a.fields:
        #     self.fields[i].label = i

    # helper = FormHelper()
    # helper.field_class = 'input-control'
    # helper.layout = Layout(
    #     Field('nome_cartao', css_class='form-control'),
    #     Field('codigo_seguranca', css_class='form-control'),
    # )


    class Meta:
        model = TipoDePagameno
        fields = '__all__'

    def clean(self):
        dados = super().clean()
        codigo_seguranca = dados.get('codigo_seguranca')
        try:
            str(dados.get('codigo_seguranca'))
        except ValueError:
            raise ValidationError('Digite somente numeros no Codigo de Seguranca')
        except TypeError:
            raise ValidationError('Por Favor Digite o Codigo de Segurança do CC')
        try:
            cartao = dados.get('numero_cartao')

            int(cartao.replace(' ', ''))
        except ValueError:
            raise ValidationError('Digite somente numeros no Numero de Cartao')
        if len(str(dados.get('numero_cartao'))) >19:
            raise ValidationError('Numero invalido de cartao')
        if len(str(codigo_seguranca))>3 and dados.get('bandeira') != 'Amex':
            raise ValidationError("Codigo de seguranca invalido!")
        if len(str(codigo_seguranca))>4:
            raise ValidationError("Codigo de seguranca invalido!")
        if len(str(codigo_seguranca))<3 or codigo_seguranca==None:
            print(codigo_seguranca)
            raise ValidationError("Codigo de seguranca invalido!")
        if dados.get('bandeira') == None:
            raise ValidationError("Escolha uma bandeira")
        if len(str(dados.get('numero_cartao'))) <14 and dados.get('bandeira') == 'Diners':
            raise ValidationError("Digite todos os numeros do cartao")

        if len(str(dados.get('numero_cartao'))) <15 and dados.get('bandeira') != 'Diners':
            print('dinners validation error',dados.get('numero_cartao'), dados.get('bandeira'))
            raise ValidationError("Digite todos os numeros do cartao")


class PlanoCategoriaForm(forms.Form):
    """
    step 1 for form plano criar
    """
    Tipo = ChoiceField(widget=RadioSelect,
                       choices=[('1', 'Anuidade'),
                                ('2', 'Evento'),
                                ('3', 'Serviço')])


class PlanoModeloForm(forms.Form):
    """
    Will show modelos to make items
    """
    Modelo = ChoiceField(choices=[('1', 'Curso de Especializacao 2016'),
                                ('2', 'Curso de Prova de Neuro 2016'),
                                ('3', 'Diploma')])


class PlanoItemDetalhesForm(forms.Form):
    """
    final step with details of item
    data_ciclo = models.DateField(blank=True, null=True)
    frequencia_intervalo = models.CharField(max_length=40, choices=[('M', 'Mensal'), ('D', 'Diario'), ('W', 'Semanal'),
                                                                    ('Y', 'Anual')])
    frequencia = models.IntegerField()
    trial = models.BooleanField(default=False)
    montante = models.DecimalField(decimal_places=2, max_digits=10)
    nome_plano = models.CharField(max_length=100)
    mensagem_pagamento = models.TextField(blank=True, null=True)
    mensagem_sucesso = models.TextField(blank=True, null=True)
    entidade = models.ForeignKey(Negocio, blank=True, null=True)
    membros = models.ManyToManyField(Pessoa)
    """
    nome = CharField(max_length=100)
    montante = DecimalField(max_digits=15, decimal_places=2, localize=True)
    data_inicio = DateField()
    parcelas = IntegerField(max_value=12, min_value=1)
    tipo = ChoiceField(
                       choices=[('1', 'Anuidade'),
                                ('2', 'Evento'),
                                ('3', 'Serviço')])
    categoria = CharField(max_length=120)
    novo_grupo = CharField(max_length=120)


class DetalhePagamentoGatewayForm(forms.Form):
    """
    main from to search details on payment gateway
    """
    tid = CharField(max_length=30)


class CobrancaForm(ModelForm):
    """
    Cobranca form
    """

    class Meta:
        model = Cobranca
        fields = '__all__'

class ImportaAssociadoForm(forms.Form):
    """
    main form to import new associados into association
    """
    arquivo = forms.FileField()


class ConciliaGatewayForm(forms.Form):
    """
    main class to conciliate
    """
    arquivo = forms.FileField()
    BuscaTID = BooleanField(label="Busca pelo TID", initial=True)

class CPFForm(forms.Form):
    """
    Class with only CPF or Doc field
    """
    CPF = CharField(max_length=20)