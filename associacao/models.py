from django.contrib.auth.models import User
from django.db import models
from mailapp.models import MailTemplate
from django.utils.translation import ugettext_lazy as _

import uuid


# Create your models here.

class GlobalOptions(models.Model):
    """
    main class for site options
    """
    menu_associado = models.CharField(max_length=200, null=True, blank=True)
    dias_pagamento = models.IntegerField(default=10)


class TipoDePagameno(models.Model):
    """
    classe para os tipos de pagamentos
    """
    tipo_escolhido = models.IntegerField(choices=[(0, _('Cartão Credito')), (1, _('Cartão Débito')), (2, _('PIX'))], default=0,
                                         verbose_name=_('Tipo de Pagamento'))

    nome_cartao = models.CharField(max_length=120, null=True, blank=True, verbose_name=_('Nome no Cartão'))
    numero_cartao = models.Char<PERSON>ield(max_length=120, null=True, blank=True, verbose_name=_('Numero Do Cartão'))
    expiracao_ano = models.Char<PERSON>ield(verbose_name=_("Ano de expiração"), max_length=4,
                                     choices=[('2016', '2016'), ('2017', '2017'), ('2018', '2018'),
                                              ('2019', '2019'), ('2020', '2020'), ('2021', '2021'),
                                              ('2022', '2022'), ('2023', '2023'), ('2024', '2024'),
                                              ('2025', '2025'), ('2026', '2026'), ('2027', '2027'),
                                              ('2028', '2028'), ('2029', '2029'), ('2030', '2030'),
                                              ('2031', '2031'), ('2032', '2032'), ('2033', '2033'),
                                              ('2034', '2034'), ('2035', '2035'), ('2036', '2036'),
                                              ('2037', '2037'), ('2038', '2038'), ('2039', '2039'),
                                              ('2040', '2040'), ('2041', '2041'), ('2042', '2042')], default='2023')
    expiracao_mes = models.CharField(max_length=2, verbose_name=_("Mês de expiração"),
                                     choices=[('01', '01'), ('02', '02'), ('03', '03'), ('04', '04'),
                                              ('05', '05'), ('06', '06'), ('07', '07'), ('08', '08'),
                                              ('09', '09'), ('10', '10'), ('11', '11'), ('12', '12')], default='01')
    bandeira = models.CharField(max_length=100, null=True, blank=True, verbose_name=_('bandeira'),
                                choices=[('Visa', 'Visa'), ('Master', 'Mastercard'), ('Diners', 'Diners'),
                                         ('Discover', 'Discover')
                                    , ('Elo', 'Elo'), ('JCB', 'JCB'), ('Aura', 'Aura'), ('Amex', 'Amex')])
    codigo_seguranca = models.CharField(max_length=5, null=True, blank=True, verbose_name=_("Codigo de segurança"))
    parcelas = models.IntegerField(default=1, verbose_name=_('Parcelas'))

    def __str__(self):
        return str(self.get_tipo_escolhido_display())


class Endereco(models.Model):
    """
    main class for enderecos
    """
    street = models.CharField(max_length=200)
    street_number = models.CharField(max_length=200)
    zipcode = models.CharField(max_length=200)
    country = models.CharField(max_length=200)
    state = models.CharField(max_length=200)
    city = models.CharField(max_length=200)
    neighborhood = models.CharField(max_length=200, null=True, blank=True)
    complementary = models.CharField(max_length=200, null=True, blank=True)


class CieloTransaction(models.Model):
    """
    main class for cielo transacations
    <Transacao(comercial=None, cartao=None,
    pedido=<Pedido(numero=x666, valor=6000, moeda=986,
    data_hora=2016-04-10T15:52:06.075-03:00, descricao=None,
    idioma=PT, taxa_embarque=0, soft_descriptor=None)>,
    pagamento=<Pagamento(bandeira=visa, produto=1, parcelas=1)>,
    url_retorno=None, autorizar=None, capturar=None,
    campo_livre=None, bin=None, gerar_token=None, avs=None,
    autenticacao=<Autenticacao(codigo=6, mensagem=Transacao sem autenticacao,
     data_hora=2016-04-10T15:52:06.099-03:00, valor=6000, eci=7)>,
     autorizacao=<Autorizacao(codigo=6, mensagem=Transacao autorizada,
      data_hora=2016-04-10T15:52:06.103-03:00, valor=6000, lr=0,
       arp=123456, nsu=397734)>, captura=<Captura(codigo=6,
       mensagem=Transacao capturada com sucesso, data_hora=2016-04-10T15:52:06.122-03:00,
        valor=6000, taxa_embarque=None)>, token=None, cancelamento=None,
         tid=1006993069000619F26A, pan=IqVz7P9zaIgTYdU41HaW/OB/d7Idwttqwb2vaTt8MT0=,
          status=6, url_autenticacao=None)>


    """
    tid = models.CharField(max_length=100, null=True, blank=True)
    pan = models.CharField(max_length=100, null=True, blank=True)
    valor = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    descricao = models.CharField(max_length=130, null=True, blank=True)
    autorizacao_lr = models.CharField(max_length=2, null=True, blank=True)
    soft_descriptor = models.CharField(max_length=130, null=True, blank=True)
    captura_codigo = models.CharField(max_length=3, null=True, blank=True)
    autorizacao_codigo = models.CharField(max_length=3, null=True, blank=True)
    data_hora = models.DateTimeField(null=True, blank=True, auto_now_add=True)
    pedido_numero = models.CharField(max_length=50, null=True, blank=True)
    moeda = models.IntegerField(null=True, blank=True)
    bandeira = models.CharField(max_length=20, null=True, blank=True)
    produto = models.CharField(max_length=200, null=True, blank=True)
    parcelas = models.IntegerField(null=True, blank=True)
    arp = models.CharField(max_length=80, null=True, blank=True)
    nsu = models.CharField(max_length=80, null=True, blank=True)
    tentative = models.TextField(max_length=500, null=True, blank=True)
    payment_id = models.CharField(max_length=140, null=True, blank=True)
    recurrentpayment_id = models.CharField(max_length=140, null=True, blank=True)
    interval = models.CharField(max_length=30, null=True, blank=True)

    def __str__(self):
        return str(self.id)


class Pessoa(models.Model):
    """
    main class for pessoa
    """
    usuario = models.OneToOneField(User, null=True, blank=True, on_delete=models.CASCADE)
    logradouro = models.CharField(max_length=300, blank=True, null=True)
    numero = models.CharField(max_length=10, blank=True, null=True)
    complemento = models.CharField(max_length=10, blank=True, null=True)
    admin_negocio = models.BooleanField(default=False)
    admin_convenio = models.BooleanField(default=False)
    visualiza_pagamentos = models.BooleanField(default=False)
    financeiro = models.BooleanField(default=False)
    escolha_pagammento = models.OneToOneField(TipoDePagameno, null=True, blank=True)
    email = models.EmailField(blank=True, null=True)
    CPF = models.CharField(max_length=18, null=True, blank=True)
    photo = models.FileField(upload_to="/avatar/", null=True, blank=True)
    pode_impersonate = models.BooleanField(default=False)
    pode_conciliar = models.BooleanField(default=False)
    pode_estornar = models.BooleanField(default=False)

    def __str__(self):
        try:
            return self.usuario.email
        except:
            return self.CPF


class TemplateOrdemEvecom(models.Model):
    """
    result['mensagem_rodape'] = 'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = 'green-jungle'
        result['logo_evento'] = '/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        nome_plano='World Congress on Brain, Behavior and Emotions',
       descricao_curta='Brain 2017'
    """
    mensagem_rodape = models.CharField(max_length=200, null=True, blank=True)
    cor_evento = models.CharField(max_length=100, null=True, blank=True)
    logo_evento = models.CharField(max_length=200, null=True, blank=True)
    nome_plano = models.CharField(max_length=200, null=True, blank=True)
    descricao_curta = models.CharField(max_length=100, null=True, blank=True)
    formato_cpf = models.BooleanField(default=True)
    template_file = models.CharField(max_length=250, default='../templates/associado/pagamento_tabbed.html')

    def __str__(self):
        return str(self.descricao_curta)


class IntegraTerceiros(models.Model):
    """
    third party integration
    protocolo -> GET -> 0, POST 1
    """
    url_destino = models.CharField(max_length=250)
    protocolo = models.IntegerField(default=0)
    nome = models.CharField(max_length=220, null=True, blank=True)
    email_envio = models.EmailField(null=True, blank=True)
    envia_email = models.BooleanField(default=False)
    envia_rest = models.BooleanField(default=True)

    def __str__(self):
        return self.url_destino


class IntegraMarketing(models.Model):
    """
    To integrate dinamize and others market tools
    sending the customer that converted
    protocolo -> GET -> 0, POST 1
    """
    name = models.CharField(max_length=100, null=True, blank=True)
    url_destino = models.CharField(max_length=250)
    protocolo = models.IntegerField(default=0, choices=[(0, 'Dinamize')])
    campo_email = models.CharField(max_length=100)
    envia_email = models.BooleanField(default=True)
    envia_adicional1 = models.BooleanField(default=False)
    adicional1_campo_nome = models.CharField(max_length=100, null=True, blank=True)
    adicional1_valor = models.CharField(max_length=200, null=True, blank=True)
    usuario = models.CharField(max_length=200, null=True, blank=True)
    password = models.CharField(max_length=32, null=True, blank=True)

    def __str__(self):
        if self.name is None:
            return 'nome indefinido'
        else:
            return '{0}->{1}->{2}'.format(self.name, self.protocolo, self.adicional1_valor)


class Negocio(models.Model):
    """
    main class para descrever negocio
    """
    name = models.CharField(max_length=250)
    logradouro = models.CharField(max_length=250)
    telefone = models.CharField(max_length=20)
    complemento = models.CharField(max_length=40, null=True, blank=True)
    cidade = models.CharField(max_length=120, null=True, blank=True)
    UF = models.CharField(max_length=2, null=True, blank=True)
    CEP = models.CharField(max_length=20, null=True, blank=True)
    CNPJ = models.CharField(max_length=25, null=True, blank=True)
    admin = models.ForeignKey(Pessoa, null=True, blank=True, limit_choices_to={'admin_negocio': True})
    dias_libera_dinheiro = models.IntegerField(null=True, blank=True, default=10)
    integracao_Analytics = models.TextField(null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    template_email_cancelamento = models.ForeignKey(MailTemplate, null=True, blank=True)
    template_evecom = models.ForeignKey(TemplateOrdemEvecom, null=True, blank=True)
    parcelas = models.IntegerField(default=1)
    parcelas_convenio_minimo = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    parcelas_MFM = models.IntegerField(default=1)
    parcelas_MFM_minimo = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    parcelas_MFM_minimo_2x = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    parcelas_MFM_minimo_3x = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    parcelas_MFM_minimo_4x = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    parcelas_MFM_minimo_5x = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    parcelas_MFM_minimo_6x = models.DecimalField(default=0.00, decimal_places=2, max_digits=10)
    sendevecom_sync = models.BooleanField(default=False)
    imprime_etiqueta = models.BooleanField(default=False)
    url_etiqueta = models.CharField(max_length=250, null=True, blank=True)
    visualizador_pagamentos = models.ManyToManyField(Pessoa, null=True, blank=True, related_name="visualizadores")
    mostra_cnpj = models.BooleanField(default=False)
    integracao_terceiro = models.ForeignKey(IntegraTerceiros, null=True, blank=True)
    integracao_marketing = models.ForeignKey(IntegraMarketing, null=True, blank=True)
    traducao = models.CharField(max_length=150, choices=[('pt-br', 'Portugues'), ('en', 'Inglês'), ('es', 'Espanhol')],
                                default='pt-br')
    google_tag_enabled = models.BooleanField(default=False)
    google_tag_head = models.TextField(null=True, blank=True)
    google_tag_body = models.TextField(null=True, blank=True)
    google_tag_version = models.IntegerField(default=1, choices=[(1, 'normal'), (2, 'GA4')])

    def __str__(self):
        return self.email


class PlanoParcelamento(models.Model):
    """
    Here we have the installments options for each negocio
    """

    codigo = models.CharField(max_length=30, null=True, blank=True)
    parcelas = models.IntegerField(default=0)
    nome = models.CharField(max_length=200)
    ativo = models.BooleanField(default=True)
    negocio = models.ForeignKey(Negocio)

    def __str__(self):
        return self.codigo + ' ' + self.nome + ' ' + str(self.parcelas)


class CieloData(models.Model):
    """
    main class for Cielo information
    """
    numero = models.CharField(max_length=128)
    chave = models.CharField(max_length=200)
    sandbox = models.BooleanField(default=True)
    producao = models.BooleanField(default=False)
    ativo = models.BooleanField(default=True)
    tipo_api = models.CharField(max_length=30, default='1.5')
    nome = models.CharField(max_length=50, null=True, blank=True)
    negocio = models.ForeignKey(Negocio, null=True, blank=True)
    admin = models.ForeignKey(Pessoa, null=True, blank=True)

    def __str__(self):
        return str(self.nome)


class PlanosdePagamento(models.Model):
    """
    main class for planos de pagamento
    """
    data_ciclo = models.DateField(blank=True, null=True)
    frequencia_intervalo = models.CharField(max_length=40, blank=True, null=True,
                                            choices=[('M', 'Mensal'), ('D', 'Diario'), ('W', 'Semanal'),
                                                     ('Y', 'Anual'), ('U', 'Unico')])
    frequencia = models.IntegerField(default=1)
    trial = models.BooleanField(default=False)
    montante = models.DecimalField(decimal_places=2, max_digits=10)
    nome_plano = models.CharField(max_length=100, blank=True, null=True)
    descricao_curta = models.CharField(max_length=13, blank=True, null=True)
    mensagem_sucesso = models.TextField(blank=True, null=True)
    entidade = models.ForeignKey(Negocio, blank=True, null=True)
    membros = models.ManyToManyField(Pessoa, blank=True)
    categoria = models.CharField(max_length=120, null=True, blank=True)
    tipo = models.CharField(max_length=120, null=True, blank=True, default='Evento')
    boleto_ativado = models.BooleanField(default=True)
    cartao_ativado = models.BooleanField(default=True)
    debito_ativado = models.BooleanField(default=True)
    deposito_ativado = models.BooleanField(default=True)
    pix_ativado = models.BooleanField(default=True)
    logo_evento = models.FileField(upload_to='logos', default=None, null=True, blank=True)
    logo_organizacao = models.FileField(upload_to='logos', default=None, null=True, blank=True)
    integrado_evecom = models.BooleanField(default=False)
    cielo_data = models.ForeignKey(CieloData, null=True, blank=True)
    uuid_pagamento = models.CharField(max_length=200, null=True, blank=True)
    parcelas = models.IntegerField(null=True, blank=True)
    valor_a_vista = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True)
    desconto_a_vista = models.BooleanField(default=False)
    assinatura = models.BooleanField(default=False)
    assinatura_trial_days = models.IntegerField(null=True, blank=True)
    valor_flexivel = models.BooleanField(default=False)
    assinatura_no_backend_1x = models.CharField(max_length=100, null=True, blank=True)
    assinatura_no_backend_2x = models.CharField(max_length=100, null=True, blank=True)
    assinatura_no_backend_3x = models.CharField(max_length=100, null=True, blank=True)
    assinatura_no_backend_4x = models.CharField(max_length=100, null=True, blank=True)
    assinatura_no_backend_5x = models.CharField(max_length=100, null=True, blank=True)
    assinatura_no_backend_6x = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return str(self.nome_plano)


class Conta(models.Model):
    """
    main class for transactions
    """
    usuario = models.OneToOneField(Pessoa, null=True, blank=True)
    saldo = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)


class Transacoes(models.Model):
    """
    each transaction is here
    """
    cielo_transactions = models.OneToOneField(CieloTransaction, null=True, blank=True)
    montante = models.DecimalField(max_digits=15, decimal_places=2)
    conta = models.ForeignKey(Conta)
    data_hora = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    destino = models.ForeignKey(Negocio)
    status = models.CharField(max_length=100, null=True, blank=True)
    plano_pagamento = models.ForeignKey(PlanosdePagamento, null=True, blank=True)
    usuario = models.ForeignKey(Pessoa, null=True, blank=True)
    status_code = models.IntegerField(null=True, blank=True,
                                      choices=[(0, 'Pago'), (1, 'Pendente'), (2, 'Cancelada')])
    pedido_saque = models.BooleanField(default=False)
    pedido_saque_data = models.DateTimeField(null=True, blank=True)
    saque_efetuado = models.BooleanField(default=False)
    saque_efetuado_data = models.DateTimeField(null=True, blank=True)
    num_transacoes = models.UUIDField(default=uuid.uuid4, editable=False, null=True, blank=True)
    cielo_data = models.ForeignKey(CieloData, null=True, blank=True)
    endereco = models.ForeignKey(Endereco, null=True, blank=True)

    def __str__(self):
        return str(self.id)


class CobrancaItem(models.Model):
    """
    main class for individual items on order
    """
    descricao = models.CharField(max_length=150, null=True, blank=True)
    valor = models.DecimalField(decimal_places=2, max_digits=15)
    negocio = models.ForeignKey(Negocio)


class CobrancaGrupo(models.Model):
    """
    main class for Cobranca gruopo
    """
    nome_grupo = models.CharField(max_length=120)


class Cobranca(models.Model):
    """
    main class for Cobranca
    """
    nome = models.CharField(max_length=120)
    grupo = models.ForeignKey(CobrancaGrupo, null=True, blank=True)
    tipo = models.CharField(max_length=100, choices=[('Evento', 'Evento'),
                                                     ('Anuidade', 'Anuidade'),
                                                     ('Curso', 'Curso'),
                                                     ('Serviços', 'Serviços')])
    items = models.ManyToManyField(CobrancaItem, blank=True)
    usuarios_permitdos = models.ManyToManyField(Pessoa, blank=True)


class TokenAssinante(models.Model):
    """
    Token pra assinante
    """
    pessoa = models.ForeignKey(Pessoa)
    token = models.CharField(max_length=250)
    nome_gateway = models.CharField(max_length=200)
    criado = models.DateTimeField(auto_created=True, auto_now_add=True)
    planos_pagamento = models.ForeignKey(PlanosdePagamento, null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    transacoes = models.ManyToManyField(Transacoes, null=True, blank=True)


class Estatisticas(models.Model):
    """
    main model for statistic
    """
    mes = models.IntegerField()
    ano = models.IntegerField(null=True, blank=True)
    tipo = models.IntegerField()  # 1 for membership revenue, 2 for all revenue, 3 members,
    #  4 membership revenue without mfm, 5 all without mfmrevenue
    total = models.DecimalField(max_digits=15, decimal_places=2)
    negocio = models.ForeignKey(Negocio, on_delete=models.SET_NULL, null=True)


class Mensagem(models.Model):
    """
    main class for mensagens
    """
    titulo = models.CharField(max_length=120)
    text = models.CharField(max_length=200, default=titulo)
    lida = models.BooleanField(default=False)
    tipo = models.IntegerField(choices=[('1', 'mensagem'), ('2', 'Calendario')])
    destino = models.ForeignKey(Pessoa)


class Boleto(models.Model):
    """
    main model for Boleto
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    expiration_date = models.DateField(null=True, blank=True)
    url_main = models.URLField(null=True, blank=True)
    number = models.CharField(max_length=80, null=True, blank=True)
    barCodeNumber = models.CharField(max_length=120, null=True, blank=True)
    DigitableLine = models.CharField(max_length=150, null=True, blank=True)
    Address = models.CharField(max_length=200, null=True, blank=True)
    PaymentId = models.CharField(max_length=150, null=True, blank=True)
    Amount = models.DecimalField(max_length=15, decimal_places=2, max_digits=15, null=True, blank=True)
    Type = models.CharField(max_length=80, default='Boleto')
    Country = models.CharField(max_length=120, default='BRA')
    ExtraDataCollection = models.CharField(max_length=120, null=True, blank=True)
    Status = models.IntegerField(null=True, blank=True)
    querylink_url = models.URLField(null=True, blank=True)
    demonstrative = models.CharField(max_length=450, null=True, blank=True)
    instructions = models.CharField(max_length=450, null=True, blank=True)
    Provider = models.CharField(max_length=120, choices=[('Bradesco', 'Bradesco'),
                                                         ('BancodoBrasil', 'BancodoBrasil')], default='BancodoBrasil')
    ppago_status = models.CharField(max_length=120, choices=[('PendenteCriacao', 'PendenteCriacao'),
                                                             ('PendentePagamento', 'PendentePagamento'),
                                                             ('Pago', 'Pago'), ('Vencido', 'Vencido')],
                                    default='PendenteCriacao')
    pessoa = models.ForeignKey(Pessoa, null=True, blank=True)
    negocio = models.ForeignKey(Negocio, null=True, blank=True)
    cobranca = models.ForeignKey(PlanosdePagamento, blank=True, null=True)
    tentative = models.TextField(null=True, blank=True)
    name = models.CharField(max_length=250, null=True, blank=True)
    html_source = models.TextField(null=True, blank=True)


class EvecomData(models.Model):
    """
    create data for evecom integration
    <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
    </form>
    tipo_integracao -> 0 evecom, 1 terceiros
    """

    lc = models.CharField(max_length=100, null=True, blank=True)
    invoice = models.CharField(max_length=200, null=True, blank=True)
    item_name = models.CharField(max_length=200, null=True, blank=True)
    amount = models.CharField(max_length=100, null=True, blank=True)
    currency_code = models.CharField(max_length=100, null=True, blank=True)
    sent = models.BooleanField(default=False)
    sent_marketing = models.BooleanField(default=False)
    criado = models.DateTimeField(auto_now_add=True)
    business = models.CharField(max_length=200, null=True, blank=True)
    paid = models.BooleanField(default=False, blank=True)
    data_entrada = models.CharField(max_length=100, null=True, blank=True)
    data_saida = models.CharField(max_length=100, null=True, blank=True)
    tipo_apto = models.CharField(max_length=100, null=True, blank=True)
    numero_acompanhantes = models.CharField(max_length=100, null=True, blank=True)
    nome_hotel = models.CharField(max_length=100, null=True, blank=True)
    categoria = models.CharField(max_length=100, null=True, blank=True)
    id_reserva = models.CharField(max_length=100, null=True, blank=True)
    check_in = models.CharField(max_length=100, null=True, blank=True)
    check_out = models.CharField(max_length=100, null=True, blank=True)
    comprovante_url = models.URLField(null=True, blank=True)
    tipo_integracao = models.IntegerField(default=0)
    integracao_terceiro = models.ForeignKey(IntegraTerceiros, blank=True, null=True)
    integracao_marketing = models.ForeignKey(IntegraMarketing, blank=True, null=True)
    cpf = models.CharField(max_length=200, null=True, blank=True)
    email = models.CharField(max_length=200, null=True, blank=True)
    parcelas = models.IntegerField(default=1)

    def __str__(self):
        return str(self.id)


class Pedido(models.Model):
    """
    main class that has order details
    """
    usuario = models.ForeignKey(Pessoa, null=True, blank=True, on_delete=models.SET_NULL)
    transacao = models.ForeignKey(Transacoes, null=True, blank=True, on_delete=models.SET_NULL)
    negocio = models.ForeignKey(Negocio, null=True, blank=True, on_delete=models.SET_NULL)
    datahora = models.DateTimeField(auto_now_add=True)
    items = models.CharField(max_length=200, null=True, blank=True)
    status = models.CharField(max_length=120, null=True, blank=True)
    valor = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    evecom_item = models.ForeignKey(EvecomData, null=True, blank=True, on_delete=models.SET_NULL)
    plano_pagamento = models.ForeignKey(PlanosdePagamento, null=True, blank=True, on_delete=models.SET_NULL)
    cobranca = models.ForeignKey(Cobranca, null=True, blank=True, on_delete=models.SET_NULL)
    cielo_transacao = models.ForeignKey(CieloTransaction, null=True, blank=True, on_delete=models.SET_NULL)
    num_pedido = models.UUIDField(default=uuid.uuid4, editable=False, null=True, blank=True)
    cielo_data = models.ForeignKey(CieloData, null=True, blank=True, on_delete=models.SET_NULL)
    nome = models.CharField(max_length=200, null=True, blank=True)
    utm_source = models.CharField(max_length=200, null=True, blank=True)
    utm_medium = models.CharField(max_length=200, null=True, blank=True)
    utm_campaign = models.CharField(max_length=200, null=True, blank=True)
    utm_term = models.CharField(max_length=200, null=True, blank=True)
    utm_content = models.CharField(max_length=200, null=True, blank=True)

    def __str__(self):
        return str(self.id)


class NegocioRecenteAcoes(models.Model):
    """
    Main model for negocio recente news
    """
    descricao = models.CharField(max_length=120, null=True, blank=True)
    tipo = models.CharField(max_length=50, null=True, blank=True)
    transacao = models.ForeignKey(Transacoes, null=True, blank=True)
    associado = models.ForeignKey(Pessoa, null=True, blank=True)
    data_hora = models.DateTimeField(auto_now_add=True)
    negocio = models.ForeignKey(Negocio, null=True, blank=True)


# class Voucher(models.Model):
#     """
#     Main class for vouchers
#     """
#     uuid = models.UUIDField(default=uuid.uuid4, editable=False, null=True, blank=True)
#     plano = models.ForeignKey(PlanosdePagamento, null=True, blank=True, on_delete=models.SET_NULL())
#     pessoa = models.ForeignKey(Pessoa, null=True, blank=True)
#     used = models.BooleanField(default=False)
#     valor = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)


class Concilicao(models.Model):
    created_date = models.DateTimeField(auto_now_add=True)
    tipo_concilicao = models.IntegerField(choices=[(1, 'Cielo'),
                                                   (2, 'TID')], default=1)
    status = models.IntegerField(choices=[(1, 'pendente'), (2, 'executada')], default=1)
    arquivo_input = models.FileField(upload_to='cielo_input/%Y/%m/%d', null=True, blank=True)
    arquivo_output = models.FileField(upload_to='cielo_output/%Y/%m/%d', null=True, blank=True)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)


class TaxaGatewayPagamentos(models.Model):
    """
    taxa dos gateway de pagamento

    Produto	Bandeira	Taxas (%)	Prazo	Tarifa	Receba Rápido		Banco	Agência
			                            (dias)		Fator 1	Fator 2
    MC CRED	MASTERCARD	1.78	30	0	0	0	341	8875
    MAESTRO	MASTERCARD	1.45	1	0	0	0	341	8875
    MC PARC LJ	MASTERCARD	2.53	30	0	0	0	341	8875
    DINERS CRE	DINERS	1.78	30	0	0	0	341	8875
    VS CRED	VISA	1.78	30	0	0	0	341	8875
    VS ELECTR	VISA	1.45	1	0	0	0	341	8875
    VS PARC LJ	VISA	2.53	30	0	0	0	341	8875
    ELO CR	ELO	1.78	30	0	0	0	341	8875
    ELO DEBITO	ELO	1.45	1	0	0	0	341	8875
    ELO PARCEL	ELO	2.53	30	0	0	0	341	8875
    AMEX CRED	AMEX	2.72	30	0	0	0	341	8875
    AMEX LOJA	AMEX	3.31	30	0	0	0	341	8875

    """
    nome = models.IntegerField(choices=[(1, 'Cielo')], default=1)
    taxa_mc_cred = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_mc_maestro = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_mc_cred_parcelado = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_dinners_cred = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_visa_cred = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_visa_electron = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_vs_cred_parcelado = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_elo_cred = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_elo_debito = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_elo_parcelado = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_amex_cred = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    taxa_amex_loja = models.DecimalField(default=0.00, max_digits=4, decimal_places=2)
    nome_mc_cred = models.CharField(default='MC crédito', max_length=100)
    nome_mc_maestro = models.CharField(default='Maestro', max_length=100)
    nome_mc_cred_parcelado = models.CharField(default='MC parcelado loja', max_length=100)
    nome_dinners_cred = models.CharField(default='Diners crédito', max_length=100)
    nome_visa_cred = models.CharField(default='Visa crédito', max_length=100)
    nome_visa_electron = models.CharField(default='Visa electron', max_length=100)
    nome_vs_cred_parcelado = models.CharField(default='Visa parcelado', max_length=100)
    nome_elo_cred = models.CharField(default='ELO crédito', max_length=100)
    nome_elo_debito = models.CharField(default='ELO débito', max_length=100)
    nome_elo_parcelado = models.CharField(default='ELO parcelado', max_length=100)
    nome_amex_cred = models.CharField(default='Amex', max_length=100)
    nome_amex_loja = models.CharField(default='Amex parcelado', max_length=100)


class ListofValidatedPeople(models.Model):
    """
    list of all pre validated people to get discounts
    """
    CPF_Document = models.CharField(max_length=200)
    email = models.EmailField(null=True, blank=True)
    nome = models.CharField(max_length=220, null=True, blank=True)

    def __str__(self):
        return self.CPF_Document


class PricePlan(models.Model):
    """
    Price Plan discounts etc
    """
    preco = models.DecimalField(default=0.00, max_digits=8, decimal_places=2)
    lista_preco = models.ManyToManyField(ListofValidatedPeople, null=True, blank=True)
    nome = models.CharField(max_length=220, null=True, blank=True)

    def __str__(self):
        return str(self.nome)


class Validation(models.Model):
    """
    model to pre-validate payments
    """
    entidade = models.ForeignKey(Negocio)
    priceplan = models.ManyToManyField(PricePlan)
    uuid = models.CharField(max_length=250, default=uuid.uuid4, unique=True)
    force_email = models.BooleanField(default=False)
    nome = models.CharField(max_length=220, null=True, blank=True)
    convenio = models.ForeignKey(CieloData, null=True, blank=True)
    logotipo = models.CharField(max_length=220, null=True, blank=True)
    preco_padrao = models.DecimalField(default=0.00, max_digits=8, decimal_places=2)


class PixPayment(models.Model):
    """
    Modelo para armazenar dados de pagamentos PIX
    """
    STATUS_CHOICES = [
        ('pending', 'Pendente'),
        ('paid', 'Pago'),
        ('expired', 'Expirado'),
        ('cancelled', 'Cancelado'),
    ]

    valor = models.DecimalField(max_digits=15, decimal_places=2)
    pix_code = models.TextField()  # Código PIX (EMV)
    pix_key = models.CharField(max_length=255)  # Chave PIX da empresa
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()  # Data de expiração (30 minutos)
    paid_at = models.DateTimeField(null=True, blank=True)

    # Relacionamentos
    negocio = models.ForeignKey(Negocio)
    plano_pagamento = models.ForeignKey(PlanosdePagamento, null=True, blank=True)
    transacao = models.OneToOneField(Transacoes, null=True, blank=True)

    # Dados do pagador (opcional)
    pagador_nome = models.CharField(max_length=255, null=True, blank=True)
    pagador_cpf = models.CharField(max_length=14, null=True, blank=True)
    pagador_email = models.EmailField(null=True, blank=True)

    def __str__(self):
        return f"PIX {self.valor} - {self.status}"

    @property
    def is_expired(self):
        from django.utils import timezone
        return timezone.now() > self.expires_at

    class Meta:
        verbose_name = "Pagamento PIX"
        verbose_name_plural = "Pagamentos PIX"
