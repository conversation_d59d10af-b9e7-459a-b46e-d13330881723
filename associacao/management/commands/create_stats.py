from django.core.management.base import BaseCommand, CommandError
from associacao.models import Transacoes, Negocio, Estatisticas
from itertools import groupby
from datetime import date
from django.db.models import Count
import calendar

class Command(BaseCommand):
    help = 'Cria as estatisticas para painel'
    #
    # def add_arguments(self, parser):
    #     parser.add_argument('poll_id', nargs='+', type=int)

    def handle(self, *args, **options):
        Estatisticas.objects.all().delete()
        try:
            for negocioid in Negocio.objects.all():
                receita = Transacoes.objects.only('data_hora','montante').order_by('data_hora').filter(destino=negocioid.id, status='Pago',data_hora__gte="2021-01-01").exclude(cielo_data__nome='mfm')
                month_totals = {
                    k: sum(x.montante for x in g)
                    for k, g in groupby(receita, key=lambda i: i.data_hora.date().month)
                    }
                inscrito_count = 0
                inscrito_month = 1
                for inscritos in receita:
                    if inscritos.data_hora.month is not inscrito_month and inscrito_count is not 0:
                        tipo2, created = Estatisticas.objects.get_or_create(negocio=negocioid,
                                                                        tipo=2, mes=inscritos.data_hora.month -1, total=inscrito_count,
                                                                        ano=date.today().year)
                        inscrito_month = inscritos.data_hora.month
                        inscrito_count = 1
                    else:
                        inscrito_count += 1
                try:
                    tipo2.total=inscrito_count
                    tipo2.save()
                except UnboundLocalError as e:
                    print('Error creating stats unbound, ',e)
                # tipo2, created = Estatisticas.objects.get_or_create(negocio=negocioid,
                #                                                 tipo=2, mes=inscritos.data_hora.month,
                #                                                 total=inscrito_count,
                #                                                 ano=date.today().year)

                for i in month_totals:
                    q, created = Estatisticas.objects.get_or_create(negocio=negocioid,
                                                                    tipo=1, mes=i, total=month_totals[i], ano=date.today().year)



            self.stdout.write(self.style.SUCCESS('Comando foi um sucesso! Para estatisticas Normais'))
        except Exception as e:
            raise CommandError('Erro em ' % e)



