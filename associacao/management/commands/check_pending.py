"""
will send check pending payments
some of it have late delay confirmation
"""
from django.core.management.base import BaseCommand, CommandError
from associacao.models import Transacoes, EvecomData, Estatisticas
from associacao.processes import OrderProcess
import requests


class Command(BaseCommand):
    help = 'Checa se nos pagamentos pendentes existe algum ja pago'

    #
    # def add_arguments(self, parser):
    #     parser.add_argument('poll_id', nargs='+', type=int)

    def handle(self, *args, **options):
        try:
            transacoes = Transacoes.objects.filter(status='Pendente',id=32805)
            count = 0
            for pendente in transacoes:
                c = OrderProcess(pendente.cielo_data)
                print(transacoes)
                try:
                   if c.check_order(str(pendente.cielo_transactions.payment_id))['status'] == 'OK':
                       print('pagamento aceito posteriormente')
                       x = requests.post('https://planopago.com.br/retorno/',
                                         data={'PaymentId':str(pendente.cielo_transactions.payment_id)})
                       count+=1
                except:
                       print('erro em',pendente.id)
        except Exception as e:
            print('Erro enviado para o evecom em id', pendente.id, 'erro:', e)
            self.stdout.write(self.style.SUCCESS('Comando foi um sucesso! Executados ' +str(count)))
        except Exception as e:
            raise CommandError('Erro em ' % e)
