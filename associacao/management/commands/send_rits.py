"""
Will send data to third party
"""
from django.core.management.base import BaseCommand, CommandError
from associacao.models import EvecomData, IntegraTerceiros
from itertools import groupby
import requests
from urllib.parse import urlencode
from django.forms.models import model_to_dict
from datetime import datetime


class Command(BaseCommand):
    help = 'envia dados para Rits'

    #
    # def add_arguments(self, parser):
    #     parser.add_argument('poll_id', nargs='+', type=int)

    def handle(self, *args, **options):
        try:
            count = 0
            for dataevecom in EvecomData.objects.all().filter(sent=False, paid=True, tipo_integracao=2):
                try:
                    print(datetime.now().__str__() + ',tentando enviar', dataevecom.id)
                    headerrits = {'Accept': 'application/json','User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}

                    data_to_send = {'code': dataevecom.invoice,
                                    'type': 'subscription_activated'}
                    c = requests.post('https://braintv.com.br/ws/webhooks/planopago',
                                        data=data_to_send, headers=headerrits,timeout=3)
                    c = requests.post(dataevecom.integracao_terceiro.url_destino, data_to_send)
                    if c.status_code == 200:
                        dataevecom.sent = True
                        dataevecom.save()
                        print(datetime.now().__str__() + ',enviado', data_to_send)
                    else:
                        print(datetime.now().__str__() + ',erro enviado nao aceito', data_to_send, str(c.status_code))
                except Exception as e:
                    print('Erro enviado para o evecom em id ', dataevecom.id, 'erro:', e)
            self.stdout.write(self.style.SUCCESS('Comando foi um sucesso! Executados ' + str(count)))
        except Exception as e:
            raise CommandError('Erro em ' % e)
