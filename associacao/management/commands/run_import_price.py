encoding='utf-8-sig'
"""
will send check pending payments
some of it have late delay confirmation
"""
from django.core.management.base import BaseCommand, CommandError
from associacao.models import Validation, PricePlan, ListofValidatedPeople
from associacao.Conciliator import FinanceConciliation
import os
import csv


class Command(BaseCommand):
    help = 'Importa csv pra pricing'


    def add_arguments(self, parser):
        parser.add_argument('arquivo', nargs='+', type=str)
        parser.add_argument('nome priceplan', nargs='+', type=str)

    def handle(self, *args, **kwargs):
        f = open(kwargs['arquivo'][0], encoding='utf-8-sig')
        reader = csv.DictReader(f, delimiter=';', dialect='excel')
        print(reader.fieldnames)
        z, created = PricePlan.objects.get_or_create(nome=kwargs['nome priceplan'][0])
        for i in reader:
            try:
                i['CPF']
                #TODO aqui vc pode ter um CPF com emails diferentes em outras listas
                q,  qcreated = ListofValidatedPeople.objects.get_or_create(CPF_Document=i['CPF'].replace('.', '').replace('-', ''))
                q.nome = i['Nome']
                q.email = i.get('E-mail')
                q.save()
                z.lista_preco.add(q)
                z.save()
            except Exception as e:
                print('erro no ',e)

