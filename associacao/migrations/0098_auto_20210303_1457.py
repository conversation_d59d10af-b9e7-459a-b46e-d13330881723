# -*- coding: utf-8 -*-
# Generated by Django 1.9.5 on 2021-03-03 14:57
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0097_tokenassinante_nome'),
    ]

    operations = [
        migrations.CreateModel(
            name='Endereco',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('street', models.Char<PERSON>ield(max_length=200)),
                ('street_number', models.Char<PERSON>ield(max_length=200)),
                ('zipcode', models.Char<PERSON>ield(max_length=200)),
                ('country', models.CharField(max_length=200)),
                ('state', models.Char<PERSON>ield(max_length=200)),
                ('city', models.CharField(max_length=200)),
                ('neighborhood', models.CharField(blank=True, max_length=200, null=True)),
                ('complementary', models.Char<PERSON>ield(blank=True, max_length=200, null=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='planosdepagamento',
            name='recorrencia_propria',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='ativo',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='bandeira',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='cvc',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='next_date',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='next_try',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='nome',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='parcelas',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='status',
        ),
        migrations.RemoveField(
            model_name='tokenassinante',
            name='valor',
        ),
        migrations.AddField(
            model_name='transacoes',
            name='endereco',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.Endereco'),
        ),
    ]
