# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2019-07-08 17:51
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0065_evecomdata_parcelas'),
    ]

    operations = [
        migrations.CreateModel(
            name='ListofValidatedPeople',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('CPF_Document', models.CharField(max_length=200)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nome', models.CharField(blank=True, max_length=220, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PricePlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preco', models.DecimalField(decimal_places=2, default=0.0, max_digits=8)),
                ('lista_preco', models.ManyToManyField(to='associacao.ListofValidatedPeople')),
            ],
        ),
        migrations.CreateModel(
            name='Validation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField()),
                ('force_email', models.BooleanField(default=False)),
                ('entidade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='associacao.Negocio')),
                ('priceplan', models.ManyToManyField(to='associacao.PricePlan')),
            ],
        ),
    ]
