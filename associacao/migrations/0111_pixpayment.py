# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2025-01-03 12:30
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0110_negocio_pix_ativado'),
    ]

    operations = [
        migrations.CreateModel(
            name='PixPayment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('valor', models.DecimalField(decimal_places=2, max_digits=15)),
                ('pix_code', models.TextField()),
                ('pix_key', models.CharField(max_length=255)),
                ('status', models.CharField(choices=[('pending', 'Pendente'), ('paid', 'Pago'), ('expired', 'Expirado'), ('cancelled', 'Cancelado')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('pagador_nome', models.CharField(blank=True, max_length=255, null=True)),
                ('pagador_cpf', models.CharField(blank=True, max_length=14, null=True)),
                ('pagador_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('negocio', models.ForeignKey(to='associacao.Negocio')),
                ('plano_pagamento', models.ForeignKey(blank=True, null=True, to='associacao.PlanosdePagamento')),
                ('transacao', models.OneToOneField(blank=True, null=True, to='associacao.Transacoes')),
            ],
            options={
                'verbose_name': 'Pagamento PIX',
                'verbose_name_plural': 'Pagamentos PIX',
            },
        ),
    ]
