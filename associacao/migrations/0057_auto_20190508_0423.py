# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2019-05-08 04:23
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0056_auto_20190215_1141'),
    ]

    operations = [
        migrations.AddField(
            model_name='pedido',
            name='utm_campaign',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='pedido',
            name='utm_content',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='pedido',
            name='utm_medium',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='pedido',
            name='utm_source',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='pedido',
            name='utm_term',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='concilicao',
            name='tipo_concilicao',
            field=models.IntegerField(choices=[(1, 'Cielo'), (2, 'TID')], default=1),
        ),
        migrations.AlterField(
            model_name='tipodepagameno',
            name='expiracao_ano',
            field=models.CharField(choices=[('2016', '2016'), ('2017', '2017'), ('2018', '2018'), ('2019', '2019'), ('2020', '2020'), ('2021', '2021'), ('2022', '2022'), ('2023', '2023'), ('2024', '2024'), ('2025', '2025'), ('2026', '2026'), ('2027', '2027'), ('2028', '2028'), ('2029', '2029'), ('2030', '2030')], default='2019', max_length=4, verbose_name='Ano de expiração'),
        ),
    ]
