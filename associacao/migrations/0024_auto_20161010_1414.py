# -*- coding: utf-8 -*-
# Generated by Django 1.9.5 on 2016-10-10 14:14
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0023_auto_20160823_1313'),
    ]

    operations = [
        migrations.CreateModel(
            name='Boleto',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('expiration_date', models.DateField(blank=True, null=True)),
                ('url_main', models.URLField(blank=True, null=True)),
                ('number', models.CharField(blank=True, max_length=80, null=True)),
                ('barCodeNumber', models.CharField(blank=True, max_length=120, null=True)),
                ('DigitableLine', models.CharField(blank=True, max_length=150, null=True)),
                ('Address', models.CharField(blank=True, max_length=200, null=True)),
                ('PaymentId', models.CharField(blank=True, max_length=150, null=True)),
                ('Amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, max_length=15, null=True)),
                ('Type', models.CharField(default='Boleto', max_length=80)),
                ('Country', models.CharField(default='BRA', max_length=120)),
                ('ExtraDataCollection', models.CharField(blank=True, max_length=120, null=True)),
                ('Status', models.IntegerField(blank=True, null=True)),
                ('querylink_url', models.URLField(blank=True, null=True)),
                ('demonstrative', models.CharField(blank=True, max_length=450, null=True)),
                ('instructions', models.CharField(blank=True, max_length=450, null=True)),
                ('Provider', models.CharField(choices=[('Bradesco', 'Bradesco'), ('BancodoBrasil', 'BancodoBrasil')], default='BancodoBrasil', max_length=120)),
                ('ppago_status', models.CharField(choices=[('PendenteCriacao', 'PendenteCriacao'), ('PendentePagamento', 'PendentePagamento'), ('Pago', 'Pago'), ('Vencido', 'Vencido')], default='PendenteCriacao', max_length=120)),
                ('tentative', models.TextField(blank=True, null=True)),
                ('name', models.CharField(blank=True, max_length=250, null=True)),
                ('html_source', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Estatisticas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mes', models.IntegerField()),
                ('tipo', models.IntegerField()),
                ('total', models.DecimalField(decimal_places=2, max_digits=15)),
            ],
        ),
        migrations.CreateModel(
            name='Mensagem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titulo', models.CharField(max_length=120)),
                ('text', models.CharField(default=models.CharField(max_length=120), max_length=200)),
                ('lida', models.BooleanField(default=False)),
                ('tipo', models.IntegerField(choices=[('1', 'mensagem'), ('2', 'Calendario')])),
            ],
        ),
        migrations.RenameField(
            model_name='pessoa',
            old_name='admin',
            new_name='admin_negocio',
        ),
        migrations.RenameField(
            model_name='planosdepagamento',
            old_name='mensagem_pagamento',
            new_name='descricao_curta',
        ),
        migrations.AddField(
            model_name='cielodata',
            name='tipo_api',
            field=models.CharField(default='1.5', max_length=30),
        ),
        migrations.AddField(
            model_name='globaloptions',
            name='dias_pagamento',
            field=models.IntegerField(default=10),
        ),
        migrations.AddField(
            model_name='negocio',
            name='CEP',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='negocio',
            name='UF',
            field=models.CharField(blank=True, max_length=2, null=True),
        ),
        migrations.AddField(
            model_name='negocio',
            name='cidade',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='negocio',
            name='dias_libera_dinheiro',
            field=models.IntegerField(blank=True, default=10, null=True),
        ),
        migrations.AddField(
            model_name='negocio',
            name='integracao_Analytics',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='pedido',
            name='items',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='pedido',
            name='status',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='pedido',
            name='valor',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='pessoa',
            name='photo',
            field=models.FileField(blank=True, null=True, upload_to='/avatar/'),
        ),
        migrations.AddField(
            model_name='planosdepagamento',
            name='tipo',
            field=models.CharField(blank=True, choices=[('Anuidade', 'Anuidade'), ('Evento', 'Evento'), ('Serviço', 'Serviço')], max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='transacoes',
            name='pedido_saque',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='transacoes',
            name='pedido_saque_data',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='transacoes',
            name='saque_efetuado',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='transacoes',
            name='saque_efetuado_data',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cielodata',
            name='numero',
            field=models.CharField(max_length=128),
        ),
        migrations.AlterField(
            model_name='negocio',
            name='CNPJ',
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AlterField(
            model_name='tipodepagameno',
            name='bandeira',
            field=models.CharField(blank=True, choices=[('Visa', 'Visa'), ('Master', 'Mastercard'), ('Diners', 'Diners'), ('Discover', 'Discover'), ('Elo', 'Elo'), ('JCB', 'JCB'), ('Aura', 'Aura'), ('Amex', 'Amex')], max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='transacoes',
            name='status_code',
            field=models.IntegerField(blank=True, choices=[(0, 'Pago'), (1, 'Pendente'), (2, 'Cancelada')], null=True),
        ),
        migrations.AddField(
            model_name='mensagem',
            name='destino',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='associacao.Pessoa'),
        ),
        migrations.AddField(
            model_name='estatisticas',
            name='negocio',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='associacao.Negocio'),
        ),
        migrations.AddField(
            model_name='boleto',
            name='cobranca',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.PlanosdePagamento'),
        ),
        migrations.AddField(
            model_name='boleto',
            name='negocio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.Negocio'),
        ),
        migrations.AddField(
            model_name='boleto',
            name='pessoa',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.Pessoa'),
        ),
    ]
