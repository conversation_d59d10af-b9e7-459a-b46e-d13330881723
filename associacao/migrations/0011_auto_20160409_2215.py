# -*- coding: utf-8 -*-
# Generated by Django 1.9.5 on 2016-04-09 22:15
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0010_planosdepagamento_nome_plano'),
    ]

    operations = [
        migrations.CreateModel(
            name='TipoDePagameno',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_escolhido', models.IntegerField(choices=[('Cartao Credito', 0), ('Cartao Debito', 1), ('Boleto', 2)])),
                ('nome_cartao', models.CharField(blank=True, max_length=120, null=True)),
                ('numero_cartao', models.CharField(blank=True, max_length=120, null=True)),
                ('expiracao', models.Char<PERSON><PERSON>(blank=True, max_length=12, null=True)),
                ('bandeira', models.CharField(blank=True, choices=[('Visa', 'Visa'), ('Mastercard', 'Mastercard')], max_length=100, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='pessoa',
            name='escolha_pagammento',
            #troquei o default de 0 para 1
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='associacao.TipoDePagameno'),
            preserve_default=False,
        ),
    ]
