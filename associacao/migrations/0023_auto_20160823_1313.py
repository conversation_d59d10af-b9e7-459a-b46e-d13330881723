# -*- coding: utf-8 -*-
# Generated by Django 1.9.5 on 2016-08-23 13:13
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0022_transacoes_status_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON>nc<PERSON>',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=120)),
                ('tipo', models.CharField(choices=[('Evento', 'Evento'), ('Anuidade', 'Anuidade'), ('Curso', 'Curso'), ('Serviços', 'Serviços')], max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='CobrancaGrupo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome_grupo', models.CharField(max_length=120)),
            ],
        ),
        migrations.CreateModel(
            name='CobrancaItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('descricao', models.CharField(blank=True, max_length=150, null=True)),
                ('valor', models.DecimalField(decimal_places=2, max_digits=15)),
                ('negocio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='associacao.Negocio')),
            ],
        ),
        migrations.CreateModel(
            name='GlobalOptions',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('menu_associado', models.CharField(blank=True, max_length=200, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Pedido',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('datahora', models.DateTimeField(auto_now_add=True)),
                ('negocio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.Negocio')),
                ('transacao', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.Transacoes')),
                ('usuario', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.Pessoa')),
            ],
        ),
        migrations.RemoveField(
            model_name='pagamentos',
            name='usuario',
        ),
        migrations.AddField(
            model_name='planosdepagamento',
            name='categoria',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.DeleteModel(
            name='Pagamentos',
        ),
        migrations.AddField(
            model_name='cobranca',
            name='grupo',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='associacao.CobrancaGrupo'),
        ),
        migrations.AddField(
            model_name='cobranca',
            name='items',
            field=models.ManyToManyField(to='associacao.CobrancaItem'),
        ),
        migrations.AddField(
            model_name='cobranca',
            name='usuarios_permitdos',
            field=models.ManyToManyField(blank=True, null=True, to='associacao.Pessoa'),
        ),
    ]
