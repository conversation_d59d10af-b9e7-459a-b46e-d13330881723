# -*- coding: utf-8 -*-
# Generated by Django 1.9.5 on 2016-10-17 12:17
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('associacao', '0025_cielotransaction_payment_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cobranca',
            name='items',
            field=models.ManyToManyField(blank=True, to='associacao.CobrancaItem'),
        ),
        migrations.AlterField(
            model_name='cobranca',
            name='usuarios_permitdos',
            field=models.ManyToManyField(blank=True, to='associacao.Pessoa'),
        ),
        migrations.AlterField(
            model_name='tipodepagameno',
            name='codigo_seguranca',
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name='tipodepagameno',
            name='expiracao_ano',
            field=models.CharField(choices=[('2016', '2016'), ('2017', '2017'), ('2018', '2018'), ('2019', '2019'), ('2020', '2020'), ('2021', '2021'), ('2022', '2022'), ('2023', '2023'), ('2024', '2024'), ('2025', '2025'), ('2026', '2026'), ('2027', '2027'), ('2028', '2028'), ('2029', '2029'), ('2030', '2030')], default='2018', max_length=4, verbose_name='Ano de expiração'),
        ),
        migrations.AlterField(
            model_name='tipodepagameno',
            name='expiracao_mes',
            field=models.CharField(choices=[('01', '01'), ('02', '02'), ('03', '03'), ('04', '04'), ('05', '05'), ('06', '06'), ('07', '07'), ('08', '08'), ('09', '09'), ('10', '10'), ('11', '11'), ('12', '12')], default='01', max_length=2, verbose_name='Mês de expiração'),
        ),
        migrations.AlterField(
            model_name='tipodepagameno',
            name='tipo_escolhido',
            field=models.IntegerField(choices=[(0, 'Cartão Credito'), (1, 'Cartão Débito')], default=0),
        ),
    ]
