# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Pessoa',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('logradouro', models.CharField(max_length=300, null=True, blank=True)),
                ('numero', models.CharField(max_length=10, null=True, blank=True)),
                ('complemento', models.CharField(max_length=10, null=True, blank=True)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='PessoaPermissions',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('admin', models.BooleanField(default=False)),
                ('financeiro', models.<PERSON>olean<PERSON>ield(default=False)),
                ('descricao', models.CharField(max_length=100, null=True, blank=True)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='PlanosdePagamento',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('data_ciclo', models.DateField(null=True, blank=True)),
                ('frequencia_invervalo', models.CharField(max_length=40, choices=[(b'Mensal', b'M'), (b'Diario', b'D'), (b'Semanal', b'W'), (b'Anual', b'Y')])),
                ('frequencia', models.IntegerField()),
                ('trial', models.BooleanField(default=False)),
                ('montante', models.DecimalField(max_digits=10, decimal_places=2)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='pessoa',
            name='permissoes',
            field=models.ForeignKey(to='associacao.PessoaPermissions'),
            preserve_default=True,
        ),
    ]
