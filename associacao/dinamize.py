import json

import requests


class DinamizeProcess(object):
    """
    Main class for dinamize mar
    """

    def __init__(self):
        self.token_auth = None
        self.signed = False
        self.auth_headers = {'Content-Type': 'application/json; charset=utf-8'}
        self.request_headers = {'Content-Type': 'application/json; charset=utf-8'}
        self.client_code = '312520'

    def get_token(self, username, password):
        """
        will do the auth and set the token
        :param username:
        :param password:
        :return:
        """
        self.auth_headers['user'] = username
        self.auth_headers['password'] = password
        self.auth_headers['client_code'] = self.client_code
        ap = requests.post('https://api.dinamize.com/auth', self.auth_headers)
        try:
            if ap.json().get('body') is not None:
                self.signed = True
                self.token_auth = ap.json().get('body').get('auth-token')
                self.request_headers['auth-token'] = self.token_auth
                self.signed = True
                return {'status': 'OK'}
        except Exception as e:
            print('error authing dinamize', e)
            return {'status': 'Error', 'data': e}

    def upsert(self, emailtosearch, campopersonalizado, valorcampopersonalizado):
        """
        Doing upser
        :param emailtosearch: '<EMAIL>'
        :param campopersonalizado: 'cmp4'
        :param valorcampopersonalizado: '1'
        :return: status
        """
        if self.signed is True:
            header = self.request_headers
            req = {'email': emailtosearch, 'update_strategy': 'MERGE',
                   'custom_fields': {campopersonalizado: [valorcampopersonalizado]}, 'contact-list_code': '1'}
            try:
                upsert = requests.post('https://api.dinamize.com/emkt/contact/upsert', json.dumps(req), headers=header)
                if upsert.status_code == 200:
                    return {'status': 'OK', 'data': upsert.content}
                else:
                    return {'status': 'Error', 'data': upsert.content}
            except Exception as e:
                return {'status': 'Error', 'data': e}
