"""
This file will have the logic and process for this app

"""
import datetime
from associacao.cieloprocessor import CieloProcess, CieloProcess30, CieloProcess30Homologacao, CieloTransaction
from .models import Conta, Transacoes, Pessoa, PlanosdePagamento, Pedido, CieloData, TokenAssinante
from cielo_webservice.exceptions import CieloRequestError
import pagarme, sys
from associacao.stoneprocessor import StoneProcess
from django.core import serializers


class OrderProcess(object):
    """
    main class for processing orders from views
    """

    def __init__(self, cielo_data):
        self.orderid = ''
        self.total = ''
        self.usercpf = ''
        self.userid = ''
        self.paymentprocessor = ''
        self.datetime = datetime.datetime.now()
        self.result = {}
        self.items = []
        self.planoid = None
        self.cielo_data = cielo_data
        self.save_card = False
        try:
            self.tipo_api = cielo_data.tipo_api
            self.api = self.tipo_api
        except Exception as e:
            print(e)
        super().__init__()

    def order_buy(self, planoid, usercpf, paymentprocessor, numero,
                  validade_ano, validade_mes, seguranca, nome, bandeira,
                  email, items=None, usuario=None, homologacao=None, tipo=0,
                  parcelas=1, desconto_a_vista=None, assinatura=False, trial = False,
                  dias_pra_inciar_assinatura=30, valorflexivel=None, periodicidade_assinatura='Monthly',
                  convenio=None, endereco=None):
        """
        buy order
        :param usuario:
        :param planoid:
        :param numero:
        :param email:
        :param bandeira:
        :param nome:
        :param seguranca:
        :param validade_mes:
        :param validade_ano:
        :param items:
        :param usercpf:
        :param paymentprocessor:
        :return: {}
        """
        if items:
            self.items = items
        if usuario:
            pedido = Pedido.objects.create(usuario=usuario)
        else:
            pedido = Pedido.objects.create()
        plano = PlanosdePagamento.objects.filter(id=planoid)
        if paymentprocessor == 'cielo' or paymentprocessor == '3.0' or paymentprocessor == '1.5':
            if desconto_a_vista and parcelas == 1:
                #TODO TESTAR o desconto aplicado somente em parcela unica
                valor = int(str(plano[0].valor_a_vista).replace('.', ''))
            else:
                valor = self.ajusta_valor(plano)
            if plano[0].valor_flexivel:
                print('valor eh flexivel,', valorflexivel)
                valor = int(str(valorflexivel).replace('.', ''))
        if paymentprocessor == 'stone':
            cy = StoneProcess()
            cy.cielo_data = self.cielo_data
            params = {"amount": str(plano[0].montante).replace('.',''),
                      "card_number": numero,
                      "card_cvv": seguranca,
                    "card_expiration_date": validade_mes+validade_ano[-2:],
                    "card_holder_name": nome,
                    "customer": {
                      "external_id": str(pedido.id),
                      "name": nome,
                      "type": "individual",
                      "country": "br",
                      "email": email,
                      "documents": [
                        {
                          "type": "cpf",
                          "number": usercpf
                        }
                      ],
                      # "phone_numbers": ["+5511999998888"]
                    },
                    # "billing": {
                    #   "name": nome,
                    #   "address":{
                    #                 "country": endereco.country,
                    #                 "state": endereco.state,
                    #                 "city": endereco.city,
                    #                 "street": endereco.street,
                    #                 "street_number": endereco.street_number,
                    #                 "zipcode": endereco.zipcode}
                    # },
                    "items": [
                      {
                        "id": "r123",
                        "title": plano[0].nome_plano,
                        "unit_price": str(plano[0].montante).replace('.',''),
                        "quantity": "1",
                        "tangible": False
                      }]}
            if assinatura:
                print('process eh assinatura na stone,',dias_pra_inciar_assinatura)
                cy.RecurrentPayment = True
                params.pop('items')
                params.pop('amount')

                if trial:
                    cy.RecurrentPayment_Trial = True
                    cy.RecurrentPayment_Trial_days = dias_pra_inciar_assinatura
                if periodicidade_assinatura:
                    cy.RecurrentPayment_Interval = periodicidade_assinatura
                    print('peridicidade da assinatura eh,', periodicidade_assinatura)
                if parcelas == 1 or parcelas is None:
                    params['plan_id'] = plano[0].assinatura_no_backend_1x
                elif parcelas == 2:
                    params['plan_id'] = plano[0].assinatura_no_backend_2x
                elif parcelas == 3:
                    params['plan_id'] = plano[0].assinatura_no_backend_3x
                elif parcelas == 4:
                    params['plan_id'] = plano[0].assinatura_no_backend_4x
                elif parcelas == 5:
                    params['plan_id'] = plano[0].assinatura_no_backend_5x
                elif parcelas == 6:
                    params['plan_id'] = plano[0].assinatura_no_backend_6x

                print('api stone')
            print(params)

            cy.params = params
            #stone = pagarme.authentication_key(cielodata.numero)
            #trx = pagarme.transaction.create(params)
            #print(trx)
            
        # else:
        #     valor = plano[0].montante
        self.usercpf = usercpf
        print('self api eh ', self.tipo_api)
        self.paymentprocessor = paymentprocessor
        if self.tipo_api == '1.5':
            cy = CieloProcess()
            self.api = '1.5'
            validade = int(validade_ano + validade_mes)
            if bandeira == 'master':
                bandeira = 'mastercard'
            cy.set_pagamento(bandeira, '1')
            cy.set_payment_data(int(numero), validade, 1, int(seguranca), nome)
            cy.set_pedido(pedido, valor=valor)
            print('api 1.5')
        if self.tipo_api == '3.0':
            print('api eh 3.0')
            self.api = '3.0'
            if homologacao:
                cy = CieloProcess30Homologacao()
            else:
                cy = CieloProcess30(self.cielo_data)
            validade = validade_mes + '/' + validade_ano
            if assinatura:
                print('process eh assinatura,',dias_pra_inciar_assinatura)
                cy.RecurrentPayment = True
                if trial:
                    cy.RecurrentPayment_Trial = True
                    cy.RecurrentPayment_Trial_days = dias_pra_inciar_assinatura
                if periodicidade_assinatura:
                    cy.RecurrentPayment_Interval = periodicidade_assinatura
                    print('peridicidade da assinatura eh,', periodicidade_assinatura)
                print('api 3')
            if tipo == 2:  # PIX
                cy.set_pix_data(nome, usercpf, email)
                if plano[0].descricao_curta is not None:
                    cy.set_pedido(pedido, valor=valor, softdescriptor=plano[0].descricao_curta)
                else:
                    cy.set_pedido(pedido, valor=valor)
            else:  # Cartão de crédito/débito
                cy.set_payment_data(numero, validade, 1, seguranca, nome, tipo, bandeira)
                if plano[0].descricao_curta is not None:
                    cy.set_pedido(pedido, valor=valor,  parcelas=parcelas, softdescriptor=plano[0].descricao_curta)
                else:
                    cy.set_pedido(pedido, valor=valor, parcelas=parcelas)

        print('fazendo transacao')
        print(cy.set_transacao())
        try:
            cy.executar_transacao()
        except CieloRequestError as e:
            print("Erro no preenchimento dos dados \n Compra não foi finalizada ")
            print(str(e))
            return {'status': 'error', 'data': e}
        try:
            cielotransid = cy.save_transaction()
        except Exception as e:
            print('impossible to save it', e)
        print('executada transacao')
        final_result = cy.final_status()
        print('if finais')
        if final_result['api'] == '1.5':
            mensagem = final_result['data'].autorizacao.mensagem
        if final_result['api'] == '3.0':
            mensagem = final_result['data']
        if final_result['api'] == 'stone':
            mensagem = final_result['data']
        if final_result['status'] == 'OK':
            print('status ok devo gravar')
            # if assinatura:
            #     montante_real = 0.00
            #     cielotransid = 'assinatura'
            # else:
            montante_real = (str(cielotransid.valor)[:-2] + '.' + str(cielotransid.valor)[-2:])
            if usuario:
                conta, created = Conta.objects.get_or_create(usuario=usuario)
                transacao = Transacoes.objects.create(cielo_transactions=cielotransid, montante=montante_real,
                                                      conta=conta,
                                                      destino=plano[0].entidade, status='Pago'
                                                      , plano_pagamento=plano[0], usuario=usuario,
                                                      cielo_data=self.cielo_data)
                pedido.usuario = usuario
                pedido.transacao = transacao
                pedido.negocio = plano[0].entidade
                pedido.status = 'finalizado'
                pedido.valor = montante_real

            else:
                usuario, created = Pessoa.objects.get_or_create(CPF=self.usercpf, email=email)
                conta, created = Conta.objects.get_or_create(usuario=usuario)
                transacao = Transacoes.objects.create(cielo_transactions=cielotransid, montante=montante_real,
                                                      conta=conta,
                                                      destino=plano[0].entidade, status='Pago',
                                                      plano_pagamento=plano[0], usuario=usuario,
                                                      cielo_data=self.cielo_data)
                pedido.transacao = transacao
                pedido.negocio = plano[0].entidade
                pedido.status = 'finalizado'
                pedido.valor = montante_real
            if assinatura and final_result['api'] == '3.0':
                token = TokenAssinante.objects.create(pessoa=usuario,
                                                      nome_gateway=plano[0].cielo_data.nome,
                                                      planos_pagamento=plano[0])
                transacao.status = 'Assinatura'
                token.transacoes.add(transacao)
                token.save()

            pedido.cielo_transacao = cielotransid
            pedido.plano_pagamento = plano[0]
            pedido.cielo_data = self.cielo_data
            pedido.save()
            print(final_result)
            return {'status': 'OK', 'data': mensagem, 'plano': plano[0], 'rawdata': cy.transacao_resultado,
                    'pedido': pedido, 'transacao': transacao}

        elif final_result['status'] != 'OK' and final_result['type'] == 'exception':
            pedido.negocio = plano[0].entidade
            pedido.status = 'erro'
            pedido.valor = plano[0].montante
            pedido.save()
            return {'status': 'error', 'data': "Autorizacao Negada", 'plano': plano[0], 'pedido': pedido}
        elif final_result['status'] == 'pending' and final_result.get('DebitCard') is True:
            montante_real = (str(cielotransid.valor)[:-2] + '.' + str(cielotransid.valor)[-2:])
            usuario, created = Pessoa.objects.get_or_create(CPF=self.usercpf, email=email)
            conta, created = Conta.objects.get_or_create(usuario=usuario)
            transacao = Transacoes.objects.create(cielo_transactions=cielotransid, montante=montante_real,
                                                  conta=conta,
                                                  destino=plano[0].entidade, status='Pendente',
                                                  plano_pagamento=plano[0], usuario=usuario,
                                                  cielo_data=self.cielo_data)
            pedido.transacao = transacao
            pedido.negocio = plano[0].entidade
            pedido.status = 'pending'
            pedido.valor = montante_real
            pedido.usuario = usuario
            pedido.cielo_transacao = cielotransid
            pedido.plano_pagamento = plano[0]
            pedido.cielo_data = self.cielo_data
            pedido.save()
            print('criado pedido pendente de debito', usuario.email, pedido.id)
            return {'status': 'pending', 'data': final_result.get('data'), 'plano': plano[0],
                    'rawdata': cy.transacao_resultado,
                    'ReturnCode': cy.transacao_resultado.get('Payment').get('ReturnCode'),
                    'MerchantId': cy.MerchantId, 'DebitCard': True, 'Redirect': True,
                    'RedirectURL': final_result.get("rawdata").get("Payment").get("AuthenticationUrl"),
                    'ReturnUrl': final_result.get("rawdata").get("Payment").get("ReturnUrl"), 'pedido': pedido}

        elif final_result['status'] != 'OK' and final_result['type'] == 'cielo':
            if final_result['api'] == '1.5':
                return {'status': 'error', 'data': mensagem, 'plano': plano[0]}
            if final_result['api'] == '3.0':
                if isinstance(cy.transacao_resultado, list):
                    print('erro 500 na transacao', cy.transacao_resultado)
                    returncode = 'Erro no pagamento! Verifique seus dados e tente novamente'
                else:
                    returncode = cy.transacao_resultado.get('Payment').get('ReturnCode')
                return {'status': 'error', 'data': final_result.get('data'), 'plano': plano[0],
                        'rawdata': cy.transacao_resultado,
                        'ReturnCode': returncode,
                        'MerchantId': cy.MerchantId, 'pedido': pedido
                        }

    @staticmethod
    def ajusta_valor(plano):
        # TODO testes ajusta_valor centavos
        valor = int(str(plano[0].montante).replace('.', ''))
        return valor

    def cria_assinatura(self,plano, cpf,  numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario,
                                         tipo,assinatura=True, days_free=0, homologacao=False):
        self.homologacao = False
        if plano.cielo_data.tipo_api == '3.0':
            self.api = '3.0'
            if self.homologacao:
                cy = CieloProcess30Homologacao()
            else:
                cy = CieloProcess30(self.cielo_data)
        try:
            x =cy.criar_token_cartao(nome,numero,nome,validade_mes+'/'+validade_ano,bandeira)
            if x['status']=='OK':
                token = x['rawdata']['CardToken']
        except Exception as e:
            print('erro no processes ao pegar token',x)
            return {'status':'Error','data':e}
        try:
            usuario, created = Pessoa.objects.get_or_create(CPF=self.usercpf, email=email)
            conta, created = Conta.objects.get_or_create(usuario=usuario)
            TokenAssinante.objects.create(**{'pessoa':usuario,'token':token,'nome_gateway':plano.cielo_data.nome,
                                       'planos_pagamento':plano,'start_date':datetime.datetime.now()})
        except Exception as e:
            print('nao conseguir criar token',e)

        usuario, created = Pessoa.objects.get_or_create(CPF=self.usercpf, email=email)
        conta, created = Conta.objects.get_or_create(usuario=usuario)
        try:

            transacao = Transacoes.objects.create(montante=0.00,
                                              conta=conta,
                                              destino=plano.entidade, status='Assinatura',
                                              plano_pagamento=plano, usuario=usuario,
                                              cielo_data=self.cielo_data)
            return {'status': 'OK', 'data':{'transacao': transacao, 'usuario': usuario,
                                            'conta': conta,'resultado_token':x['data']}}
        except Exception as e:
            return {'status': 'Error', 'data':e}
    def order_cancel(self, transaction, processor='cielo'):
        """
        cancel the order and chargeback the customer
        :param tid: string
        :param processor: string
        :return: dictionary
        """
        recurrent = False
        if processor == 'cielo':
            if self.tipo_api == '1.5':
                c = CieloProcess()
                tid = transaction.cielo_transactions.tid
            if self.tipo_api == '3.0':
                c = CieloProcess30(self.cielo_data)
                tid = transaction.cielo_transactions.payment_id
                if tid is None:
                    print('tid eh none, vou procurar assinatura')
                    tid = transaction.cielo_transactions.recurrentpayment_id
                    if tid is not None:
                        recurrent = True
        if recurrent:
            x = c.cancelar_transacao(tid, recurrentpayment=recurrent)
        else:
            x = c.cancelar_transacao(tid)
        print('imprimindo x', x)
        if x.get('Status') == 10 or x.get('Status') == 11:
            return {'status': 'OK', 'data': x}
        if x.get('ReturnCode') != '9':
            return {'status': 'error', 'data': x}

    def create_token(self):
        """create token from credit card"""


    def check_order(self, transactionid, processor='cielo'):
        """
        check if the order is ok(paid)
        :param tid: string
        :param processor: string
        :return: dictionary
        """
        #Done return error if none cases
        #TODO : check order test
        if processor == 'cielo':
            if self.tipo_api == '1.5':
                c = CieloProcess()
                tid = transactionid
            if self.tipo_api == '3.0':
                c = CieloProcess30(self.cielo_data)
                tid = transactionid
        x = c.consultar_venda(tid)
        print('imprimindo x', x)
        if x.get('Payment').get('Status') == 1 or x.get('Payment').get('Status') == 2:
            return {'status': 'OK', 'data': x}
        if x.get('ReturnCode') != '9':
            return {'status': 'error', 'data': x}
        return {'status': 'error', 'data': x}

    def cria_boleto(self, montante, nome, cielo_data, plano=None, CPF=None):
        if self.tipo_api == '3.0':
            c = CieloProcess30(self.cielo_data)
            montante = str((montante * 100))[:-3]
        x = c.gera_boleto(str(montante), nome)
        print(x, 'gerado boleto')
        resultado = c.final_status_boleto()
        print('final status boleto', x)
        boleto = c.save_transaction_boleto()
        return resultado, boleto

    def cria_cielo_transaction(self, jsondata):
        resultado = jsondata
        try:
            print(resultado['Payment']['PaymentId'])
            query = CieloTransaction.objects.create(tid=resultado['Payment']['Tid'],
                                                    valor=resultado['Payment']['Amount'], descricao=''
                                                    )
            try:
                query.autorizacao_lr = resultado['Payment']['ReturnCode']
            except:
                print('sem ReturnCode')
            try:
                query.soft_descriptor = resultado['Payment']['SoftDescriptor']
            except KeyError:
                print('Sem softdescriptor')
            try:
                query.captura_codigo = resultado['Payment']['ReturnCode']
            except KeyError:
                print('sem captura codigo')
            try:
                query.payment_id = resultado['Payment']['PaymentId']
            except KeyError:
                print('sem paymentid')
            try:
                query.autorizacao_codigo = '3.0'
            except KeyError:
                print('sem autorizacao codigo')
            try:
                query.data_hora = resultado['Payment']['CapturedDate']
            except KeyError:
                print('sem data hora capture')
            try:
                query.pedido_numero = resultado['MerchantOrderId']
            except KeyError:
                print('sem numero pedido')
            # try:
            #     query.moeda = resultado['Payment']['Currency']
            # except KeyError:
            #     print('sem currency')
            try:
                query.bandeira = resultado['Payment']['CreditCard']['Brand']
            except KeyError:
                print('sem bandeira')
            try:
                query.produto = resultado['Payment']['Type']
            except KeyError:
                print('sem resultado payment')
            try:
                query.parcelas = resultado['Payment']['Installments']
            except KeyError:
                print('sem installments')
            try:
                query.arp = resultado['Payment']['AuthorizationCode']
            except KeyError:
                print('sem payment authorizationcode')
            try:
                query.nsu = resultado['Payment']['ProofOfSale']
            except KeyError:
                print('sem nsu')

            print(query.id)
            query.save()
            return query
        except AttributeError as e:
            print('erro no criar transaction', e)
            query = CieloTransaction.objects.create(tentative=resultado)
            query.save()
            return query
            # return super().save_transaction()

    def processa_pendente(self, paymentid, pedido):
        """
        will finish a pendent by gateway error
        :param paymentid:
        :param pedido:
        :return:
        """
        paid = self.check_order(paymentid)
        if paid['status'] == 'OK':
            print('foi paga', paymentid, paid)
            resultado = paid['data']
            print(resultado)
            print('criando cielo_transaction')
            cielotrans = self.cria_cielo_transaction(resultado)
            pedido.cielo_transacao = cielotrans
            pedido.valor = pedido.plano_pagamento.montante
            usuario, created = Pessoa.objects.get_or_create(CPF='00000000', email='<EMAIL>')
            conta, created = Conta.objects.get_or_create(usuario=usuario)
            transacao = Transacoes.objects.create(cielo_transactions=cielotrans, montante=pedido.valor,
                                                  destino=pedido.plano_pagamento.entidade, status='Pago',
                                                  conta=conta, usuario=usuario,
                                                  plano_pagamento=pedido.plano_pagamento,
                                                  cielo_data=self.cielo_data)
            pedido.transacao = transacao
            pedido.negocio = pedido.plano_pagamento.entidade
            pedido.status = 'finalizado'
            #pedido.valor = montante_real
            pedido.cielo_transacao = cielotrans
            pedido.cielo_data = self.cielo_data
            pedido.evecom_item.paid = True
            pedido.evecom_item.save()
            pedido.save()

