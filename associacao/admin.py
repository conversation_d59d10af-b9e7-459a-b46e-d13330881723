from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin

from associacao.models import *


class AssociadoinLine(admin.StackedInline):
    model = Pessoa
    can_delete = False
    verbose_name_plural = 'Associado'


class UserAdmin(BaseUserAdmin):
    inlines = (AssociadoinLine, )

class PlanoParcelamentoInline(admin.TabularInline):
    model = PlanoParcelamento

class PlanoParcelamentoADmin(admin.ModelAdmin):
    inlines = [
        PlanoParcelamentoInline,
    ]

class NegocioAdmin(admin.ModelAdmin):
    list_display = ['name', 'cartao_ativado', 'boleto_ativado', 'deposito_ativado', 'pix_ativado']
    list_filter = ['cartao_ativado', 'boleto_ativado', 'deposito_ativado', 'pix_ativado']
    fieldsets = (
        ('Informações Básicas', {
            'fields': ('name', 'logradouro', 'telefone', 'complemento', 'cidade', 'UF', 'CEP', 'CNPJ', 'email')
        }),
        ('Configurações de Pagamento', {
            'fields': ('cartao_ativado', 'boleto_ativado', 'deposito_ativado', 'pix_ativado')
        }),
        ('Administração', {
            'fields': ('admin', 'dias_libera_dinheiro')
        }),
    )


admin.site.unregister(User)
admin.site.register(User, UserAdmin)
# admin.site.register(PessoaPermissions)
admin.site.register(Pessoa)
admin.site.register(PlanosdePagamento)
admin.site.register(TipoDePagameno)
admin.site.register(GlobalOptions)
admin.site.register(CieloData)
admin.site.register(Conta)
admin.site.register(Transacoes)
admin.site.register(Negocio, NegocioAdmin)
admin.site.register(CieloTransaction)
admin.site.register(Pedido)
admin.site.register(Estatisticas)
admin.site.register(EvecomData)
admin.site.register(Boleto)
admin.site.register(NegocioRecenteAcoes)
admin.site.register(TemplateOrdemEvecom)
admin.site.register(Concilicao)
admin.site.register(TaxaGatewayPagamentos)
admin.site.register(PlanoParcelamento)
admin.site.register(IntegraTerceiros)
admin.site.register(PricePlan)
admin.site.register(Validation)
admin.site.register(ListofValidatedPeople)
admin.site.register(TokenAssinante)
admin.site.register(IntegraMarketing)
admin.site.register(PixPayment)

# Register your models here.
