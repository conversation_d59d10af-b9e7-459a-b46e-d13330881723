"""
Main file for stone operations
"""

import pagarme
from datetime import datetime, date, timedelta
from associacao.models import CieloData, CieloTransaction
from uuid import uuid4


class StoneProcess():
    """
    main class
    """

    def __init__(self):
        self.cartao = None
        self.pedido = None
        self.pagamento = None
        self.transacao = None
        self.cielo_data = None
        self.params = {}
        self.items = {}
        self.transacao_resultado = None
        self.MerchantId = None
        self.MerchantKey = None
        self.requestid = str(uuid4())
        self.CreditCard = {}
        self.DebitCard = {}
        self.Payment = {}
        self.Customer = {}
        self.MerchantOrderId = None
        self.valor = None
        self.softdescriptor = ''
        self.tipo = None
        self.salvar_cartao = None
        self.RecurrentPayment = None
        self.RecurrentPayment_Trial = False
        self.RecurrentPayment_Interval = 'Monthly'
        self.RecurrentPayment_Trial_days = 30
        self.RecurrentPayment_StartDate = (date.today() + timedelta(days=30)).isoformat()
        self.planoid_no_backend = None

        # self.session.mount('https://', MyAdapter())
        super().__init__()

    def set_payment_data(self, numero_cartao, validade, indicador, codigo_seguranca, nome_portador, tipo, bandeira,
                         parcelas=1):
        self.params['card_number'] = numero_cartao
        self.params['card_expiration_date'] = validade
        self.params['card_cvv'] = codigo_seguranca
        self.params['card_holder_name'] = nome_portador


        super().set_payment_data(numero_cartao, validade, indicador, codigo_seguranca, nome_portador, tipo, bandeira,
                                 parcelas)

    def set_pedido(self, pedido, valor, moeda=986, softdescriptor=None, parcelas=1, assinatura=False,
                   dias_para_iniciar_assinatura=0):
        self.params['amount'] = valor
        self.params['soft_descriptor'] = softdescriptor
        self.params['installments'] = parcelas
        self.MerchantOrderId = str(pedido.num_pedido)
        self.items['id'] = self.pedido.plano_pagamento_id
        self.items['title'] = self.pedido.nome
        self.items['unit_price'] = valor
        self.items['quantity'] = "1"
        self.items['tangible'] = False



        
        super().set_pedido(pedido, valor, moeda, softdescriptor, parcelas, assinatura, dias_para_iniciar_assinatura)

    def set_pagamento(self, bandeira, produto, parcelas=1):
        super().set_pagamento(bandeira, produto, parcelas)

    def fazer_requisicao(self, jsonenviar, directoryurl=None, METHOD=None):
        return super().fazer_requisicao(jsonenviar, directoryurl, METHOD)

    def consultar_venda(self, paymentid):
        return super().consultar_venda(paymentid)

    def executar_transacao(self, sandbox=True):
        print('tentando stone', self.params)
        pagarme.authentication_key(self.cielo_data.numero)
        if self.RecurrentPayment:
            try:
                trx = pagarme.subscription.create(self.params)
            except Exception as e:
                trx = e
        else:
            try:
                trx = pagarme.transaction.create(self.params)
            except Exception as e:
                trx = e
        self.transacao_resultado = trx
        print(trx)

    def cancelar_transacao(self, tid, recurrentpayment=None):
        return super().cancelar_transacao(tid, recurrentpayment)

    def criar_token_cartao(self, name, cardnumber, holder, expirationDate, brand):
        return super().criar_token_cartao(name, cardnumber, holder, expirationDate, brand)

    def save_transaction(self):
        """
        {'object': 'transaction', 'status': 'paid', 'refuse_reason': None,
         'status_reason': 'acquirer', 'acquirer_response_code': '0000',
          'acquirer_name': 'pagarme', 'acquirer_id': '60380452bca1f4001142f7ce',
           'authorization_code': '634474', 'soft_descriptor': None, 'tid': 11528220,
            'nsu': 11528220, 'date_created': '2021-03-05T19:26:40.944Z',
             'date_updated': '2021-03-05T19:26:41.171Z', 'amount': 10000,
              'authorized_amount': 10000, 'paid_amount': 10000, 'refunded_amount': 0,
               'installments': 1, 'id': 11528220, 'cost': 30, 'card_holder_name': '                ',
                'card_last_digits': '5255', 'card_first_digits': '491680', 'card_brand': 'visa',
                 'card_pin_mode': None, 'card_magstripe_fallback': False, 'cvm_pin': False,
                  'postback_url': None, 'payment_method': 'credit_card', 'capture_method': 'ecommerce',
                   'antifraud_score': None, 'boleto_url': None, 'boleto_barcode': None, 'boleto_expiration_date': None,
                    'referer': 'api_key', 'ip': '*************',
                     'subscription_id': None, 'phone': None, 'address': None,
                      'customer': {'object': 'customer', 'id': 4836000, 'external_id': '472',
                       'type': 'individual', 'country': 'br', 'document_number': None, 'document_type': 'cpf',
                        'name': '                ',
                         'email': '<EMAIL>', 'phone_numbers': ['+5511999998888'],
                          'born_at': None, 'birthday': None, 'gender': None, 'date_created': '2021-03-05T19:26:40.872Z',
                           'documents': [{'object': 'document', 'id': 'doc_cklwovbp40au20h9tuuz7ryqi',
                            'type': 'cpf', 'number': '111.111.111-11'}]},
                             'billing': {'object': 'billing', 'id': 2032287,
                              'name': '4916801061375255', 'address': {'object': 'address', 'street': 'rua',
                               'complementary': None, 'street_number': 'numero',
                                'neighborhood': None, 'city': 'poa',
                                 'state': 'rs', 'zipcode': '12345678',
                                  'country': 'br', 'id': 4169999}}, 'shipping': None, 'items': [{'object': 'item', 'id': 'r123', 'title': 'Assinatura Teste', 'unit_price': 10000, 'quantity': 1, 'category': None, 'tangible': False, 'venue': None, 'date': None}], 'card': {'object': 'card', 'id': 'card_cklwovbq50au30h9tqfmh2liq', 'date_created': '2021-03-05T19:26:40.926Z', 'date_updated': '2021-03-05T19:26:41.222Z', 'brand': 'visa', 'holder_name': '                ', 'first_digits': '491680', 'last_digits': '5255', 'country': 'CHINA, PEOPLES REP. OF', 'fingerprint': 'cklwovb0g2h6b0l71jmupaa7m', 'valid': True, 'expiration_date': '0822'}, 'split_rules': None, 'metadata': {}, 'antifraud_metadata': {}, 'reference_key': None, 'device': None, 'local_transaction_id': None, 'local_time': None, 'fraud_covered': False, 'fraud_reimbursed': None, 'order_id': None, 'risk_level': 'unknown', 'receipt_url': None, 'payment': None, 'addition': None, 'discount': None, 'private_label': None, 'pix_qr_code': None, 'pix_expiration_date': None}

        :return:
        """
        resultado = self.transacao_resultado
        print(resultado)
        #        print(resultado.tid)
        try:
            if 'acquirer_id' in resultado.keys():
                print(resultado['acquirer_id'])
                query = CieloTransaction.objects.create(tid=resultado['acquirer_id'],
                                                        valor=resultado['amount'], descricao=''
                                                        , autorizacao_lr=resultado['authorization_code'])

            elif 'plan' in resultado.keys():
                query = CieloTransaction.objects.create(
                    recurrentpayment_id=resultado['manage_token'],
                    valor=resultado['plan']['amount'], descricao=resultado['plan']['name']
                )
            try:
                query.soft_descriptor = resultado['soft_descriptor']
            except KeyError:
                print('Sem softdescriptor')
            try:
                query.interval = resultado['Payment']['RecurrentPayment']['Interval']
            except KeyError:
                print('interval not found')
            try:
                query.captura_codigo = resultado['Payment']['ReturnCode']
            except KeyError:
                print('sem captura codigo')
            # try:
            #     query.payment_id = resultado['Payment']['PaymentId']
            # except KeyError:
            #     print('sem paymentid')
            # try:
            #     query.autorizacao_codigo = '3.0'
            # except KeyError:
            #     print('sem autorizacao codigo')
            try:
                query.data_hora = resultado['date_created']
            except KeyError:
                print('sem data hora capture')
            try:
                query.pedido_numero = resultado[self.pedido]
            except KeyError:
                print('sem numero pedido')
            # try:
            #     query.moeda = resultado['Payment']['Currency']
            # except KeyError:
            #     print('sem currency')
            try:
                query.bandeira = resultado['card_brand']
            except KeyError:
                print('sem bandeira')
            try:
                query.produto = resultado['Payment']['Type']
            except KeyError:
                print('sem resultado payment')
            if self.RecurrentPayment:
                try:
                    query.produto = resultado['plan']['id']
                except KeyError:
                    print('assinatura stone sem produto')
            try:
                query.parcelas = resultado['installments']
            except KeyError:
                print('sem installments')
            try:
                query.arp = resultado['authorization_code']
            except KeyError:
                print('sem payment authorizationcode')
            try:
                query.nsu = resultado['Payment']['ProofOfSale']
            except KeyError:
                print('sem nsu')

            #print(query.id)
            query.save()
            return query
        except AttributeError as e:
            print('erro no transaction save do stone,', e)
            query = CieloTransaction.objects.create(tentative=resultado)
            query.save()
            return query
            # return super().save_transaction()


        return super().save_transaction()

    def final_status(self):
        result = {}
        result['api'] = 'stone'
        if isinstance(self.transacao_resultado, Exception):
            return {'status': 'error', 'api': 'stone',
                    'data': self.transacao_resultado,'type':'exception'}

        if self.transacao_resultado['status'] == 'paid' or self.transacao_resultado['status'] == 'trialing':
            self.transacao_resultado['status'] = 'OK'
            self.transacao_resultado['Payment'] = {'Status':1}
            return {'status': 'OK', 'api': 'stone',
                    'data': self.transacao_resultado}
        else:
            return {'status': 'error', 'api': 'stone',
                    'data': self.transacao_resultado['refuse_reason']}

    def set_transacao(self):
        pass

    def gera_boleto(self, montante, nome, provedor='BancodoBrasil', endereco=None, plano=None, cpf=None):
        super().gera_boleto(montante, nome, provedor, endereco, plano, cpf)

    def final_status_boleto(self):
        return super().final_status_boleto()

    def save_transaction_boleto(self):
        return super().save_transaction_boleto()
