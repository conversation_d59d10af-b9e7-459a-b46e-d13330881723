import json
import ssl
from datetime import datetime, date, timedelta
from uuid import uuid4

import requests
from cielo_webservice.models import Comercial, Cartao, Pedido, Pagamento, Transacao
from cielo_webservice.request import CieloRequest
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.poolmanager import PoolManager

from associacao.models import CieloData, CieloTransaction, Boleto
from django.urls import reverse

class MyAdapter(HTTPAdapter):
    def init_poolmanager(self, connections, maxsize, block=False):
        self.poolmanager = PoolManager(num_pools=connections,
                                       maxsize=maxsize,
                                       block=block,
                                       ssl_version=ssl.PROTOCOL_SSLv2)


class CieloProcess(object):
    """
    main class for cielo related things
    """

    def __init__(self):
        self.cartao = None
        self.pedido = None
        self.pagamento = None
        self.transacao = None
        self.transacao_resultado = None
        self.softdescriptor = ''

        try:
            q = CieloData.objects.filter(ativo=True).last()
            self.comercial = Comercial(numero=int(q.numero), chave=q.chave)
            self.inicializacao = True
            self.sandbox = q.sandbox
        except Exception as e:
            print('erro no init cielo data')
            print(e)
            self.inicializacao = False

        super().__init__()

    def set_payment_data(self, numero_cartao, validade, indicador, codigo_seguranca, nome_portador):
        """
        ajusta paymento data
        :param numero_cartao: integer
        :param validade: integer
        :param codigo_seguranca: string
        :param nome_portador: string
        :param indicador:integer
        :return:none
        """
        self.cartao = Cartao(numero=numero_cartao, validade=validade, indicador=indicador,
                             codigo_seguranca=codigo_seguranca, nome_portador=nome_portador)

    def set_pedido(self, numero, valor, moeda=986, data_hora=datetime.now().isoformat()):
        """
        define pedido
        :param numero:string
        :param valor: integer
        :param moeda: integer
        :param data_hora: string
        :return: none
        """
        self.pedido = Pedido(numero=str(numero.num_pedido)[:8], valor=valor, moeda=moeda, data_hora=data_hora)

    def set_pagamento(self, bandeira, produto, parcelas=1):
        """
        define pagamento data
        produto: 1- credito a vista, 2- parcelado, A-debito
        :param bandeira: string
        :param produto: string
        :param parcelas: integer
        :return: none
        """
        self.pagamento = Pagamento(bandeira=bandeira, produto=produto, parcelas=parcelas)

    def set_transacao(self):
        """
        will prepare the transaction
        :return:
        """
        self.transacao = Transacao(comercial=self.comercial, cartao=self.cartao, pedido=self.pedido,
                                   pagamento=self.pagamento, autorizar=3, capturar=True)

    def save_transaction(self):
        """
        main function to save transaction on DB
        :return:
        tid = models.CharField(max_length=100)
        pan = models.CharField(max_length=100)
        valor = models.DecimalField(max_digits=10, decimal_places=2)
        descricao = models.CharField(max_length=130, null=True, blank=True)
        autorizacao_lr = models.CharField(max_length=2)
        soft_descriptor = models.CharField(max_length=130, null=True, blank=True)
        captura_codigo = models.CharField(max_length=3)
        autorizacao_codigo = models.CharField(max_length=3)
        data_hora = models.DateTimeField()
        pedido_numero = models.CharField(max_length=30)
        moeda = models.IntegerField()
        bandeira = models.CharField(max_length=20)
        produto = models.CharField(max_length=3)
        parcelas = models.IntegerField()
        arp = models.IntegerField()
        nsu = models.IntegerField()

        <Transacao(comercial=None, cartao=None, pedido=<Pedido(numero=x666, valor=6000, moeda=986, data_hora=2016-06-07T13:47:49.681-03:00,
         descricao=None, idioma=PT, taxa_embarque=0, soft_descriptor=None)>, pagamento=<Pagamento(bandeira=visa, produto=1,
          parcelas=1)>, url_retorno=None, autorizar=None, capturar=None, campo_livre=None, bin=None, gerar_token=None, avs=None,
           autenticacao=<Autenticacao(codigo=6, mensagem=Transacao sem autenticacao, data_hora=2016-06-07T13:47:49.689-03:00,
            valor=6000, eci=7)>, autorizacao=<Autorizacao(codigo=6, mensagem=Transacao autorizada, data_hora=2016-06-07T13:47:49.694-03:00,
             valor=6000, lr=0, arp=123456, nsu=877318)>, captura=<Captura(codigo=6, mensagem=Transacao capturada com sucesso,
              data_hora=2016-06-07T13:47:49.711-03:00, valor=6000, taxa_embarque=None)>, token=None, cancelamento=None,
               tid=100699306900068F086A, pan=IqVz7P9zaIgTYdU41HaW/OB/d7Idwttqwb2vaTt8MT0=, status=6, url_autenticacao=None)>

        """

        resultado = self.transacao_resultado
        print(resultado)
        #        print(resultado.tid)
        try:
            print(resultado.tid)
            query = CieloTransaction.objects.create(tid=resultado.tid, pan=self.transacao_resultado.pan,
                                                    valor=resultado.pedido.valor, descricao=resultado.pedido.descricao
                                                    , autorizacao_lr=resultado.autorizacao.lr,
                                                    soft_descriptor=resultado.pedido.soft_descriptor,
                                                    captura_codigo=resultado.captura.codigo,
                                                    autorizacao_codigo=resultado.autorizacao.codigo,
                                                    data_hora=resultado.pedido.data_hora,
                                                    pedido_numero=resultado.pedido.numero, moeda=resultado.pedido.moeda,
                                                    bandeira=resultado.pagamento.bandeira,
                                                    produto=resultado.pagamento.produto,
                                                    parcelas=resultado.pagamento.parcelas,
                                                    arp=resultado.autorizacao.arp, nsu=resultado.autorizacao.nsu)
            print(query.id)
            return query
        except AttributeError:
            query = CieloTransaction.objects.create(tentative=resultado)
            return query

    def executar_transacao(self, sandbox=True):
        """
        vai executar transacao
        :param sandbox: boolean
        :return: transacao data
        """
        request = CieloRequest(sandbox=self.sandbox)
        try:
            self.transacao_resultado = request.autorizar(transacao=self.transacao)
        except ConnectionError:
            print('Connection error')
            self.transacao_resultado = 'ConnectionError'
        return self.transacao_resultado

    def cancelar_transacao(self, tid):
        """
        vai cancelar/estornar transacao
        :param tid: string
        :return:
        """
        request = CieloRequest(sandbox=self.sandbox)
        try:
            self.transacao_resultado = request.cancelar(tid=tid, comercial=self.comercial)
        except ConnectionError:
            print('Connection error')
            self.transacao_resultado = 'ConnectionError'
        return self.transacao_resultado

    def final_status(self):
        """
        will return dict with status and data
        :return:
        """
        try:
            if self.transacao_resultado.autorizacao.mensagem == "Transacao autorizada":
                if self.transacao_resultado.captura.mensagem == "Transacao capturada com sucesso":
                    self.status = {'status': 'OK', 'data': self.transacao_resultado, 'type': 'cielo', 'api': '1.5'}
                    return self.status
                elif self.transacao_resultado.autorizacao.mensagem == "Transacao autorizada" and self.transacao_resultado.captura.mensagem != "Transacao capturada com sucesso":
                    self.status = {'status': 'Partial', 'data': self.transacao_resultado, 'type': 'cielo', 'api': '1.5'}
                    return self.status
            else:  # self.transacao_resultado.autorizacao.mensagem != "Transacao autorizada" and self.transacao_resultado.captura.mensagem != "Transacao capturada com sucesso":
                self.status = {'status': 'Error', 'data': self.transacao_resultado, 'type': 'exception', 'api': '1.5'}
                return self.status
        except AttributeError:
            self.status = {'status': 'Error', 'data': self.transacao_resultado, 'type': 'exception', 'api': '1.5'}
            return self.status


class CieloProcess30(CieloProcess):
    """
    will use the webservices api 3.0
    """

    def __init__(self, cielo_data):
        self.cartao = None
        self.pedido = None
        self.pagamento = None
        self.transacao = None
        self.transacao_resultado = None
        self.MerchantId = None
        self.MerchantKey = None
        self.requestid = str(uuid4())
        self.CreditCard = {}
        self.DebitCard = {}
        self.Payment = {}
        self.Customer = {}
        self.Pix = {}
        self.MerchantOrderId = None
        self.valor = None
        self.softdescriptor = ''
        self.tipo = None
        self.salvar_cartao = None
        self.session = requests.Session()
        self.RecurrentPayment = None
        self.RecurrentPayment_Trial = False
        self.RecurrentPayment_Interval = 'Monthly'
        self.RecurrentPayment_Trial_days = 30
        self.RecurrentPayment_StartDate = (date.today()+timedelta(days=30)).isoformat()

        # self.session.mount('https://', MyAdapter())
        try:
            #q = CieloData.objects.filter(ativo=True, tipo_api='3.0').last()
            self.MerchantId = cielo_data.numero
            self.MerchantKey = cielo_data.chave
            self.inicializacao = True
            self.sandbox = cielo_data.sandbox
            if self.sandbox == True:
                self.url_requisicao = 'https://apisandbox.cieloecommerce.cielo.com.br'
                self.url_consulta = 'https://apiquerysandbox.cieloecommerce.cielo.com.br'
            else:
                self.url_requisicao = 'https://api.cieloecommerce.cielo.com.br'
                self.url_consulta = 'https://apiquery.cieloecommerce.cielo.com.br'


        except Exception as e:
            print('erro no init cielo data 30')
            print(e)
            self.inicializacao = False

            # super().__init__()

    def set_payment_data(self, numero_cartao, validade, indicador, codigo_seguranca, nome_portador, tipo, bandeira, parcelas=1):
        if tipo == 0 :
            self.Payment['Type'] = 'CreditCard'
            self.CreditCard['Brand'] = bandeira
            self.CreditCard['Holder'] = nome_portador
            self.CreditCard['ExpirationDate'] = validade
            self.CreditCard['SecurityCode'] = codigo_seguranca
            self.CreditCard['CardNumber'] = numero_cartao
            self.Payment['Installments'] = parcelas
            if self.RecurrentPayment is True:
                self.Payment['Capture'] = 'true'
                if self.RecurrentPayment_Trial is True:
                    self.Payment['SaveCard'] = 'false'
            else:
                self.Payment['Capture'] = 'true'

        elif tipo ==1:
            self.Payment['Type'] = 'DebitCard'
            self.Payment['ReturnUrl'] = 'https://planopago.com.br' + reverse(viewname='retornocielo')
            self.Payment['Authenticate'] = True
            self.DebitCard['Brand'] = bandeira
            self.DebitCard['Holder'] = nome_portador
            self.DebitCard['ExpirationDate'] = validade
            self.DebitCard['SecurityCode'] = codigo_seguranca
            self.DebitCard['CardNumber'] = numero_cartao
            self.Payment['Installments'] = parcelas

        elif tipo == 2:  # PIX
            self.Payment['Type'] = 'Pix'
            # PIX não precisa de dados de cartão
            self.Customer['Name'] = nome_portador

        self.tipo = tipo
        if tipo != 2:  # Não definir nome para PIX aqui
            self.Customer['Name'] = nome_portador

    def set_pix_data(self, nome_pagador, cpf_pagador=None, email_pagador=None):
        """
        Configura dados específicos para PIX
        """
        self.Payment['Type'] = 'Pix'
        self.Customer['Name'] = nome_pagador
        if cpf_pagador:
            self.Customer['Identity'] = cpf_pagador
            self.Customer['IdentityType'] = 'CPF'
        if email_pagador:
            self.Customer['Email'] = email_pagador
        self.tipo = 2

    def set_pedido(self, pedido, valor, moeda=986, softdescriptor=None, parcelas=1, assinatura=False, dias_para_iniciar_assinatura=0):
        self.Payment['Amount'] = valor
        if parcelas>1:
            self.Payment['Installments'] = parcelas
        if softdescriptor:
            self.Payment['SoftDescriptor'] = softdescriptor
        self.MerchantOrderId = str(pedido.num_pedido)
        print('pedido num eh', self.MerchantId)
        if assinatura:
            self.salvar_cartao=True
        # super().set_pedido(numero, valor, moeda, data_hora)

    def set_pagamento(self, bandeira, produto, parcelas=1):
        self.Payment['Authenticate'] = False

        # super().set_pagamento(bandeira, produto, parcelas)

    def fazer_requisicao(self, jsonenviar, directoryurl=None, METHOD=None):
        """
        will do the request and save session
        :param jsonenviar:
        :param sandbox:
        :return: response
        """
        headers2 = {'MerchantId': self.MerchantId,
                    'MerchantKey': self.MerchantKey,
                    'RequestId': self.requestid,
                    'Content-Type': 'application/json'}
        if directoryurl:
            url = self.url_requisicao + directoryurl
        url = self.url_requisicao+'/1/sales/'
        if METHOD == 'GET':
            url = self.url_consulta + directoryurl
            req = requests.get(url=url, headers =headers2)
        else:
            req = self.session.request('POST', self.url_requisicao+'/1/sales/', headers=headers2, data=jsonenviar)
        print(req)
        print(req.content)
        self.session = req
        return req

    def consultar_venda(self, paymentid):
        """
        query paymentID status
        :param paymentid: uuid
        :return: {status,data}
        """
        data = self.fazer_requisicao({}, directoryurl='/1/sales/'+paymentid, METHOD='GET')
        print(data)
        return json.loads(data.text)

    def executar_transacao(self, sandbox=True):
        """
        will execute credicard purchase
        :param sandbox:
        :return:
        """
        prejson = {}
        prejson['MerchantOrderId'] = self.MerchantOrderId
        prejson['Customer'] = self.Customer
        prejson['Payment'] = self.Payment
        if self.tipo==0:
            prejson['Payment']['Creditcard'] = self.CreditCard
        elif self.tipo==1:
            prejson['Payment']['Debitcard'] = self.DebitCard
        elif self.tipo==2:
            # PIX não precisa de dados adicionais além do Payment['Type'] = 'Pix'
            pass
        if self.RecurrentPayment:
            print('pagamento eh recorrente')
            prejson['Payment']['RecurrentPayment'] = {
                "Interval": self.RecurrentPayment_Interval,
                "AuthorizeNow": 'true'}
            if self.RecurrentPayment_Trial:
                print('pagamento recorrente comeca em 30 dias')
                prejson['Payment'].pop('Capture')
                prejson['Payment']['RecurrentPayment']= {
                    "AuthorizeNow":'false',
                    "Interval": self.RecurrentPayment_Interval,
                    "StartDate":(date.today()+timedelta(days=self.RecurrentPayment_Trial_days)).isoformat()}
        jsondata = json.dumps(prejson)
        req = self.fazer_requisicao(jsondata)
        self.session = req
        try:
            self.transacao_resultado = json.loads(req.text)
        except Exception as e:
            print('erro no request,',e, req.text)
        return req

    def cancelar_transacao(self, tid, recurrentpayment=None):
        headers2 = {'MerchantId': self.MerchantId,
                    'MerchantKey': self.MerchantKey,
                    'RequestId': self.requestid,
                    'Content-Type': 'application/json'}
        if recurrentpayment:
            req = self.session.request('PUT', self.url_requisicao + '/1/RecurrentPayment/' + tid + '/Deactivate', headers=headers2)
            print(self.url_requisicao + '/1/RecurrentPayment/' + tid + '/Deactivate')
            if req.status_code == 200:
                resultjson = {'Status':10, 'RecurrentPaymentId':tid}
            else:
                resultjson = {'ReturnCode': -1,'data':req}
        else:
            req = self.session.request('PUT', self.url_requisicao + '/1/sales/'+tid+'/void', headers=headers2)
            print(self.url_requisicao + '/1/sales/'+tid+'/void')
            resultjson = json.loads(req.text)
        print(req)
        print(req.content)

        return resultjson

    def criar_token_cartao(self, name, cardnumber, holder, expirationDate, brand):
        """
        Name
        :return:
        """
        headers2 = {'MerchantId': self.MerchantId,
                    'MerchantKey': self.MerchantKey,
                    'RequestId': self.requestid,
                    'Content-Type': 'application/json'}
        data = {'CustomerName':name,'CardNumber':cardnumber,
                'Holder':holder,'ExpirationDate':expirationDate,
                'Brand':brand}
        req = self.session.request('POST', self.url_requisicao + '/1/card/', headers=headers2, data=json.dumps(data))
        print(self.url_requisicao + '/1/card/', data)
        print(req)
        print(req.content)
        resultjson = json.loads(req.text)
        try:
            token = resultjson.get('CardToken')
            status = 'OK'
            data = 'assinatura feita com sucesso'

        except AttributeError:
            status = 'Error ao pegar token'
            data = 'Erro ao criar assinatura verefique seus dados'

        return {'status': status, 'api': '3.0',
                'data':data,
                'rawdata': resultjson}




    def save_transaction(self):
        """
        {'Authenticate': False, 'CapturedAmount': 1500, 'Currency': 'BRL', 'Tid': '0926022142838',
        'Provider': 'Simulado', 'CreditCard': {'Brand': 'Visa', 'SaveCard': False,
        'Holder': 'card master', 'CardNumber': '000000******0001', 'ExpirationDate': '01/2018'},
        'AuthorizationCode': '518656', 'Capture': True,
        'ServiceTaxAmount': 0, 'CapturedDate': '2016-09-26 14:21:42',
        'Country': 'BRA', 'Links': [{'Rel': 'self', 'Method': 'GET',
        'Href': 'https://apiquerysandbox.cieloecommerce.cielo.com.br/1/sales/d0bbc8ab-1164-422b-8cdc-65c16d1a6756'},
        {'Rel': 'void', 'Method': 'PUT', 'Href': 'https://apisandbox.cieloecommerce.cielo.com.br/1/sales/d0bbc8ab-1164-422b-8cdc-65c16d1a6756/void'}],
        'PaymentId': 'd0bbc8ab-1164-422b-8cdc-65c16d1a6756', 'ReceivedDate': '2016-09-26 14:21:42',
        'ReturnMessage': 'Operation Successful', 'Installments': 1, 'ProofOfSale': '20160926022142838',
        'Status': 2, 'Recurrent': False, 'Type': 'CreditCard', 'ReturnCode': '6',
        'Amount': 1500, 'Interest': 0}

        recurrent
       {"MerchantOrderId":"b65b2b08-80cb-487d-862c-fea554231068","Customer":{"Name":"[Guest]"},
       "Payment":{"ServiceTaxAmount":0,"Installments":1,"Interest":0,"Capture":true,
       "Authenticate":false,"Recurrent":false,"CreditCard":{"CardNumber":"545454******5454","Holder":""
       ,"ExpirationDate":"01/2021","SaveCard":false,"Brand":"Visa"},"SoftDescriptor":"Assinatura",
       "Provider":"Simulado","IsQrCode":false,"Amount":10000,
       "RecurrentPayment":{"RecurrentPaymentId":"f6cfc439-6368-4ad0-831d-432039d79255",
       "ReasonCode":0,"ReasonMessage":"Successful","NextRecurrency":"2020-04-11",
       "StartDate":"2020-04-11",
       "Interval":1,"Link":{"Method":"GET","Rel":"recurrentPayment",
       "Href":"https://apiquerysandbox.cieloecommerce.cielo.com.br/1/RecurrentPayment/f6cfc439-6368-4ad0-831d-432039d79255"},
       "AuthorizeNow":false},"Status":20,"IsSplitted":false,"Type":"CreditCard","Currency":"BRL","Country":"BRA"}}'


        :return:
        """
        resultado = self.transacao_resultado
        print(resultado)
        #        print(resultado.tid)
        try:
            if 'PaymentId' in resultado['Payment']:
                print(resultado['Payment']['PaymentId'])
                query = CieloTransaction.objects.create(tid=resultado['Payment']['Tid'],
                                                        valor=resultado['Payment']['Amount'], descricao=''
                                                        , autorizacao_lr=resultado['Payment']['ReturnCode'])
                if 'RecurrentPayment' in resultado['Payment']:
                    query.recurrentpayment_id = resultado['Payment']['RecurrentPayment']['RecurrentPaymentId']
            elif 'RecurrentPayment' in resultado['Payment']:
                query = CieloTransaction.objects.create(recurrentpayment_id=resultado['Payment']['RecurrentPayment']['RecurrentPaymentId'],
                                                        valor=resultado['Payment']['Amount'], descricao=''
                                                        , autorizacao_lr=resultado['Payment']['RecurrentPayment']['ReasonCode'])
            try:
                query.soft_descriptor = resultado['Payment']['SoftDescriptor']
            except KeyError:
                print('Sem softdescriptor')
            try:
                query.interval = resultado['Payment']['RecurrentPayment']['Interval']
            except KeyError:
                print('interval not found')
            try:
                query.captura_codigo = resultado['Payment']['ReturnCode']
            except KeyError:
                print('sem captura codigo')
            try:
                query.payment_id = resultado['Payment']['PaymentId']
            except KeyError:
                print('sem paymentid')
            try:
                query.autorizacao_codigo = '3.0'
            except KeyError:
                print('sem autorizacao codigo')
            try:
                query.data_hora = resultado['Payment']['CapturedDate']
            except KeyError:
                print('sem data hora capture')
            try:
                query.pedido_numero = resultado['MerchantOrderId']
            except KeyError:
                print('sem numero pedido')
            # try:
            #     query.moeda = resultado['Payment']['Currency']
            # except KeyError:
            #     print('sem currency')
            try:
                query.bandeira = resultado['Payment']['CreditCard']['Brand']
            except KeyError:
                print('sem bandeira')
            try:
                query.produto = resultado['Payment']['Type']
            except KeyError:
                print('sem resultado payment')
            try:
                query.parcelas = resultado['Payment']['Installments']
            except KeyError:
                print('sem installments')
            try:
                query.arp = resultado['Payment']['AuthorizationCode']
            except KeyError:
                print('sem payment authorizationcode')
            try:
                query.nsu = resultado['Payment']['ProofOfSale']
            except KeyError:
                print('sem nsu')

            print(query.id)
            query.save()
            return query
        except AttributeError:
            query = CieloTransaction.objects.create(tentative=resultado)
            query.save()
            return query
            # return super().save_transaction()

    def final_status(self):
        print(self.transacao_resultado)
        if isinstance(self.transacao_resultado, list):
            print('final status system error')
            return {'status': 'error', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado[0].get('Message')}
        if self.session.status_code == 500:
            print('cieloprocessor:erro de processamento na cielo com status 500', self.transacao_resultado)
            self.transacao_resultado=['Erro no processamento verifique os dados informados']
            return {'status': 'error', 'type': 'cielo', 'api': '3.0',
                    'data': ['Erro no processamento verifique os dados informados']}


        payment_status = self.transacao_resultado.get("Payment").get('Status')
        payment_type = self.transacao_resultado.get("Payment").get("Type")

        if payment_status == 2:
            self.valor = self.transacao_resultado['Payment']['Amount']
            print('final status 2')
            return {'status': 'OK', 'api': '3.0',
                    'data': self.transacao_resultado['Payment']['ReturnMessage'],
                    'rawdata':self.transacao_resultado}
        if payment_status == 3:
            print('final status 3')
            return {'status': 'error', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado['Payment']['ReturnMessage'],
                    'rawdata': self.transacao_resultado}
        if payment_status == 0 and payment_type=='CreditCard':
            print('final status 0')
            return {'status': 'error', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado['Payment']['ReturnMessage'],
                    'rawdata': self.transacao_resultado}
        if payment_status == 20 and payment_type == 'CreditCard':
            print('final status 20, recorrencia programada com trial')
            return {'status': 'OK', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado['Payment']['RecurrentPayment']['ReasonCode'],
                    'rawdata': self.transacao_resultado}
        if payment_status == 1 and payment_type == 'CreditCard':
            print('final status 1, recorrencia programada imediata')
            return {'status': 'OK', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado['Payment']['RecurrentPayment']['ReasonCode'],
                    'rawdata': self.transacao_resultado}
        if payment_status == 0 and payment_type=='DebitCard':
            print('final status 0, debitcard')
            return {'status': 'pending', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado['Payment']['AuthenticationUrl'],
                    'DebitCard': True,
                    'rawdata': self.transacao_resultado}

        # PIX Status
        if payment_type == 'Pix':
            if payment_status == 12:  # PIX gerado com sucesso
                print('final status 12, PIX gerado')
                return {'status': 'OK', 'type': 'cielo', 'api': '3.0',
                        'data': 'PIX gerado com sucesso',
                        'pix_qr_code': self.transacao_resultado['Payment'].get('QrCodeString', ''),
                        'pix_code': self.transacao_resultado['Payment'].get('QrCodeBase64Image', ''),
                        'rawdata': self.transacao_resultado}
            elif payment_status == 2:  # PIX pago
                print('final status 2, PIX pago')
                return {'status': 'OK', 'type': 'cielo', 'api': '3.0',
                        'data': 'PIX pago com sucesso',
                        'rawdata': self.transacao_resultado}


    def set_transacao(self):
        pass

    def gera_boleto(self, montante, nome, provedor='BancodoBrasil', endereco=None, plano=None, cpf=None):
        """
        make boleto
        :param montante: recebe decimal, cria inteiro com 2 casas
        :param plano: plano-cobranca
        :param cpf: cpf comprador
        :param nome: nome comprador
        :param provedor: bb
        :param endereco: endereco loja
        :return: {status}
        """
        prejson = {}
        prejson['MerchantOrderId'] = str(uuid4())
        prejson['Customer'] = self.Customer
        prejson['Customer']['Name'] = nome
        prejson['Payment'] = self.Payment
        prejson['Payment']['Type'] = 'Boleto'
        prejson['Payment']['Amount'] = montante
        prejson['Payment']['Provider'] = provedor
        jsondata = json.dumps(prejson)
        print(jsondata)
        req = self.fazer_requisicao(jsondata)
        print(req.content)
        self.transacao_resultado = json.loads(req.text)

    def final_status_boleto(self):
        """
        vai criar e gerar o status do boleto recem gerado
        :return: {status}
        """
        print(self.transacao_resultado)
        if isinstance(self.transacao_resultado, list):
            print('final status system error')
            return {'status': 'error', 'type': 'cielo', 'api': '3.0',
                    'data': self.transacao_resultado[0].get('Message')}
        payment_status = self.transacao_resultado.get("Payment").get('Status')

        if payment_status == 1:
            self.valor = self.transacao_resultado['Payment']['Amount']
            print('final status 1')
            return {'status': 'OK', 'api': '3.0',
                    'data': self.transacao_resultado}
        if payment_status != 1:
            self.valor = self.transacao_resultado['Payment']['Amount']
            print('final status 1')
            return {'status': 'error', 'api': '3.0',
                    'data': self.transacao_resultado}

    def save_transaction_boleto(self):
        """
        will save boleto transaction
        :return:
        b'{"MerchantOrderId":"b17f9b4e-c0c0-43f9-a8e8-a5559a8eddab","Customer":{"Name":"Carlos teste"},
        "Payment":{"ExpirationDate":"2016-10-09","Url":"https://sandbox.pagador.com.br/post/pagador/reenvia.asp/0b4a6aeb-4d72-497d-902a-bd255471ad14",
        "BoletoNumber":"3-1","BarCodeNumber":"00091694200000002009999250000000000399999990",
        "DigitableLine":"00099.99921 50000.000005 03999.999901 1 69420000000200","Address":"N/A,
         1","PaymentId":"0b4a6aeb-4d72-497d-902a-bd255471ad14","Type":"Boleto","Amount":200,
         "ReceivedDate":"2016-10-06 18:31:57","Currency":"BRL","Country":"BRA","Provider":"Simulado",
         "Status":1,"Links":[{"Method":"GET","Rel":"self",
         "Href":"https://apiquerysandbox.cieloecommerce.cielo.com.br/1/sales/0b4a6aeb-4d72-497d-902a-bd255471ad14"}]}}'
            expiration_date = models.DateField()
        url_main = models.URLField(null=True,blank=True)
        number = models.CharField(max_length=80)
        barCodeNumber = models.CharField(max_length=120, null=True, blank=True)
        DigitableLine = models.CharField(max_length=150, null=True, blank=True)
        Address = models.CharField(max_length=200, null=True, blank=True)
        PaymentId = models.CharField(max_length=150, null=True, blank=True)
        Amount = models.DecimalField(max_length=15, decimal_places=2,max_digits=15)
        Type = models.CharField(max_length=80, default='Boleto')
        Country = models.CharField(max_length=120, default='BRA')
        ExtraDataCollection = models.CharField(max_length=120)
        Status = models.IntegerField(null=True,blank=True)
        querylink_url = models.URLField(null=True,blank=True)
        demonstrative = models.CharField(max_length=450, null=True,blank=True)
        instructions = models.CharField(max_length=450, null=True,blank=True)
        Provider = models.CharField(max_length=120,choices=[('Bradesco','Bradesco'),
                                                            ('BB','BB')], default='BB')
        ppago_status = models.CharField(max_length=120, choices=[('PendenteCriacao','PendenteCriacao'),
                                                                 ('PendentePagamento','PendentePagamento'),
                                                                 ('Pago','Pago'),('Vencido','Vencido')],
                                        default='PendenteCriacao')
        pessoa = models.ForeignKey(Pessoa, null=True, blank=True)
        negocio = models.ForeignKey(Negocio, null=True, blank=True)
        cobranca = models.ForeignKey(PlanosdePagamento, blank=True, null=True)
        """
        #        print(resultado.tid)
        print('final result transac_resul', self.transacao_resultado)
        if self.transacao_resultado['Payment']['Status'] != 1:
            query = Boleto.objects.create(tentative=self.transacao_resultado)
            print('erro na criacao do boleto', self.transacao_resultado)
            return query
        query = Boleto.objects.create(Status=int(self.transacao_resultado['Payment']['Status']))#, Amount=int(self.transacao_resultado['Payment']['Amount']))
        try:
            query.name = self.transacao_resultado['Customer']['Name']
        except KeyError:
            print('sem Nome')
        try:
            query.demonstrative = self.transacao_resultado['Payment']['Demonstrative']
        except KeyError:
            print('sem Demonstrative')
        # try:
        #     query.expiration_date = self.transacao_resultado['Payment']['ExpirationDate']
        # except KeyError:
        #     print('sem ExpirationDate')
        try:
            query.instructions = str(self.transacao_resultado['Payment']['Instructions'])
        except KeyError:
            print('sem Instructions')
        try:
            print('fazendo paymentid',self.transacao_resultado['Payment']['PaymentId'])
            query.PaymentId = self.transacao_resultado['Payment']['PaymentId']
        except KeyError:
            print('sem PaymentId')
        try:
            query.DigitableLine = self.transacao_resultado['Payment']['DigitableLine']
        except KeyError:
            print('sem DigitableLine')
        try:
            query.barCodeNumber = str(self.transacao_resultado['Payment']['BarCodeNumber'])
        except KeyError:
            print('sem BarCodeNumber')
        try:
            query.Provider = self.transacao_resultado['Payment']['Provider']
        except KeyError:
            print('sem Provider')
        try:
            query.querylink_url = self.transacao_resultado['Payment']['Links'][0]['Href']
        except KeyError:
            print('sem Links')
        try:
            query.url_main = self.transacao_resultado['Payment']['Url']
        except KeyError:
            print('sem urlmain')
            query.objects.update()
        try:
            query.Amount = self.transacao_resultado['Payment']['Amount']/100
        except KeyError:
            print('sem amount')
        try:
            query.expiration_date = self.transacao_resultado['Payment']['ExpirationDate']
        except KeyError:
            print('sem ExpirationDate')
        try:
            query.html_source = requests.get(query.url_main).content
        except KeyError:
            print('sem querylink pro source')
        query.save()
        return query


class CieloProcess30Homologacao(CieloProcess30):
    """
    only to make homologacao with other keys
    """
    def __init__(self):
        self.cartao = None
        self.pedido = None
        self.pagamento = None
        self.transacao = None
        self.transacao_resultado = None
        self.MerchantId = None
        self.MerchantKey = None
        self.requestid = str(uuid4())
        self.CreditCard = {}
        self.Payment = {}
        self.Customer = {}
        self.MerchantOrderId = None
        self.valor = None
        self.softdescriptor = ''
        self.session = requests.Session()

        # self.session.mount('https://', MyAdapter())
        try:
            self.MerchantId = '0342cd9e-2a45-46b2-8938-52733ef29c86'
            print('carregando merchant id homologacao', self.MerchantId)
            self.MerchantKey = 'JfAvwOI4POCB8Mmb5XvfpzZuWzIMJHqtwWqQcA9T'
            self.inicializacao = True
            self.sandbox = False
            if self.sandbox == True:
                self.url_requisicao = 'https://apisandbox.cieloecommerce.cielo.com.br'
                self.url_consulta = 'https://apiquerysandbox.cieloecommerce.cielo.com.br'
            else:
                self.url_requisicao = 'https://api.cieloecommerce.cielo.com.br'
                self.url_consulta = 'https://apiquery.cieloecommerce.cielo.com.br'


        except Exception as e:
            print('erro no init cielo data 30')
            print(e)
            self.inicializacao = False

            # super().__init__()