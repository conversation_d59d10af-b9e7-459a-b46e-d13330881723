import json
from allauth.account.signals import user_signed_up
from django.core.exceptions import FieldError, MultipleObjectsReturned
from django.core.serializers.json import DjangoJSONEncoder
from django.core.urlresolvers import reverse_lazy, reverse
from django.db.models import Sum
from django.dispatch import receiver
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render_to_response, render, redirect
from django.utils.translation import LANGUAGE_SESSION_KEY
from django.views import generic
from django.views.decorators.cache import cache_control
from cielo_webservice.exceptions import CieloRequestError
from associacao.forms import CartaoForm, PlanoItemDetalhesForm, DetalhePagamentoGatewayForm, ConciliaGatewayForm, CPFForm
from associacao.models import Pessoa, PlanosdePagamento, Transacoes, Estatisticas, Negocio, EvecomData,\
    Conta, CieloData, NegocioRecenteAcoes, Pedido, CieloTransaction, <PERSON><PERSON><PERSON><PERSON>, User, Val<PERSON><PERSON>,\
    ListofValidatedPeople, PricePlan, Endereco
from .processes import OrderProcess
from django.utils.decorators import method_decorator
from datetime import date
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import ObjectDoesNotExist
from django.shortcuts import redirect
from datetime import timedelta, datetime
from django.http import Http404
import logging
import xlwt
from decimal import Decimal
from django import forms
from associacao.integration.evecom_integration import EvecomProcess
from associacao.Conciliator import FinanceConciliation
from mailapp.mailprocessor import EmailProcessor
from mailapp.models import MailTemplate
from django.utils import translation
import pagarme
import requests



# Get an instance of a logger
logger = logging.getLogger('django')


class Index(generic.TemplateView):
    """
    main view for index
    #TODO Usuario tem que ser de algum convenio para ver o menu financeiro
    Se ele eh so visualizador nao pode ser admin no negocio. tem que ser so visualizador
    """

    def get_template_names(self):
        try:
            if not self.request.user.is_authenticated():
                return "landing/index.html"
            elif len(self.request.user.pessoa.negocio_set.all()) > 0 or self.request.user.pessoa.admin_convenio:
                print('eh admin')
                logger.error('eh admin error')
                logger.info('eh admin info')
                return "associacao/paineldemo.html"
            elif len(self.request.user.pessoa.visualizadores.all()) > 0 or self.request.user.pessoa.visualiza_pagamentos:
                print('eh visualizador')
                logger.error('eh admin error')
                logger.info('eh admin info')
                return "associacao/painelvisualizapagamento.html"
            elif self.request.user.is_authenticated():
                print('eh associado 2')
                logger.error('eh associado error')
                logger.info('eh associado info')

                return "associado/painelassociado.html"
            else:
                return "landing/index.html"
        except Exception as e:
            logger.error('rolouc excpetiuon')
            #
            # except AttributeError:
            #     return "admin2/associado/painelassociado.html"

    def get_context_data(self, **kwargs):
        """
        neg = Negocio.objects.all().filter(admin=self.request.user.pessoa)
        :param kwargs:
        :return:
        """
        context = super().get_context_data(**kwargs)
        if not self.request.user.is_authenticated():
            return super().get_context_data()

        if self.request.user.pessoa.admin_convenio:
            try:
                convenio = CieloData.objects.get(admin=self.request.user.pessoa)
            except AttributeError:
                print('pessoa eh admin de convenio mas nao tem nenhum ligado a ela')
                return context
            transacoes = Transacoes.objects.filter(cielo_data=convenio, status='Pago')
            context['Associados_receita'] = transacoes.aggregate(Sum('montante'))['montante__sum']
            context['Disponivel_Resgate'] = Transacoes.objects.filter(cielo_data=convenio,
                                                                      status='Pago',
                                                                      data_hora__gte=datetime.now() - timedelta(
                                                                          days=30)).aggregate((Sum('montante')))
            context['Associados_num'] = transacoes.count()
            return context

        if self.request.user.pessoa.admin_negocio:

            try:
                neg = Negocio.objects.all().filter(admin=self.request.user.pessoa)
                context['Associados_receita'] = Estatisticas.objects.all().get(negocio=neg,mes=date.today().month, tipo=1).total
                context['Disponivel_Resgate'] = Transacoes.objects.filter(destino=neg,
                                                                          status='Pago', data_hora__gte=datetime.now() - timedelta(days=30)).aggregate((Sum('montante')))
                q = Estatisticas.objects.all().filter(negocio=neg, ano=date.today().year)
                context['Associados_receita_anual'] = q.aggregate(Sum('total'))
                context['Associados_num'] = Transacoes.objects.filter(destino=neg, status='Pago').count()


                # context['Associados_receita'] = Transacoes.objects.filter(conta=self.request.user.pessoa.conta).aggregate(Sum('montante'))
            except IndexError:
                context['Associados_receita'] = 0
            except ObjectDoesNotExist:
                context['Associados_receita'] = 0
            context['page_description'] = 'Inicial'
            try:
                context['acoes_recentes'] = NegocioRecenteAcoes.objects.all().filter(negocio=neg).reverse()[:5]
            except Exception as e:
                print('erro acoes recentes',e )
            return context

        if self.request.user.pessoa.visualiza_pagamentos:

            try:
                neg = self.request.user.pessoa.visualizadores.get()
                print(neg)
                context['Associados_receita'] = Transacoes.objects.filter(destino=neg,
                                                                          status='Pago', data_hora__gte=datetime.now() - timedelta(days=30)).exclude(cielo_data__nome='mfm').aggregate((Sum('montante')))
                context['Disponivel_Resgate'] = Transacoes.objects.filter(destino=neg,
                                                                          status='Pago', data_hora__lte=datetime.now() - timedelta(days=30)).exclude(cielo_data__nome='mfm').aggregate((Sum('montante')))
                context['Associados_receita_anual'] = Estatisticas.objects.all().filter(negocio=neg, ano=date.today().year, tipo=1).aggregate(Sum('total'))
                context['Associados_num'] = Transacoes.objects.filter(destino=neg, status='Pago').exclude(cielo_data__nome='mfm').count()

                print('context do painel eh', context)
            except IndexError:
                print('index error no painel')
                context['Associados_receita'] = 0
            except ObjectDoesNotExist as e:
                print('Object does not exist error no painel', e)
                context['Associados_receita'] = 0
            context['page_description'] = 'Inicial'
            try:
                context['acoes_recentes'] = NegocioRecenteAcoes.objects.all().filter(negocio=neg).reverse()[:5]
            except Exception as e:
                print('erro acoes recentes',e )
            return context
        return super().get_context_data()


class IndexAssociado(generic.TemplateView):
    """
    main view for associado index
    """
    template_name = "../templates/associado/painelassociado.html"


class Associados(generic.ListView):
    """
    main class view for Associados
    """
    template_name = "../templates/associacao/associados.html"
    context_object_name = "result"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_description'] = 'Associados'
        return context

    def get_queryset(self):
        queryset = Pessoa.objects.all().filter(planosdepagamento__entidade=self.request.user.pessoa.negocio_set.all())
        return queryset


class ExtratoAssociacao(generic.ListView):
    """
    main class to report on member statement
    """
    template_name = "../templates/associacao/extratofinanceiroassociacao.html"
    context_object_name = 'result'

    # model = Transacoes
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_description'] = 'Extrato Financeiro'
        return context

    def get_queryset(self):
        if self.request.user.pessoa.visualiza_pagamentos:
            print('eh visualizador')
            self.queryset = Transacoes.objects.all().filter(destino__in=self.request.user.pessoa.visualizadores.all()).exclude(cielo_data__nome='mfm')
        else:
            self.queryset = Transacoes.objects.all().filter(destino__in=self.request.user.pessoa.negocio_set.all())
        return self.queryset


class ExtratoAssociacaoExportaCsv(ExtratoAssociacao):
    """
    Export transacoes negocio to CSV
    """
    # DONE fazer teste CSV
    def get_queryset(self):
        return super().get_queryset()

    def render_to_response(self, context, **response_kwargs):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="somefilename.csv"'
        fields = ['usuario', 'montante', 'data_hora_completa', 'status', '.cielo_arp',
               'usuario_CPF', 'cielo_nsu','cielo_tid', 'nome_plano', 'data', 'evecom']
        print(self.queryset, len(self.queryset))
        line_header = (',').join(fields)
        line_header += '\n'
        response.write(line_header)
        for q in self.queryset:
            line = "{0},{1},{2},{3},{4},{5},{6},{7},\"{8}\",{9},".format(q.usuario,
                                                                         q.montante, q.data_hora,
                                                                         q.status, q.cielo_transactions.arp,
                                                                         q.usuario.CPF, q.cielo_transactions.nsu,
                                                                         q.cielo_transactions.tid,
                                                                         q.plano_pagamento.nome_plano,
                                                                         q.data_hora.strftime('%x'))
            try:
                line += q.pedido_set.all()[0].evecom_item.invoice
            except:
                print('nao tem invoice evecom nesse pdido csv')

            response.write(str(line) +'\n')
            print(line)
        return response
        # return super().render_to_response(context, **response_kwargs)


class ExtratoAssociacaoExportaXLS(ExtratoAssociacaoExportaCsv):
    """
    main class to export to xls
    """
    # DONE fazer teste XLS

    def render_to_response(self, context, **response_kwargs):
        response = HttpResponse(content_type='application/ms-excel')
        response['Content-Disposition'] = 'attachment; filename="{0}.xls"'.format(self.request.user.email)
        fields = ['usuario', 'montante', 'data_hora_completa', 'status', '.cielo_arp',
               'usuario_CPF', 'cielo_nsu','cielo_tid', 'nome_plano', 'data', 'evecom']
        print(self.queryset, len(self.queryset))
        #xls start
        # Sheet header, first row
        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet('Users')

        row_num = 0


        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet('Users')

        font_style = xlwt.XFStyle()
        font_style.font.bold = True
        for col_num in range(len(fields)):
            ws.write(row_num, col_num, fields[col_num], font_style)

        font_style = xlwt.XFStyle()



        for q in self.queryset:
            row_num+=1
            data = [q.usuario,q.montante, q.data_hora,q.status, q.cielo_transactions.arp,
                    q.usuario.CPF, q.cielo_transactions.nsu,q.cielo_transactions.tid,
                    q.plano_pagamento.nome_plano,q.data_hora.strftime('%x')]
            try:
                data.append(q.pedido_set.all()[0].evecom_item.invoice)
            except:
                print('nao tem invoice evecom nesse pdido xls')
            for col_num in range(len(fields)):
                try:
                    ws.write(row_num, col_num, data[col_num], font_style)

                    # line = "{0},{1},{2},{3},{4},{5},{6},{7},\"{8}\",{9},".format(q.usuario,
                    #                                                              q.montante, q.data_hora,
                    #                                                              q.status, q.cielo_transactions.arp,
                    #                                                              q.usuario.CPF, q.cielo_transactions.nsu,
                    #                                                              q.cielo_transactions.tid,
                    #                                                              q.plano_pagamento.nome_plano,
                    #                                                              q.data_hora.strftime('%x'))
                    # try:
                    #     line += q.pedido_set.all()[0].evecom_item.invoice
                    # except:
                    #     print('nao tem invoice evecom nesse pdido csv')
                except:
                    print('erro no key', q, col_num)

        wb.save(response)
        return response


class ExtratoXLSTUDO(ExtratoAssociacaoExportaXLS):
    def get_queryset(self):
        if self.request.user.pessoa.email == '<EMAIL>':
            how_many_days = 150
            self.queryset = Transacoes.objects.filter(data_hora__gte=datetime.now()-timedelta(days=how_many_days))
            return self.queryset


class PlanosPagamentos(generic.ListView):
    """
    main class to show plano de pagamentos
    """
    template_name = "../templates/associacao/planospagamentos.html"
    context_object_name = "result"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_description'] = 'Planos de Pagamento'
        return context

    def get_queryset(self):
        q = self.request.user.pessoa.negocio_set.all()
        queryset = PlanosdePagamento.objects.all().filter(entidade=q)
        return queryset


class PlanosPagamentosIfsoLac(generic.ListView):
    """
    main class to show plano de pagamentos
    TODO please neh, aqui nao
    """
    template_name = "../templates/associacao/planospagamentopagarsomente.html"
    context_object_name = "result"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_description'] = 'Planos de Pagamento'
        return context

    def get_queryset(self):
        p = Pessoa.objects.get(id=1589)
        q = p.negocio_set.all()
        queryset = PlanosdePagamento.objects.all().filter(entidade=q)
        return queryset


class DetalhesDePagamento(generic.DetailView):
    """
    main class to show pagamento transaction details
    """
    model = Transacoes
    template_name = "../templates/associacao/detalhetransacao.html"
    context_object_name = 'result'

    def get_queryset(self):
        if self.request.user.is_anonymous():
            raise Http404
        q = Transacoes.objects.all().filter(id=self.kwargs['pk'])
        if self.request.user.pessoa == q[0].destino.admin:
            return q
        if self.request.user.pessoa in q[0].destino.visualizador_pagamentos.all():
            return q
        raise Http404

class DetalheTransacaoGatewayForm(generic.FormView):
    """
    main form to search for details at paymenten gateway data
    """
    form_class = DetalhePagamentoGatewayForm
    success_url = reverse_lazy("detalhepagamentogateway")
    template_name = "associacao/busca_detalhe_form.html"


    def get_success_url(self):
        #kwargs = {'tid': self.kwargs['tid']}
        print(self.kwargs,self.get_form_kwargs())
        return reverse_lazy("detalhepagamentogateway", kwargs={'tid': self.get_form_kwargs()['data']['tid']})


class ConciliaGatewayExtrato(generic.TemplateView):
    """
    show conciliated gateway
    """
    template_name = "associacao/detalhetransacaogateway.html"


class MostraConcilia(generic.DetailView):
    """
    mostra job conciliado
    """
    model = Concilicao
    context_object_name = 'result'
    template_name = 'associacao/detalheconcilia.html'


class SubmeteArquivoConcilia( generic.FormView):
    """
    main class to submit concilia
    """
    form_class = ConciliaGatewayForm
    template_name = "associacao/busca_detalhe_form.html"

    def get(self, request, *args, **kwargs):
        if self.request.user.is_anonymous():
            raise Http404
        if self.request.user.pessoa.pode_conciliar:
            return super().get(request, *args, **kwargs)
        else:
            raise Http404

    def form_valid(self, form):
        print('form eh valido concilia')
        file_input = form.cleaned_data.get('arquivo')
        if form.cleaned_data.get('BuscaTID') == True:
            tipo = 2
        else:
            tipo = 1
        self.conciliacao = Concilicao.objects.create(arquivo_input=file_input, tipo_concilicao=tipo)
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('mostraconcilia', kwargs={'pk': str(self.conciliacao.id)})


class DetalheTransacaoGateway(generic.DetailView):
    """
    Main class to show payment detais at payment gateway data
    """
    model = CieloTransaction
    pk_url_kwarg = 'tid'
    template_name = "../templates/associacao/detalhetransacaogateway.html"
    context_object_name = 'result'

    def render_to_response(self, context, **response_kwargs):
        context['field_names'] = Transacoes._meta.get_fields()
        return super().render_to_response(context, **response_kwargs)

    def get_object(self, queryset=None):
        print(self.pk_url_kwarg)
        return (CieloTransaction.objects.all().filter(tid=self.kwargs['tid'])[0])


class CancelaPagamento(generic.RedirectView):
    """
    will cancel/estorno pagamento
    TODO: Restringir
    """
    # TODO enviar email para cliente sobre cancelamento
    # TODO testar erro cancelamento
    # TODO notificar na console do erro cancelamento
    # url = reverse_lazy("detalhepagamento", kwargs=self.kwargs)

    def get_redirect_url(self, *args, **kwargs):
        self.url = reverse_lazy("detalhepagamento", kwargs=self.kwargs)
        return self.url

    def get(self, request, *args, **kwargs):
        q = get_object_or_404(Transacoes.objects.filter(id=self.kwargs['pk']))
        print('cancelando queryset', q)
        c = OrderProcess(q.cielo_data)
        try:
            resultado = c.order_cancel(q)
            print(resultado)
            if resultado['status'] == 'OK':
                q.status_code = 2
                q.status = 'Cancelada'
                q.save()
                print('sucesso cancelamento xxx', resultado)
                if q.destino.template_email_cancelamento:
                    m1 = q.destino.template_email_cancelamento
                else:
                    m1 = MailTemplate.objects.filter(
                        situacao="cancelamento").first()
                e1 = EmailProcessor()
                e1.set_template(m1)
                mailtemplatedict = {
                    'total': str(q.plano_pagamento.montante),
                    'nome_atividade': q.plano_pagamento.nome_plano,
                    'evento_razaosocial': str(q.plano_pagamento.entidade.name),
                    'evento_cnpj': str(q.plano_pagamento.entidade.CNPJ),
                    'cpf': q.usuario.CPF
                }
                if not q.plano_pagamento.entidade.mostra_cnpj:
                    mailtemplatedict['evento_cnpj'] = ''
                e1.create_body(mailtemplatedict)
                e1.create_email_object(q.usuario.email, m1)

            else:
                print('falha cancelamento', resultado)
        except CieloRequestError as e:
            print('Erro no cancelamento', e, q.cielo_transactions.tid)
        return super().get(request, *args, **kwargs)


class ClientePlanosPagamentos(generic.ListView):
    """
    main class to show plano de pagamentos associado
    """
    template_name = "../templates/associado/planospagamentos.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tmp = Transacoes.objects.filter(usuario=self.request.user.pessoa).filter(status='Enviada').values(
            'plano_pagamento_id').distinct()
        pagos = []
        for i in tmp:
            pagos.append(i['plano_pagamento_id'])
        context['q'] = pagos
        return context

    def get_queryset(self):
        try:
            queryset = PlanosdePagamento.objects.filter(membros=self.request.user.pessoa)
            return queryset
        except FieldError:
            return super().get_queryset()

    context_object_name = "result"


class EditaAssociado(generic.UpdateView):
    """
    main class to edit associado
    """
    template_name = "associacao/pessoa_update.html"
    model = Pessoa

    def get_queryset(self):
        queryset = queryset = Pessoa.objects.all().filter(planosdepagamento__entidade=self.request.user.pessoa.negocio_set.all())
        return queryset

    fields = [ 'logradouro', 'numero', 'complemento',
              'email', 'CPF']

    success_url = "/associados"



class CriaPlanoPagamentos(generic.CreateView):
    """
    view to add new planodepagamento
    """
    model = PlanosdePagamento
    template_name = "../templates/associacao/planospagamento_update.html"
    fields = ['data_ciclo', 'frequencia_intervalo', 'frequencia', 'montante',
              'nome_plano']

    success_url = "/planospagamento"

    def form_valid(self, form):
        form.instance.entidade = self.request.user.pessoa.negocio_set.all()[0]
        return super().form_valid(form)


class EditaPlanoPagamentos(generic.UpdateView):
    model = PlanosdePagamento
    template_name = "../templates/associacao/planospagamento_update.html"
    fields = ['data_ciclo', 'frequencia_intervalo', 'frequencia', 'montante',
              'nome_plano', 'cielo_data', 'boleto_ativado']

    success_url = "/planospagamento"


class CriaCobrancaWizard(generic.FormView):
    """
    main view to create Cobranca using bootstrap wizard + model form
    """
    form_class = PlanoItemDetalhesForm
    template_name = "../templates/associacao/criarcobrancabootstrap.html"
    success_url = reverse_lazy("planospagamentos")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        q = self.request.user.pessoa.negocio_set.all()
        queryset = PlanosdePagamento.objects.all().filter(entidade=q).values_list('categoria')
        context['categorias'] = set(queryset)
        print(queryset)
        return context

    def form_invalid(self, form):
        print('eh invalido', form.cleaned_data)
        print(form.errors)
        return super().form_invalid(form)

    def form_valid(self, form):
        """
        {'nome': 'asdasdsds', 'montante': Decimal('123'),
         'parcelas': 1, 'tipo': '1', 'categoria': 'neuro',
          'data_inicio': datetime.date(2022, 11, 11)}

        :param form:
        :return:
        """
        print(form.cleaned_data)
        if form.cleaned_data['categoria'] == 'Novo':
            Q = PlanosdePagamento.objects.create(nome_plano=form.cleaned_data['nome'],
                                             frequencia_intervalo='Y',
                                             frequencia=form.cleaned_data['parcelas'],
                                             entidade=self.request.user.pessoa.negocio_set.all()[0],
                                             montante=form.cleaned_data['montante'],
                                             categoria=form.cleaned_data['novo_grupo'],
                                             tipo=form.cleaned_data['tipo']
                                             )
        else:
            Q = PlanosdePagamento.objects.create(nome_plano=form.cleaned_data['nome'],
                                                 frequencia_intervalo='Y',
                                                 frequencia=form.cleaned_data['parcelas'],
                                                 entidade=self.request.user.pessoa.negocio_set.all()[0],
                                                 montante=form.cleaned_data['montante'],
                                                 categoria=form.cleaned_data['categoria'],
                                                 tipo=form.cleaned_data['tipo']
                                                 )
        return super().form_valid(form)


class Criaboleto(generic.RedirectView):
    """
    will create a boleto
    """
    # template_name = "resultadopagamentos/boleto.html"

    def get_redirect_url(self, *args, **kwargs):
        dados = self.request.GET
        nome = dados['Nome']
        telefone = dados['CPF']
        cpf = dados['Telefone']
        plano = dados['Plano']
        q = get_object_or_404(PlanosdePagamento.objects.filter(id=plano))
        c = OrderProcess(q.cielo_data)
        resultado, boleto = c.cria_boleto(q.montante, nome, cpf)
        if resultado['status'] == 'OK':
            return boleto.url_main.replace(' ', '')


class ListaImpersonateUsers(generic.ListView):
    """
    will list all impersonate  users
    """
    queryset = User.objects.filter(pessoa__admin_negocio=True).order_by('-id')
    template_name = "associacao/listaimpersonateusers.html"
    context_object_name = 'result'




#########
########
#######
######
'''
Publich and member views should come below
'''

class MostraPagamentoComprovante(generic.DetailView):
    model = Pedido
    slug_field = 'num_pedido'
    slug_url_kwarg = 'pedido'
    template_name = "resultadopagamentos/resultadosucesso.html"

    def get_context_data(self, **kwargs):
        plano = self.object.plano_pagamento
        if self.object.status == 'finalizado':
            result_final = 2
        else:
            result_final = 0
        self.request.session['result'] = result_final
        self.request.session['nome_plano'] = plano.nome_plano
        # self.request.session['cliente_nome'] = self.object.transacao.usuario.pessoa.usuario
        self.request.session['cliente_cpf'] = self.object.transacao.usuario.CPF
        self.request.session['cliente_email'] = self.object.transacao.usuario.email
        self.request.session['negocio_nome'] = plano.entidade.name
        if plano.entidade.mostra_cnpj:
            self.request.session['negocio_CNPJ'] = plano.entidade.CNPJ
        self.request.session['negocio_logradouro'] = plano.entidade.logradouro
        self.request.session['negocio_cidade'] = plano.entidade.cidade
        self.request.session['negocio_CEP'] = plano.entidade.CEP
        self.request.session['negocio_UF'] = plano.entidade.UF
        self.request.session['plano_descricao_curta'] = plano.descricao_curta
        self.request.session['plano_valor'] = str(plano.montante)
        self.request.session['plano_tipo'] = plano.tipo
        self.request.session['returncode'] = result_final
        self.request.session['pedido_num'] = str(self.object.num_pedido)
        return super().get_context_data(**kwargs)



class EditaPerfilProprioAssociado(generic.UpdateView):
    """
    main class to edit cliente (associado view)
    """

    def get_queryset(self):
        queryset = Pessoa.objects.filter(usuario=self.request.user)
        return queryset

    model = Pessoa
    template_name = "../templates/associado/pessoa_update.html"
    fields = ['logradouro', 'numero', 'complemento',
              'email', 'CPF']

    success_url = "/cliente"

class ResultadoPagamento(generic.TemplateView):
    """
    main class for resultado de pagamento
    """
    # template_name = "../templates/associado/resultadopagamento.html"
    template_name = "../templates/resultadopagamentos/resultadosucesso.html"


class ResultadoPagamentoAssinatura(generic.TemplateView):
    """
    main class for resultado de pagamento customizado
    """
    template_name = "../templates/resultadoassinaturas/resultadosucesso.html"


@method_decorator(csrf_exempt, name='dispatch')
class ResultadoPagamentoPendente(generic.View):
    """
    Nesta classe o pagamnto eh pendende e atraves do id eh buscado no pagador
    """
    #TODO testar criacao pendente
    #TODO testar criacao pendente e depois pagamento
    #TODO testar criacao pendente e depois o retorno do pagamento
    template_name = "../templates/resultadopagamentos/resultadosucesso.html"

    def post(self, request, *args, **kwargs):
        print(request)
        print(request.POST)
        paymentid = request.POST['PaymentId']
        p = get_object_or_404(Pedido.objects.filter(cielo_transacao__payment_id=paymentid))
        c = OrderProcess(p.cielo_data)
        result = c.check_order(paymentid)
        if result['status'] == 'OK':
            print('pago por debito', result, p.plano_pagamento, p.usuario)
            m1 = MailTemplate.objects.filter(
                    situacao="pagamento").first()
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': str(p.plano_pagamento.montante), 'nome': p.nome,
                                'nome_atividade': p.plano_pagamento.nome_plano,
                                'evento_razaosocial': str(p.plano_pagamento.entidade.name),
                                'evento_cnpj': str(p.plano_pagamento.entidade.CNPJ),
                                'cpf': p.usuario.CPF}
            if not p.plano_pagamento.entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(p.usuario.email, m1)
            # p.evecom_item.paid = True
            eve = p.evecom_item
            eve.paid = True
            eve.save()
            transacao = p.transacao
            transacao.status = 'Pago'
            transacao.save()
            p.save()

            cielo_data = p.cielo_data

            print(' hotel nome', p.evecom_item.nome_hotel)
            self.request.session['result'] = result['data']
            self.request.session['nome_plano'] = p.plano_pagamento.nome_plano
            self.request.session['cliente_nome'] = p.nome
            self.request.session['cliente_cpf'] = p.usuario.CPF
            self.request.session['cliente_email'] = p.usuario.email
            if cielo_data.negocio is None:
                self.request.session['negocio_nome'] = p.plano_pagamento.entidade.name
                if p.plano_pagamento.entidade.mostra_cnpj:
                    self.request.session['negocio_CNPJ'] = p.plano_pagamento.entidade.CNPJ
                self.request.session['negocio_logradouro'] = p.plano_pagamento.entidade.logradouro
                self.request.session['negocio_cidade'] = p.plano_pagamento.entidade.cidade
                self.request.session['negocio_CEP'] = p.plano_pagamento.entidade.CEP
                self.request.session['negocio_UF'] = p.plano_pagamento.entidade.UF
            else:
                self.request.session['negocio_nome'] = cielo_data.negocio.name
                if cielo_data.negocio.mostra_cnpj:
                    self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
                self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
                self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
                self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
                self.request.session['negocio_UF'] = cielo_data.negocio.UF
            self.request.session['plano_descricao_curta'] = p.plano_pagamento.descricao_curta
            self.request.session['plano_valor'] = str(p.plano_pagamento.montante)
            self.request.session['plano_tipo'] = p.plano_pagamento.tipo
            self.request.session['returncode'] = result['data'].get('Payment').get('Status')
            self.request.session['pedido_num'] = str(p.num_pedido)
            if p.evecom_item.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = p.evecom_item.nome_hotel
                self.request.session['check_in'] = p.evecom_item.check_in
                self.request.session['check_out'] = p.evecom_item.check_out
                self.request.session['categoria'] = p.evecom_item.categoria
                self.request.session['data_entrada'] = p.evecom_item.data_entrada
                self.request.session['data_saida'] = p.evecom_item.data_saida
                self.request.session['id_reserva'] = p.evecom_item.id_reserva
                self.request.session['tipo_apto'] = p.evecom_item.tipo_apto
                self.request.session['numero_acompanhantes'] = p.evecom_item.numero_acompanhantes
            ## falta marcar pedido como Pago e transacao como paga tb e adicionar dados no cielotrans
            return redirect(to="resultadpagamento")

        else:
            print('nao pago', result, p.num_pedido)
            try:
                return HttpResponse('Falha no pagamento:{0}'.format(result.get('data').get('Payment').get('ReturnMessage')))
            except:
                return HttpResponse('Falha no pagamento: autorizacao negada')


class ClientePagar(generic.FormView):
    """
    class for payment unique using uuid
    """
    form_class = CartaoForm
    template_name = "../templates/associado/pagamento_tabbed.html"
    success_url = "/cliente/resultadopagamento/"

    @cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "associado/painelassociado.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"

        # try:
        print('procurando plano', self.kwargs['uuid'])
        q = get_object_or_404(PlanosdePagamento.objects.filter(uuid_pagamento=self.kwargs['uuid']))
        context['result'] = q
        context['plano'] = q
        return context

        # except KeyError:
        #     print(' error finding plano')
        #     return context
        # return context

    def form_valid(self, form):
        print('form valid')
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        cpf = form.cleaned_data['CPF']
        email = form.cleaned_data['Email']
        plano = PlanosdePagamento.objects.get(uuid_pagamento=self.kwargs['uuid'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(plano.cielo_data)
        print(process)
        final_result = process.order_buy(plano.id, cpf, 'cielo', numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario, tipo=tipo_escolhido)
        print('final result na view', final_result)
        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('atribute error no result da view',e )
        self.request.session['result'] = final_result['data']
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano
        pedido.nome = nome
        pedido.save()
        cielo_data = plano.cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano.nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano.entidade.name
            if plano.entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano.entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano.entidade.logradouro
            self.request.session['negocio_cidade'] = plano.entidade.cidade
            self.request.session['negocio_CEP'] = plano.entidade.CEP
            self.request.session['negocio_UF'] = plano.entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
        self.request.session['plano_descricao_curta'] = plano.descricao_curta
        self.request.session['plano_valor'] = str(plano.montante)
        self.request.session['plano_tipo'] = plano.tipo
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2:
            m1 = MailTemplate.objects.filter(
                    situacao="pagamento").first()
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': str(plano.montante), 'nome': nome,
                                'nome_atividade': plano.nome_plano,
                                'evento_razaosocial': str(plano.entidade.name),
                                'evento_cnpj': str(plano.entidade.CNPJ),
                                'cpf': cpf}
            if not plano.entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1)
        return super().form_valid(form)


class ClientePagarTab(generic.FormView):
    """
    class for payment only plan id is sent.

    """
    form_class = CartaoForm
    template_name = "../templates/associado/pagamento_tabbed.html"
    success_url = "/cliente/resultadopagamento/"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cpf_is_sent = None

    @cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
    def dispatch(self, request, *args, **kwargs):
        self.plano = PlanosdePagamento.objects.get(uuid_pagamento=self.kwargs['plano'])
        return super().dispatch(request, *args, **kwargs)

    def render_to_response(self, context, **response_kwargs):
        context = super().get_context_data()
        result = {'cartao_ativado': True}
        ##planoid = self.kwargs['plano']
        ##plano = PlanosdePagamento.objects.get(id=planoid)
        plano = self.plano
        result['montante'] = plano.montante
        entidade = plano.entidade
        result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
        result['logo_evento'] = entidade.template_evecom.logo_evento#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        result['nome_plano'] =plano.nome_plano
        result['parcelas'] = entidade.parcelas
        result['formato_cpf'] = entidade.template_evecom.formato_cpf

        try:
            result['logo_evento'] = plano.logo_evento.url
        except ValueError:
            print('sem logo definido',entidade)
        context['result'] = result
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"
        return super().render_to_response(context, **response_kwargs)

    def get_form(self, step=None, data=None, files=None):
        plano = self.plano
        entidade = plano.entidade
        form = super(ClientePagarTab, self).get_form()
        #TODO fazer teste pra checkbox
        if self.request.GET.get('cpf'):
            form.fields.pop('CPF')
            self.cpf_is_sent = True
        if self.plano.entidade.integracao_terceiro:
            form.fields['Pagamento_terceiro'] = forms.BooleanField(label="Beneficiario é dono do cartão",initial=True, required=False)
            form.fields['nome_beneficiario'] = forms.CharField(label="Nome beneficiario", max_length=200, required=False)
        #TODO se ta escondendo a parcela pra que calcular ela abaixo?

        #TODO fazer as opcoes de parcelas nos convenios (cielodata)
        if str(plano.cielo_data.nome) == 'mfm':
            form.fields['parcelas'].widget = forms.HiddenInput()
            return form

        montante = Decimal(plano.montante)
        par = []
        #TODO testar o pagamentotab com os parcelamentos
        if entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria')).exists():
            print('tem parcelamento categoria',self.request.GET.get('categoria'))
            parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria'))[0].parcelas
        else:
            if entidade.parcelas == 1 and plano.parcelas is  None:
                form.fields['parcelas'].widget = forms.HiddenInput()
                return form
            else:
                parcelas_num = entidade.parcelas
                print('tem parcelamento na categoria')
        if plano.parcelas:
            print('tem parcelas fixo', plano.parcelas)
            parcelas_num = plano.parcelas

        for i in range(1, parcelas_num+1):
            if i == 1 and plano.desconto_a_vista:
                par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(plano.valor_a_vista))))
            else:
                par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
        # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
        #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
        parcelas = par
        form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
        print(parcelas)
        return form

    def create_evecom_data(self, request):
        """
        from request get the data to create all evecom data
        :param request:
        :return:
        """
        kwargs = {}
        tmp = EvecomData._meta.get_fields()
        fields = []
        for i in tmp:
            fields.append(i.name)
        for key, value in request.GET.items():
            if key not in fields:
                print('pass no', key, value)
            else:
                print(' ok no', key, value)
                kwargs[key] = value
        print(kwargs)
        q = EvecomData.objects.create(**kwargs)
        print(q,q.invoice)
        return q

    def get_plano(self, method='get', **kwargs ):
        """
        will get the right plan
        :param kwargs:
        :return:
        """
        print('entrei no get_plano')
        # if self.request.method == 'GET':
        print('procurando por GET method')
        plano = self.plano
        return {'status':'OK','planoid':plano.id, 'method':'get', 'planoqueryset':plano}



    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"

        try:
            print('procurando plano')
            # q = get_object_or_404(PlanosdePagamento.objects.filter(id=self.kwargs['plano']))
            ##plano = self.get_plano()
            plano = self.plano
            print('plano no context,get',plano)

            context['result'] = plano##['planoqueryset']
            context['plano'] = plano##['planoqueryset']
            context['negocio'] = plano.entidade

        except KeyError as e:
            print(' error finding plano:',e)
            return context
        return context

    def form_valid(self, form):
        print('form valid')
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        if self.cpf_is_sent:
            cpf = self.request.GET.get('cpf')
        else:
            cpf = form.cleaned_data['CPF']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        #if form.cleaned_data['nome_beneficiario'] is '':
        if form.cleaned_data.get('nome_beneficiario') is '' or form.cleaned_data.get('nome_beneficiario') is None:
            nome = form.cleaned_data['nome_cartao']
        else:
            nome = form.cleaned_data['nome_beneficiario']
        email = form.cleaned_data['Email']
        #plano = [self.get_plano()['planoqueryset']]
        plano = [self.plano]
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        parcelas = int(form.cleaned_data['parcelas'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(plano[0].cielo_data)
        print(process)
        print('dados compra',plano[0].id, cpf, 'cielo', numero[-4:], validade_ano,
                                         validade_mes,
                                         nome, bandeira, email, usuario,
                                         plano[0].tipo, 1)
        final_result = process.order_buy(plano[0].id, cpf, 'cielo', numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario,
                                         tipo=tipo_escolhido, parcelas=parcelas, desconto_a_vista=self.plano.desconto_a_vista)
        print('final result eh', final_result)
        valor_real = Decimal(final_result.get('rawdata').get('Payment').get('Amount')/100)
        evedata = self.create_evecom_data(self.request)
        evedata.business = plano[0].entidade.email
        evedata.amount = "{0:.2f}".format(valor_real)
        evedata.email = email
        evedata.item_name = plano[0].nome_plano
        if self.request.GET.get('invoice'):
            evedata.invoice = self.request.GET.get('invoice')
        else:
            evedata.invoice = plano[0].uuid_pagamento
        evedata.cpf = cpf
        evedata.parcelas = parcelas
        if plano[0].entidade.integracao_terceiro:
            evedata.tipo_integracao = 1
            evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
        if plano[0].entidade.integracao_marketing:
            evedata.integracao_marketing = plano[0].entidade.integracao_marketing
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano[0]
        pedido.evecom_item = evedata
        pedido.nome = nome
        evedata.comprovante_url = 'https://planopago.com.br' + str(
            reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
        print('url comprovante', evedata.comprovante_url)
        evedata.save()
        pedido.save()
        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('erro no get debit,', e)

        cielo_data = plano[0].cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano[0].nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano[0].entidade.name
            if plano[0].entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
            self.request.session['negocio_cidade'] = plano[0].entidade.cidade
            self.request.session['negocio_CEP'] = plano[0].entidade.CEP
            self.request.session['negocio_UF'] = plano[0].entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
        self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
        self.request.session['plano_valor'] = "{0:.2f}".format(valor_real)
        if 'tipo' in self.request.GET:
            self.request.session['plano_tipo'] = self.request.GET['tipo']
        else:
            self.request.session['plano_tipo'] = plano[0].tipo
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2:
            m1 = MailTemplate.objects.filter(
                situacao="pagamento").first()
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': "{0:.2f}".format(valor_real), 'nome': nome,
                                'nome_atividade': plano[0].nome_plano,
                                'evento_razaosocial': str(plano[0].entidade.name),
                                'evento_cnpj': str(plano[0].entidade.CNPJ),
                                'cpf': cpf}
            if not plano[0].entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1)
            evedata.paid = True
            evedata.save()
            if plano[0].entidade.sendevecom_sync == True:
                c = EvecomProcess()
                if c.connect()['status'] == 'OK':
                    c.send_data_db(evedata)
                    c.disconnect()
                    evedata.sent = True
                    evedata.save()
                else:
                    print('nao conectado')
            if plano[0].entidade.imprime_etiqueta == True:
                self.request.session['imprime_etiqueta'] = 1
                self.request.session['url_etiqueta'] = plano[0].entidade.url_etiqueta
                try:
                    self.request.session['etiqueta'] = evedata.invoice.split('_')[0]
                except Exception as e:
                    print('erro na etiqueta ', e)
            else:
                self.request.session['imprime_etiqueta'] = 0

            try:
                self.request.session['inscricao'] = evedata.invoice.split('_')[0]
            except Exception as e:
                print('erro na etiqueta ', e)

            print(' hotel nome', evedata.nome_hotel)
            if evedata.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = evedata.nome_hotel
                self.request.session['check_in'] = evedata.check_in
                self.request.session['check_out'] = evedata.check_out
                self.request.session['categoria'] = evedata.categoria
                self.request.session['data_entrada'] = evedata.data_entrada
                self.request.session['data_saida'] = evedata.data_saida
                self.request.session['id_reserva'] = evedata.id_reserva
                self.request.session['tipo_apto'] = evedata.tipo_apto
                self.request.session['numero_acompanhantes'] = evedata.numero_acompanhantes
                evedata.save()
        return super().form_valid(form)


@method_decorator(csrf_exempt, name='dispatch')
class ClientePagarTabEvecomPOST(generic.FormView):
    """
    POST
    main class for payments that came from evecom hotlink
    mostly it does:
    <form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_blank">
                  <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
    </form>
    brain somente!!!
    Brain 2017 – INI
    Nome do Evento: World Congress on Brain, Behavior and Emotions
    Público estimado: 5.000,00
    Razão Social: Instituto de Neurociências Integradas
    CNPJ: 04.186.959/0001-14
        Endereço: Rua Barão do Triunfo, 448/501, Porto Alegre CEP 90130-100
        t

    """
    #TODO criar um overload para local
    #TODO simplificar if cielo_data.negocio is None:
    form_class = CartaoForm
    template_name = "../templates/associado/pagamento_tabbed.html"
    success_url = "/cliente/resultadopagamento/"

    def get_success_url(self):
        return super().get_success_url()

    def __init__(self, *args, **kwargs):
        self.statusinitial = 0
        super().__init__(*args, **kwargs)

    def dispatch(self, request, *args, **kwargs):
        if self.statusinitial ==0:
            self.entidade = Negocio.objects.get(email=self.request.POST['business'])
            self.montante = float(self.request.POST['amount'])
        return super().dispatch(request, *args, **kwargs)

    def get_form(self, step=None, data=None, files=None):

        ##entidade = Negocio.objects.get(email=self.request.POST['business'])
        entidade = self.entidade
        form = super(ClientePagarTabEvecomPOST, self).get_form()
        #TODO se ta escondendo a parcela pra que calcular ela abaixo?

        #TODO fazer as opcoes de parcelas nos convenios (cielodata)
        if str(self.kwargs['convenio']).lower() == 'mfm':
            form.fields['parcelas'].widget = forms.HiddenInput()
            return form

        montante = Decimal(self.request.POST['amount'])
        par = []
        if entidade.planoparcelamento_set.filter(codigo=self.request.POST.get('categoria')).exists():
            print('tem parcelamento categoria',self.request.GET.get('categoria'))
            parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.POST.get('categoria'))[0].parcelas
        else:
            if entidade.parcelas == 1:
                form.fields['parcelas'].widget = forms.HiddenInput()
                return form
            else:
                parcelas_num = entidade.parcelas
                print('nao tem parcelamento na categoria')

        for i in range(1, parcelas_num+1):
            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
        # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
        #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
        parcelas = par
        form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
        print(parcelas)
        return form


    def get_plano(self, method='get', **kwargs ):
        """
        will get the right plan
        :param kwargs:
        :return:
        """
        print('entrei no get_plano')
        print('procurando por POST method')
        ##entidade = Negocio.objects.get(email=self.request.POST['business'])
        entidade = self.entidade
        cielo_data = CieloData.objects.get(ativo=True, tipo_api='3.0', nome=self.kwargs['convenio'])
        print('convenio escolhido', cielo_data.nome)
        if 'tipo' in self.request.POST:
            plano_tipo = self.request.POST['tipo']
            planoid, created = PlanosdePagamento.objects.get_or_create(entidade=entidade,
                                                                       montante=self.montante,
                                                                       nome_plano=entidade.template_evecom.nome_plano,
                                                                       descricao_curta=entidade.template_evecom.descricao_curta,
                                                                       cielo_data=cielo_data, tipo=plano_tipo)
        else:
            planoid, created = PlanosdePagamento.objects.get_or_create(entidade=entidade,
                                                       montante=float(self.montante),
                                                                   nome_plano=entidade.template_evecom.nome_plano,
                                                                   descricao_curta=entidade.template_evecom.descricao_curta,
                                                                   cielo_data=cielo_data, tipo='Evento')
        return {'status':'OK', 'method':'post', 'planoqueryset': planoid}

    def render_to_response(self, context, **response_kwargs):
        context = super().get_context_data()
        result = {'cartao_ativado': True}
        result['montante'] = self.request.POST['amount']
        entidade = Negocio.objects.get(email=self.request.POST['business'])
        result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
        result['logo_evento'] = entidade.template_evecom.logo_evento#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        plano = self.get_plano(method='POST')
        result['nome_plano'] = self.request.POST['item_name']
        result['parcelas'] = entidade.parcelas
        result['formato_cpf'] = entidade.template_evecom.formato_cpf
        try:
            result['logo_evento'] = plano['planoqueryset'].logo_evento.url
        except ValueError:
            print('sem logo definido',entidade)
        context['result'] = result
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"
        return super().render_to_response(context, **response_kwargs)

    def create_evecom_data(self, request):
        """
        from request get the data to create all evecom data
        :param request:
        :return:
        """
        kwargs = {}
        tmp = EvecomData._meta.get_fields()
        fields = []
        for i in tmp:
            fields.append(i.name)
        for key, value in request.GET.items():
            if key not in fields:
                print('pass no', key, value)
            else:
                print(' ok no', key, value)
                kwargs[key] = value
        print(kwargs)
        q = EvecomData.objects.create(**kwargs)
        print(q,q.invoice)
        return q

    def post(self, request, *args, **kwargs):
        form_class = self.get_form_class()
        form = self.get_form(form_class)
        if self.statusinitial == 0:
            self.statusinitial=1
            return self.render_to_response(self.get_context_data(form=form))
        else:
            return self.form_invalid(form)
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        print('form valid')
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        cpf = form.cleaned_data['CPF']
        email = form.cleaned_data['Email']
        plano = [self.get_plano()['planoqueryset']]
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        parcelas = int(form.cleaned_data['parcelas'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(plano[0].cielo_data)
        print(process, plano[0].cielo_data.numero)
        final_result = process.order_buy(plano[0].id, cpf, 'cielo', numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario,
                                         tipo=tipo_escolhido, parcelas=parcelas)
        print('final result eh', final_result)
        evedata = self.create_evecom_data(self.request)
        evedata.email = email
        if plano[0].entidade.integracao_terceiro:
            evedata.tipo_integracao = 1
            evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano[0]
        pedido.evecom_item = evedata
        pedido.nome = nome
        evedata.comprovante_url = 'https://planopago.com.br' + str(
            reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
        print('url comprovante', evedata.comprovante_url)
        evedata.save()
        pedido.save()
        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('erro no get debit,', e)

        cielo_data = plano[0].cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano[0].nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano[0].entidade.name
            if plano[0].entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
            self.request.session['negocio_cidade'] = plano[0].entidade.cidade
            self.request.session['negocio_CEP'] = plano[0].entidade.CEP
            self.request.session['negocio_UF'] = plano[0].entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
        self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
        self.request.session['plano_valor'] = str(plano[0].montante)
        if 'tipo' in self.request.GET:
            self.request.session['plano_tipo'] = self.request.GET['tipo']
        else:
            self.request.session['plano_tipo'] = plano[0].tipo
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2:
            m1 = MailTemplate.objects.filter(
                    situacao="pagamento").first()
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': str(plano[0].montante), 'nome': nome,
                                'nome_atividade': plano[0].nome_plano,
                                'evento_razaosocial': str(plano[0].entidade.name),
                                'evento_cnpj': str(plano[0].entidade.CNPJ),
                                'cpf': cpf}
            if not plano[0].entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1)
            evedata.paid = True
            evedata.save()
            if plano[0].entidade.sendevecom_sync == True:
                c = EvecomProcess()
                if c.connect()['status'] == 'OK':
                    c.send_data_db(evedata)
                    c.disconnect()
                    evedata.sent = True
                    evedata.save()
                else:
                    print('nao conectado')
            if plano[0].entidade.imprime_etiqueta == True:
                self.request.session['imprime_etiqueta'] = 1
                self.request.session['url_etiqueta'] = plano[0].entidade.url_etiqueta
                try:
                    self.request.session['etiqueta'] = evedata.invoice.split('_')[0]
                except Exception as e:
                    print('erro na etiqueta ', e)
            else:
                self.request.session['imprime_etiqueta'] = 0

            try:
                self.request.session['inscricao'] = evedata.invoice.split('_')[0]
            except Exception as e:
                print('erro na etiqueta ', e)

            print(' hotel nome', evedata.nome_hotel)
            if evedata.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = evedata.nome_hotel
                self.request.session['check_in'] = evedata.check_in
                self.request.session['check_out'] = evedata.check_out
                self.request.session['categoria'] = evedata.categoria
                self.request.session['data_entrada'] = evedata.data_entrada
                self.request.session['data_saida'] = evedata.data_saida
                self.request.session['id_reserva'] = evedata.id_reserva
                self.request.session['tipo_apto'] = evedata.tipo_apto
                self.request.session['numero_acompanhantes'] = evedata.numero_acompanhantes
                evedata.save()
        return super().form_valid(form)




@method_decorator(csrf_exempt, name='dispatch')
class ClientePagarTabEvecom(generic.FormView):
    """
    this is the class!!
    main class for payments that came from evecom hotlink
    mostly it does:
    <form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_blank">
                  <input type="hidden" name="business" value="<EMAIL>">
                  <input type="hidden" name="lc" value="BR">
                  <input type="hidden" name="cmd" value="_xclick">
                  <input type="hidden" name="invoice" value="243361_1">
                  <input type="hidden" name="item_name" value="243361 - FREDERICO CORREA DA SILVA - Inscrição no 10º Congresso Paulista de Geriatria e Gerontologia">
                  <input type="hidden" name="amount" value="530.00">
                  <input type="hidden" name="currency_code" value="BRL"> <!-- USD se for dólar -->
                  <input type="image" name="submit" border="0" src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/buy-logo-large.png" alt="PayPal - The safer, easier way to pay online">
                  <img alt="" border="0" src="https://www.paypalobjects.com/pt_BR/i/scr/pixel.gif" width="1" height="1">
    </form>
    brain somente!!!
    Brain 2017 – INI
    Nome do Evento: World Congress on Brain, Behavior and Emotions
    Público estimado: 5.000,00
    Razão Social: Instituto de Neurociências Integradas
    CNPJ: 04.186.959/0001-14
        Endereço: Rua Barão do Triunfo, 448/501, Porto Alegre CEP 90130-100
        t

    """
    #TODO criar um overload para local
    #TODO simplificar if cielo_data.negocio is None:
    form_class = CartaoForm
    #template_name = "../templates/associado/pagamento_tabbed.html"
    success_url = "/cliente/resultadopagamento/"

    def get(self, request, *args, **kwargs):

        return super().get(self, request, *args, **kwargs)

    def get_success_url(self):
        return super().get_success_url()

    def get_form(self, step=None, data=None, files=None):

        entidade = Negocio.objects.get(email=self.request.GET['business'])
        form = super(ClientePagarTabEvecom, self).get_form()
        montante = Decimal(self.request.GET['amount'])
        #TODO se ta escondendo a parcela pra que calcular ela abaixo?

        #TODO fazer as opcoes de parcelas no negocio na mfm (cielodata)
        if str(self.kwargs['convenio']).lower() == 'mfm':
            if entidade.parcelas_MFM >1:
                if montante/entidade.parcelas_MFM >= entidade.parcelas_MFM_minimo:
                    parcelas_num = entidade.parcelas_MFM
                    par = []
                    for i in range(1, parcelas_num + 1):
                        if i == 1 and entidade.parcelas_MFM_minimo <= Decimal(montante) / Decimal(i):
                            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                        if i == 2 and entidade.parcelas_MFM_minimo_2x <= Decimal(montante) / Decimal(i):
                            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                        if i == 3 and entidade.parcelas_MFM_minimo_3x <= Decimal(montante) / Decimal(i):
                            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                        if i == 4 and entidade.parcelas_MFM_minimo_4x <= Decimal(montante) / Decimal(i):
                            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                        if i == 5 and entidade.parcelas_MFM_minimo_5x <= Decimal(montante) / Decimal(i):
                            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                        if i == 6 and entidade.parcelas_MFM_minimo_6x <= Decimal(montante) / Decimal(i):
                            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                        #par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante) / Decimal(i))))
                    # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
                    #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
                    parcelas = par
                    form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
                    return form
                else:

                    form.fields['parcelas'].widget = forms.HiddenInput()
                    return form
            else:

                form.fields['parcelas'].widget = forms.HiddenInput()
                return form

        montante = Decimal(self.request.GET['amount'])
        par = []
        if entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria')).exists():
            print('tem parcelamento categoria',self.request.GET.get('categoria'))
            parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria'))[0].parcelas
        else:
            if entidade.parcelas == 1:
                form.fields['parcelas'].widget = forms.HiddenInput()
                return form
            else:
                #TODO fazer teste de parcela com minimo atingido ou nao
                if montante / entidade.parcelas >= entidade.parcelas_convenio_minimo:
                    parcelas_num = entidade.parcelas
                    print('nao tem parcelamento na categoria, mas tem parcela comum')
                else:
                    form.fields['parcelas'].widget = forms.HiddenInput()
                    return form
        for i in range(1, parcelas_num+1):
            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
        # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
        #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
        parcelas = par
        form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
        print(parcelas)
        return form


    def get_plano(self, method='get', **kwargs ):
        """
        will get the right plan
        :param kwargs:
        :return:
        """
        print('entrei no get_plano')
        print('procurando por POST method')
        entidade = Negocio.objects.get(email=self.request.GET['business'])
        cielo_data = CieloData.objects.get(ativo=True, tipo_api='3.0', nome=self.kwargs['convenio'])
        print('convenio escolhido', cielo_data.nome)
        if 'tipo' in self.request.GET:
            plano_tipo = self.request.GET['tipo']
            planoid, created = PlanosdePagamento.objects.get_or_create(entidade=entidade,
                                                                       montante=float(self.request.GET['amount']),
                                                                       nome_plano=entidade.template_evecom.nome_plano,
                                                                       descricao_curta=entidade.template_evecom.descricao_curta,
                                                                       cielo_data=cielo_data, tipo=plano_tipo)
        else:
            planoid, created = PlanosdePagamento.objects.get_or_create(entidade=entidade,
                                                       montante=float(self.request.GET['amount']),
                                                                   nome_plano=entidade.template_evecom.nome_plano,
                                                                   descricao_curta=entidade.template_evecom.descricao_curta,
                                                                   cielo_data=cielo_data, tipo='Evento')
        return {'status':'OK', 'method':'post', 'planoqueryset': planoid}

    def render_to_response(self, context, **response_kwargs):
        context = super().get_context_data()
        entidade = Negocio.objects.get(email=self.request.GET['business'])
        # TODO testar a troca de template
        self.template_name = entidade.template_evecom.template_file
        if entidade.traducao:
            print('setando para,', entidade.traducao)
            translation.activate(entidade.traducao)
            self.request.LANGUAGE_CODE = translation.get_language()
        result = {'cartao_ativado': True}
        result['montante'] = self.request.GET['amount']
        result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
        result['logo_evento'] = entidade.template_evecom.logo_evento#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        plano = self.get_plano(method='POST')
        result['nome_plano'] = self.request.GET['item_name']
        result['parcelas'] = entidade.parcelas
        result['formato_cpf'] = entidade.template_evecom.formato_cpf
        print('language is,', self.request.LANGUAGE_CODE)
        try:
            result['logo_evento'] = plano['planoqueryset'].logo_evento.url
        except ValueError:
            print('sem logo definido',entidade)
        context['result'] = result
        context['negocio'] = entidade
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"
        return super().render_to_response(context, **response_kwargs)

    def create_evecom_data(self, request):
        """
        from request get the data to create all evecom data
        :param request:
        :return:
        """
        kwargs = {}
        tmp = EvecomData._meta.get_fields()
        fields = []
        for i in tmp:
            fields.append(i.name)
        for key, value in request.GET.items():
            if key not in fields:
                print('pass no', key, value)
            else:
                print(' ok no', key, value)
                kwargs[key] = value
        print(kwargs)
        q = EvecomData.objects.create(**kwargs)
        print(q)
        return q

    def form_valid(self, form):
        print('form valid')
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        cpf = form.cleaned_data['CPF']
        email = form.cleaned_data['Email']
        plano = [self.get_plano()['planoqueryset']]
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        parcelas = int(form.cleaned_data['parcelas'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(plano[0].cielo_data)
        print(process, plano[0].cielo_data.numero)
        final_result = process.order_buy(plano[0].id, cpf, 'cielo', numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario,
                                         tipo=tipo_escolhido, parcelas=parcelas)
        print('final result eh', final_result)
        evedata = self.create_evecom_data(self.request)
        evedata.email= email
        if plano[0].entidade.integracao_terceiro:
            evedata.tipo_integracao = 1
            evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
        if plano[0].entidade.integracao_marketing:
            evedata.integracao_marketing= plano[0].entidade.integracao_marketing
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano[0]
        pedido.evecom_item = evedata
        pedido.nome = nome
        evedata.comprovante_url = 'https://planopago.com.br' + str(
            reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
        print('url comprovante', evedata.comprovante_url)
        evedata.save()
        if self.request.GET.get('utm_source'):
            pedido.utm_source = self.request.GET.get('utm_source')
        if self.request.GET.get('utm_medium'):
            pedido.utm_medium = self.request.GET.get('utm_medium')
        if self.request.GET.get('utm_campaign'):
            pedido.utm_campaign = self.request.GET.get('utm_campaign')
        if self.request.GET.get('utm_term'):
            pedido.utm_term = self.request.GET.get('utm_term')
        if self.request.GET.get('utm_content'):
            pedido.utm_content = self.request.GET.get('utm_content')
        pedido.save()
        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('erro no get debit,', e)

        cielo_data = plano[0].cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano[0].nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        try:
            self.request.session[LANGUAGE_SESSION_KEY] = plano[0].entidade.traducao
        except Exception as e:
            print('error tentando criar a sessao key',e)
        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano[0].entidade.name
            if plano[0].entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
            self.request.session['negocio_cidade'] = plano[0].entidade.cidade
            self.request.session['negocio_CEP'] = plano[0].entidade.CEP
            self.request.session['negocio_UF'] = plano[0].entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
            self.request.session['google_tag_enabled'] = plano[0].entidade.google_tag_enabled
            self.request.session['google_tag_version'] = plano[0].entidade.google_tag_version
        self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
        self.request.session['plano_valor'] = str(plano[0].montante)
        if 'tipo' in self.request.GET:
            self.request.session['plano_tipo'] = self.request.GET['tipo']
        else:
            self.request.session['plano_tipo'] = plano[0].tipo
        self.request.session['invoice'] = self.request.GET.get('invoice')
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2:
            m1 = MailTemplate.objects.get(
                    situacao="pagamento", traducao=plano[0].entidade.traducao)
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': str(plano[0].montante), 'nome': nome,
                                'nome_atividade': plano[0].nome_plano,
                                'evento_razaosocial': str(plano[0].entidade.name),
                                'evento_cnpj': str(plano[0].entidade.CNPJ),
                                'cpf': cpf}
            if not plano[0].entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1)
            evedata.paid = True
            evedata.save()
            if plano[0].entidade.google_tag_enabled:
                self.request.session['google_tag_enabled'] = plano[0].entidade.google_tag_enabled
                self.request.session['google_tag_head'] = plano[0].entidade.google_tag_head
                self.request.session['google_tag_body'] = plano[0].entidade.google_tag_body
                self.request.session['google_tag_version'] = plano[0].entidade.google_tag_version
            if plano[0].entidade.sendevecom_sync == True:
                c = EvecomProcess()
                if c.connect()['status'] == 'OK':
                    c.send_data_db(evedata)
                    c.disconnect()
                    evedata.sent = True
                    evedata.save()
                else:
                    print('nao conectado')
            if plano[0].entidade.imprime_etiqueta == True:
                self.request.session['imprime_etiqueta'] = 1
                self.request.session['url_etiqueta'] = plano[0].entidade.url_etiqueta
                try:
                    self.request.session['etiqueta'] = evedata.invoice.split('_')[0]
                except Exception as e:
                    print('erro na etiqueta ', e)
            else:
                self.request.session['imprime_etiqueta'] = 0

            try:
                self.request.session['inscricao'] = evedata.invoice.split('_')[0]
            except Exception as e:
                print('erro na etiqueta ', e)

            print(' hotel nome', evedata.nome_hotel)
            if evedata.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = evedata.nome_hotel
                self.request.session['check_in'] = evedata.check_in
                self.request.session['check_out'] = evedata.check_out
                self.request.session['categoria'] = evedata.categoria
                self.request.session['data_entrada'] = evedata.data_entrada
                self.request.session['data_saida'] = evedata.data_saida
                self.request.session['id_reserva'] = evedata.id_reserva
                self.request.session['tipo_apto'] = evedata.tipo_apto
                self.request.session['numero_acompanhantes'] = evedata.numero_acompanhantes
                evedata.save()
        return super().form_valid(form)



class ExtratoAssociado(generic.ListView):
    """
    main class to report on member statement
    """
    template_name = "../templates/associado/extratofinanceiro.html"
    context_object_name = 'result'

    # model = Transacoes

    def get_queryset(self):
        query = Transacoes.objects.all().filter(conta__usuario=self.request.user.pessoa)
        return query


class ProfileView(generic.TemplateView):
    """
    main view for profile
    """
    template_name = "social/profile.html"


"""
AJAX Calls
"""


class AssociacaoRetornaEstatisticaJson(generic.View):
    """
    main class to return estatis for dashboard
    """
    # TODO: testar estatistica inscritos no mes corrente

    def get(self, request, *args, **kwargs):
        try:
            self.request.user.pessoa
            print(self.request.user)
        except:
            raise Http404("Please Log in")
        data = {1: 'Jan', 2: 'Fev', 3: 'Mar', 4: 'Abr', 5: 'Mai', 6: 'Jun', 7: 'Jul',
                8: 'Ago', 9: 'Set', 10: 'Out', 11: 'Nov', 12: 'Dez'}
        if self.request.user.pessoa.visualiza_pagamentos:
            neg = self.request.user.pessoa.visualizadores.get()
        else:
            neg = get_object_or_404(Negocio.objects.all().filter(admin=self.request.user.pessoa))
        from datetime import date
        queryset = Estatisticas.objects.all().filter(negocio=neg, tipo=self.kwargs['tipo'], ano=date.today().year).values_list('mes', 'total').order_by('mes')
        tmp = []
        for i in queryset:
            print(i)
            tmp.append((data[i[0]], i[1]))
        print(tmp)
        return HttpResponse(json.dumps(tmp, cls=DjangoJSONEncoder))

@receiver(user_signed_up)
def user_siging_up(request, user, **kwargs):
    """
    get the signal and creates a new pessoa for the new user
    :param request:
    :param user:
    :return:
    """
    p = Pessoa.objects.create(usuario=user, email=user.email, CPF=user.username)
    Conta.objects.create(usuario=p)


"""
Here are the views that pre validate above views

"""




class ClientePagarValidated(generic.FormView):
    """
    class for payment only plan id is sent.

    """
    form_class = CartaoForm
    template_name = "../templates/associado/pagamento_tabbed.html"
    success_url = "/cliente/resultadopagamento/"

    def __init__(self, *args, **kwargs):
        self.cpf = None
        self.validation = None
        super().__init__(*args, **kwargs)

    @cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
    def dispatch(self, request, *args, **kwargs):
        self.validation = Validation.objects.get(id=self.request.session['validation_plan'])
        if self.request.session['lista_preco']:
            lista_preco = self.request.session['lista_preco']
            montante = PricePlan.objects.get(id=lista_preco).preco
        else:
            montante = self.validation.preco_padrao

        self.cpf = self.request.session['cpf']
        self.plano, created = PlanosdePagamento.objects.get_or_create(montante=montante, entidade=self.validation.entidade,
                                                                      nome_plano=self.validation.nome,
                                                                      cielo_data=self.validation.convenio,
                                                                      descricao_curta=self.validation.entidade.template_evecom.descricao_curta)
        return super().dispatch(request, *args, **kwargs)

    def render_to_response(self, context, **response_kwargs):
        context = super().get_context_data()
        result = {'cartao_ativado': True}
        plano = self.plano
        result['montante'] = plano.montante
        entidade = plano.entidade
        result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
        result['logo_evento'] = self.validation.logotipo#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        result['nome_plano'] =plano.nome_plano
        result['parcelas'] = entidade.parcelas
        result['formato_cpf'] = entidade.template_evecom.formato_cpf

        try:
            result['logo_evento'] = plano.logo_evento.url
        except ValueError:
            print('sem logo definido',entidade)
        context['result'] = result
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"
        return super().render_to_response(context, **response_kwargs)

    def get_form(self, step=None, data=None, files=None):
        plano = self.plano
        entidade = plano.entidade
        form = super(ClientePagarValidated, self).get_form()
        #TODO fazer teste pra checkbox
        if self.request.session.get('cpf'):
            form.fields.pop('CPF')
        # if self.plano.entidade.integracao_terceiro:
        #     form.fields['Pagamento_terceiro'] = forms.BooleanField(label="Beneficiario é dono do cartão",initial=True, required=False)
        #     form.fields['nome_beneficiario'] = forms.CharField(label="Nome beneficiario", max_length=200, required=False)
        #TODO se ta escondendo a parcela pra que calcular ela abaixo?

        # #TODO fazer as opcoes de parcelas nos convenios (cielodata)

        montante = Decimal(plano.montante)
        par = []
        #TODO testar o pagamentotab com os parcelamentos
        if entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria')).exists():
            print('tem parcelamento categoria',self.request.GET.get('categoria'))
            parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria'))[0].parcelas
        else:
            if entidade.parcelas == 1 and plano.parcelas is  None:
                form.fields['parcelas'].widget = forms.HiddenInput()
                return form
            else:
                parcelas_num = entidade.parcelas
                print('tem parcelamento na categoria')
        if plano.parcelas:
            print('tem parcelas fixo', plano.parcelas)
            parcelas_num = plano.parcelas

        for i in range(1, parcelas_num+1):
            par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
        parcelas = par
        form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
        print(parcelas)
        return form

    def get_plano(self, method='get', **kwargs ):
        """
        will get the right plan
        :param kwargs:
        :return:
        """
        print('entrei no get_plano')
        # if self.request.method == 'GET':
        print('procurando por GET method')
        plano = self.plano
        return {'status':'OK','planoid':plano.id, 'method':'get', 'planoqueryset':plano}



    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"

        try:
            print('procurando plano')
            plano = self.plano
            print('plano no context,get',plano)

            context['result'] = plano##['planoqueryset']
            context['plano'] = plano##['planoqueryset']
            context['negocio'] = plano.entidade

        except KeyError as e:
            print(' error finding plano:',e)
            return context
        return context

    def form_valid(self, form):
        print('form valid')
        cpf = self.cpf
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        if form.cleaned_data.get('nome_beneficiario') is None:
            nome = form.cleaned_data['nome_cartao']
        else:
            nome = form.cleaned_data['nome_beneficiario']
        email = form.cleaned_data['Email']
        plano = [self.plano]
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        parcelas = int(form.cleaned_data['parcelas'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(self.validation.convenio)
        print(process)
        print('dados compra',plano[0].id, cpf, 'cielo', numero[-4:], validade_ano,
                                         validade_mes,
                                         nome, bandeira, email, usuario,
                                         plano[0].tipo, 1)
        final_result = process.order_buy(plano[0].id, cpf, 'cielo', numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario,
                                         tipo=tipo_escolhido, parcelas=parcelas)
        print('final result eh', final_result)
        evedata = EvecomData.objects.create()
        evedata.business = plano[0].entidade.email
        evedata.amount = plano[0].montante
        evedata.email = email
        evedata.item_name = plano[0].nome_plano
        evedata.invoice = plano[0].uuid_pagamento
        evedata.cpf = cpf
        evedata.parcelas = parcelas
        if plano[0].entidade.integracao_terceiro:
            evedata.tipo_integracao = 1
            evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
        if plano[0].entidade.integracao_marketing:
            evedata.integracao_marketing= plano[0].entidade.integracao_marketing
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano[0]
        pedido.evecom_item = evedata
        pedido.nome = nome
        evedata.comprovante_url = 'https://planopago.com.br' + str(
            reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
        print('url comprovante', evedata.comprovante_url)
        evedata.save()
        pedido.save()
        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('erro no get debit,', e)

        cielo_data = plano[0].cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano[0].nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano[0].entidade.name
            if plano[0].entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
            self.request.session['negocio_cidade'] = plano[0].entidade.cidade
            self.request.session['negocio_CEP'] = plano[0].entidade.CEP
            self.request.session['negocio_UF'] = plano[0].entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
        self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
        self.request.session['plano_valor'] = str(plano[0].montante)
        if 'tipo' in self.request.GET:
            self.request.session['plano_tipo'] = self.request.GET['tipo']
        else:
            self.request.session['plano_tipo'] = plano[0].tipo
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2:
            m1 = MailTemplate.objects.filter(
                situacao="pagamento").first()
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': str(plano[0].montante), 'nome': nome,
                                'nome_atividade': plano[0].nome_plano,
                                'evento_razaosocial': str(plano[0].entidade.name),
                                'evento_cnpj': str(plano[0].entidade.CNPJ),
                                'cpf': cpf}
            if not plano[0].entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1, ccenabled=True)
            evedata.paid = True
            evedata.save()
            if plano[0].entidade.sendevecom_sync == True:
                c = EvecomProcess()
                if c.connect()['status'] == 'OK':
                    c.send_data_db(evedata)
                    c.disconnect()
                    evedata.sent = True
                    evedata.save()
                else:
                    print('nao conectado')
            if plano[0].entidade.imprime_etiqueta == True:
                self.request.session['imprime_etiqueta'] = 1
                self.request.session['url_etiqueta'] = plano[0].entidade.url_etiqueta
                try:
                    self.request.session['etiqueta'] = evedata.invoice.split('_')[0]
                except Exception as e:
                    print('erro na etiqueta ', e)
            else:
                self.request.session['imprime_etiqueta'] = 0

            try:
                self.request.session['inscricao'] = evedata.invoice.split('_')[0]
            except Exception as e:
                print('erro na etiqueta ', e)

            print(' hotel nome', evedata.nome_hotel)
            if evedata.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = evedata.nome_hotel
                self.request.session['check_in'] = evedata.check_in
                self.request.session['check_out'] = evedata.check_out
                self.request.session['categoria'] = evedata.categoria
                self.request.session['data_entrada'] = evedata.data_entrada
                self.request.session['data_saida'] = evedata.data_saida
                self.request.session['id_reserva'] = evedata.id_reserva
                self.request.session['tipo_apto'] = evedata.tipo_apto
                self.request.session['numero_acompanhantes'] = evedata.numero_acompanhantes
                evedata.save()
        return super().form_valid(form)


class PreFilterPayment(generic.FormView):
    """
    Main class to pre filter payment
    """
    form_class = CPFForm
    template_name = "associacao/prevalidate.html"

    success_url = reverse_lazy("prevalidado")

    def get_context_data(self, **kwargs):
        context= super().get_context_data(**kwargs)
        context['plano'] = get_object_or_404(Validation.objects.filter(uuid=self.kwargs['uuid']))
        return context

    def form_valid(self, form):
        docidtemp = form.cleaned_data['CPF']
        docid = docidtemp.replace('.', '').replace('-', '')
        uuid = self.kwargs['uuid']
        q = Validation.objects.get(uuid=uuid)
        print(q, q.priceplan.all())
        for i in q.priceplan.all():
            print(i,i.lista_preco.all())
            for j in i.lista_preco.all():
                if j.CPF_Document == docid:
                    print(docid, i.preco)
                    preco = i.preco
                    if preco == 0.00:
                        m1 = MailTemplate.objects.filter(
                            situacao="controle").last()
                        e1 = EmailProcessor()
                        e1.set_template(m1)
                        mailtemplatedict = {'cpf': j.CPF_Document,
                                            'email': j.email,
                                            'evento': str('Gerp Isento')}

                        e1.create_body(mailtemplatedict)
                        e1.create_email_object('<EMAIL>', m1, ccenabled=True)
                        return render(self.request, 'clientes/gerp/respostainsento.html')
                    print('preco final', preco)
                    self.request.session['lista_preco'] = i.id
                    self.request.session['entidate'] = q.entidade.id
                    self.request.session['validation_plan'] = q.id
                    self.request.session['cpf'] = docid
                    return super().form_valid(form)
        print('nao achado doc, preco padrao')
        self.request.session['lista_preco'] = None
        self.request.session['entidate'] = q.entidade.id
        self.request.session['validation_plan'] = q.id
        self.request.session['cpf'] = docid
        return super().form_valid(form)

    def get_success_url(self):
        return super().get_success_url()


#######
######
### ASSINATURAS

# class ClientePagarTabAssinaturaToken(generic.FormView):
#     """
#     class for payment only plan id is sent.
#
#     """
#     form_class = CartaoForm
#     template_name = "../templates/associado/pagamento_tabbed.html"
#     success_url = "/cliente/resultadopagamento/"
#
#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.cpf_is_sent = None
#
#     @cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
#     def dispatch(self, request, *args, **kwargs):
#         self.plano = PlanosdePagamento.objects.get(uuid_pagamento=self.kwargs['plano'])
#         return super().dispatch(request, *args, **kwargs)
#
#     def render_to_response(self, context, **response_kwargs):
#         context = super().get_context_data()
#         result = {'cartao_ativado': True}
#         ##planoid = self.kwargs['plano']
#         ##plano = PlanosdePagamento.objects.get(id=planoid)
#         plano = self.plano
#         result['montante'] = plano.montante
#         entidade = plano.entidade
#         result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
#         result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
#         result['logo_evento'] = entidade.template_evecom.logo_evento#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
#         result['nome_plano'] = plano.nome_plano
#         result['parcelas'] = entidade.parcelas
#         result['assinatura'] = plano.assinatura
#         result['formato_cpf'] = entidade.template_evecom.formato_cpf
#
#         try:
#             result['logo_evento'] = plano.logo_evento.url
#         except ValueError:
#             print('sem logo definido',entidade)
#         context['result'] = result
#         if self.request.user.is_authenticated():
#             context['pagamento_template'] = "admin4/index.html"
#         else:
#             context['pagamento_template'] = "admin4/index_anonimo.html"
#         return super().render_to_response(context, **response_kwargs)
#
#     def get_form(self, step=None, data=None, files=None):
#         plano = self.plano
#         entidade = plano.entidade
#         form = super(ClientePagarTabAssinaturaToken, self).get_form()
#         #TODO fazer teste pra checkbox
#         if self.request.GET.get('cpf'):
#             form.fields.pop('CPF')
#             self.cpf_is_sent = True
#         if self.plano.entidade.integracao_terceiro:
#             form.fields['Pagamento_terceiro'] = forms.BooleanField(label="Beneficiario é dono do cartão",initial=True, required=False)
#             form.fields['nome_beneficiario'] = forms.CharField(label="Nome beneficiario", max_length=200, required=False)
#         #TODO se ta escondendo a parcela pra que calcular ela abaixo?
#
#         #TODO fazer as opcoes de parcelas nos convenios (cielodata)
#         if str(plano.cielo_data.nome) == 'mfm':
#             form.fields['parcelas'].widget = forms.HiddenInput()
#             return form
#
#         montante = Decimal(plano.montante)
#         par = []
#         #TODO testar o pagamentotab com os parcelamentos
#         if entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria')).exists():
#             print('tem parcelamento categoria',self.request.GET.get('categoria'))
#             parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria'))[0].parcelas
#         else:
#             if entidade.parcelas == 1 and plano.parcelas is  None:
#                 form.fields['parcelas'].widget = forms.HiddenInput()
#                 return form
#             else:
#                 parcelas_num = entidade.parcelas
#                 print('tem parcelamento na categoria')
#         if plano.parcelas:
#             print('tem parcelas fixo', plano.parcelas)
#             parcelas_num = plano.parcelas
#
#         for i in range(1, parcelas_num+1):
#             if i == 1 and plano.desconto_a_vista:
#                 par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(plano.valor_a_vista))))
#             else:
#                 par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
#         # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
#         #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
#         parcelas = par
#         form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
#         print(parcelas)
#         return form
#
#     def create_evecom_data(self, request):
#         """
#         from request get the data to create all evecom data
#         :param request:
#         :return:
#         """
#         kwargs = {}
#         tmp = EvecomData._meta.get_fields()
#         fields = []
#         for i in tmp:
#             fields.append(i.name)
#         for key, value in request.GET.items():
#             if key not in fields:
#                 print('pass no', key, value)
#             else:
#                 print(' ok no', key, value)
#                 kwargs[key] = value
#         print(kwargs)
#         q = EvecomData.objects.create(**kwargs)
#         print(q)
#         return q
#
#     def get_plano(self, method='get', **kwargs ):
#         """
#         will get the right plan
#         :param kwargs:
#         :return:
#         """
#         print('entrei no get_plano')
#         # if self.request.method == 'GET':
#         print('procurando por GET method')
#         plano = self.plano
#         return {'status':'OK','planoid':plano.id, 'method':'get', 'planoqueryset':plano}
#
#
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         if self.request.user.is_authenticated():
#             context['pagamento_template'] = "admin4/index.html"
#         else:
#             context['pagamento_template'] = "admin4/index_anonimo.html"
#
#         try:
#             print('procurando plano')
#             # q = get_object_or_404(PlanosdePagamento.objects.filter(id=self.kwargs['plano']))
#             ##plano = self.get_plano()
#             plano = self.plano
#             print('plano no context,get',plano)
#
#             context['result'] = plano##['planoqueryset']
#             context['plano'] = plano##['planoqueryset']
#             context['negocio'] = plano.entidade
#
#         except KeyError as e:
#             print(' error finding plano:',e)
#             return context
#         return context
#
#     def form_valid(self, form):
#         print('form valid')
#         numero = form.cleaned_data['numero_cartao'].replace(' ', '')
#         validade_ano = form.cleaned_data['expiracao_ano']
#         validade_mes = form.cleaned_data['expiracao_mes']
#         seguranca = form.cleaned_data['codigo_seguranca']
#         if self.cpf_is_sent:
#             cpf = self.request.GET.get('cpf')
#         else:
#             cpf = form.cleaned_data['CPF']
#         bandeira = form.cleaned_data['bandeira'].lower()
#         nome = form.cleaned_data['nome_cartao']
#         email = form.cleaned_data['Email']
#         #plano = [self.get_plano()['planoqueryset']]
#         plano = [self.plano]
#         tipo_escolhido = form.cleaned_data['tipo_escolhido']
#         parcelas = int(form.cleaned_data['parcelas'])
#         print('inicio')
#         if self.request.user.is_authenticated():
#             usuario = self.request.user.pessoa
#         else:
#             usuario = None
#         process = OrderProcess(plano[0].cielo_data)
#         print(process)
#         print('dados assinatura',plano[0].id, cpf, 'cielo', numero[-4:], validade_ano,
#                                          validade_mes,
#                                          nome, bandeira, email, usuario,
#                                          plano[0].tipo, 1)
#         final_result = process.cria_assinatura(plano[0], cpf, numero, validade_ano,
#                                          validade_mes, seguranca,
#                                          nome, bandeira, email, usuario=usuario,
#                                          tipo=tipo_escolhido,assinatura=True)
#         print('final result assinatura', final_result)
#         if final_result['status'] == 'OK':
#             valor_real = final_result['data']['transacao'].montante
#             evedata = self.create_evecom_data(self.request)
#             evedata.business = plano[0].entidade.email
#             evedata.amount = "{0:.2f}".format(valor_real)
#             evedata.email = email
#             evedata.item_name = plano[0].nome_plano
#             evedata.invoice = plano[0].uuid_pagamento
#             evedata.cpf = cpf
#             evedata.parcelas = parcelas
#             if plano[0].entidade.integracao_terceiro:
#                 evedata.tipo_integracao = 2 #assinatura
#                 evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
#             pedido = Pedido.objects.create()
#             pedido.plano_pagamento = plano[0]
#             pedido.evecom_item = evedata
#             pedido.nome = nome
#             evedata.comprovante_url = 'https://planopago.com.br' + str(
#                 reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
#             print('url comprovante', evedata.comprovante_url)
#             evedata.save()
#             pedido.save()
#
#             cielo_data = plano[0].cielo_data
#             self.request.session['result'] = final_result['data']
#             self.request.session['nome_plano'] = plano[0].nome_plano
#             self.request.session['cliente_nome'] = nome
#             self.request.session['cliente_cpf'] = cpf
#             self.request.session['cliente_email'] = email
#             if cielo_data.negocio is None:
#                 self.request.session['negocio_nome'] = plano[0].entidade.name
#                 if plano[0].entidade.mostra_cnpj:
#                     self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
#                 self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
#                 self.request.session['negocio_cidade'] = plano[0].entidade.cidade
#                 self.request.session['negocio_CEP'] = plano[0].entidade.CEP
#                 self.request.session['negocio_UF'] = plano[0].entidade.UF
#             else:
#                 self.request.session['negocio_nome'] = cielo_data.negocio.name
#                 if cielo_data.negocio.mostra_cnpj:
#                     self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
#                 self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
#                 self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
#                 self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
#                 self.request.session['negocio_UF'] = cielo_data.negocio.UF
#             self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
#             self.request.session['plano_valor'] = "{0:.2f}".format(valor_real)
#             if 'tipo' in self.request.GET:
#                 self.request.session['plano_tipo'] = self.request.GET['tipo']
#             else:
#                 self.request.session['plano_tipo'] = plano[0].tipo
#             #self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
#             self.request.session['pedido_num'] = pedido.num_pedido
#
#             m1 = MailTemplate.objects.filter(
#                 situacao="assinatura").last()
#             e1 = EmailProcessor()
#             e1.set_template(m1)
#             mailtemplatedict = {'total': "{0:.2f}".format(valor_real), 'nome': nome,
#                                 'nome_atividade': plano[0].nome_plano,
#                                 'evento_razaosocial': str(plano[0].entidade.name),
#                                 'evento_cnpj': str(plano[0].entidade.CNPJ),
#                                 'cpf': cpf}
#             if not plano[0].entidade.mostra_cnpj:
#                 mailtemplatedict['evento_cnpj'] = ''
#             e1.create_body(mailtemplatedict)
#             e1.create_email_object(email, m1)
#             evedata.paid = True
#             evedata.save()
#
#             self.request.session['imprime_etiqueta'] = 0
#
#             try:
#                 self.request.session['inscricao'] = evedata.invoice.split('_')[0]
#             except Exception as e:
#                 print('erro na etiqueta ', e)
#         return super().form_valid(form)

class ClientePagarTabAssinatura(generic.FormView):
    """
    class for payment only plan id is sent.

    """
    form_class = CartaoForm
    #template_name = "../templates/associado/pagamento_tabbed.html"
    success_url = "/cliente/resultadoassinatura/"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cpf_is_sent = None
        self.convenio = None

    @cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
    def dispatch(self, request, *args, **kwargs):
        self.plano = PlanosdePagamento.objects.get(uuid_pagamento=self.kwargs['plano'])
        return super().dispatch(request, *args, **kwargs)

    def render_to_response(self, context, **response_kwargs):
        context = super().get_context_data()
        result = {'cartao_ativado': True}
        ##planoid = self.kwargs['plano']
        ##plano = PlanosdePagamento.objects.get(id=planoid)
        plano = self.plano
        result['montante'] = plano.montante
        entidade = plano.entidade
        self.template_name = entidade.template_evecom.template_file
        if self.plano.cielo_data:
            self.convenio = self.plano.cielo_data
        else:
            self.convenio = CieloData.objects.get(nome=self.kwargs['convenio']).tipo_api
        result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
        result['logo_evento'] = entidade.template_evecom.logo_evento#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        result['nome_plano'] = plano.nome_plano
        result['parcelas'] = entidade.parcelas
        result['assinatura'] = plano.assinatura
        result['paymentprocessor'] = self.convenio
        result['formato_cpf'] = entidade.template_evecom.formato_cpf
        result['intervalo'] = plano.frequencia_intervalo
        result['trial_days'] = plano.assinatura_trial_days

        try:
            result['logo_evento'] = plano.logo_evento.url
        except ValueError:
            print('sem logo definido',entidade)
        context['result'] = result
        self.request.session['google_tag_enabled'] = plano.entidade.google_tag_enabled
        self.request.session['google_tag_head'] = plano.entidade.google_tag_head
        self.request.session['google_tag_body'] = plano.entidade.google_tag_body
        self.request.session['google_tag_version'] = plano.entidade.google_tag_version
        self.request.session['invoice'] = plano.uuid_pagamento
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"
        return super().render_to_response(context, **response_kwargs)

    def get_form(self, step=None, data=None, files=None):
        plano = self.plano
        entidade = plano.entidade
        form = super(ClientePagarTabAssinatura, self).get_form()
        if plano.debito_ativado is False:
            form.fields['tipo_escolhido'].widget.choices.pop(1)

        #TODO fazer teste pra checkbox
        if self.request.GET.get('cpf'):
            form.fields.pop('CPF')
            self.cpf_is_sent = True
        # if self.plano.entidade.integracao_terceiro:
        #     form.fields['Pagamento_terceiro'] = forms.BooleanField(label="Beneficiario é dono do cartão",initial=True, required=False)
        #     form.fields['nome_beneficiario'] = forms.CharField(label="Nome beneficiario", max_length=200, required=False)
        if self.plano.cielo_data:
            processor = self.plano.cielo_data
        else:
            processor = CieloData.objects.get(nome=self.kwargs['convenio'])
        print('convenio tipo', processor.tipo_api)

        # if processor.tipo_api == 'stone':
        #     print('eh stone')
        #     form.fields['Rua'] = forms.CharField()
        #     form.fields['Numero'] = forms.CharField()
        #     form.fields['Cep'] = forms.CharField()
        #     form.fields['Cidade'] = forms.CharField()
        #     form.fields['Estado'] = forms.CharField()
        #     pais = forms.CharField()
        #     pais.initial = 'Brasil'
        #     form.fields['Pais'] = pais
        #     print('form adicionado')


        #TODO fazer as opcoes de parcelas nos convenios (cielodata)
        if str(plano.cielo_data.nome) == 'mfm':
            form.fields['parcelas'].widget = forms.HiddenInput()
            return form

        montante = Decimal(plano.montante)
        par = []
        #TODO testar o pagamentotab com os parcelamentos
        if entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria')).exists():
            print('tem parcelamento categoria',self.request.GET.get('categoria'))
            parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria'))[0].parcelas
        else:
            if entidade.parcelas == 1 and plano.parcelas is  None:
                form.fields['parcelas'].widget = forms.HiddenInput()
                return form
            else:
                parcelas_num = entidade.parcelas
                print('tem parcelamento na categoria')
        if plano.parcelas:
            print('tem parcelas fixo', plano.parcelas)
            parcelas_num = plano.parcelas

        for i in range(1, parcelas_num+1):
            if i == 1 and plano.desconto_a_vista:
                par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(plano.valor_a_vista))))
            else:
                par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
        # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
        #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
        parcelas = par
        form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
        print(parcelas)
        return form

    def create_evecom_data(self, request):
        """
        from request get the data to create all evecom data
        :param request:
        :return:
        """
        kwargs = {}
        tmp = EvecomData._meta.get_fields()
        fields = []
        for i in tmp:
            fields.append(i.name)
        for key, value in request.GET.items():
            if key not in fields:
                print('pass no', key, value)
            else:
                print(' ok no', key, value)
                kwargs[key] = value
        print(kwargs)
        q = EvecomData.objects.create(**kwargs)
        print(q)
        return q

    def get_plano(self, method='get', **kwargs ):
        """
        will get the right plan
        :param kwargs:
        :return:
        """
        print('entrei no get_plano')
        # if self.request.method == 'GET':
        print('procurando por GET method')
        plano = self.plano
        return {'status':'OK','planoid':plano.id, 'method':'get', 'planoqueryset':plano}



    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"

        try:
            print('procurando plano')
            # q = get_object_or_404(PlanosdePagamento.objects.filter(id=self.kwargs['plano']))
            ##plano = self.get_plano()
            plano = self.plano
            print('plano no context,get',plano)

            context['result'] = plano##['planoqueryset']
            context['plano'] = plano##['planoqueryset']
            context['negocio'] = plano.entidade


        except KeyError as e:
            print(' error finding plano:',e)
            return context
        return context

    def form_valid(self, form):
        print('form valid')
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        if self.cpf_is_sent:
            cpf = self.request.GET.get('cpf')
        else:
            cpf = form.cleaned_data['CPF']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        if form.cleaned_data.get('nome_beneficiario','') is '':
            nome = form.cleaned_data['nome_cartao']
        else:
            nome = form.cleaned_data['nome_beneficiario']
        email = form.cleaned_data['Email']
        #plano = [self.get_plano()['planoqueryset']]
        plano = [self.plano]
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        parcelas = int(form.cleaned_data['parcelas'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(plano[0].cielo_data)
        print(process)
        if plano[0].cielo_data:
            tipo_processor = plano[0].cielo_data.tipo_api
        else:
            tipo_processor = CieloData.objects.get(nome=self.kwargs['convenio']).tipo_api
        endq= None
        if tipo_processor == 'stone':
            temp={}
            temp['street']= form.cleaned_data.get("Rua", None)#form.cleaned_data['Rua']
            temp['street_number']=form.cleaned_data.get('Numero', None)
            temp['zipcode']=form.cleaned_data.get('Cep',None)
            temp['city']=form.cleaned_data.get('Cidade',None)
            temp['state']=form.cleaned_data.get('Estado',None)
            temp['country']=form.cleaned_data.get('Pais', 'br')
            #temp['country'] = 'br'
            #endq = Endereco.objects.create(**temp)
            endq = None
        assinatura = plano[0].assinatura
        dias_iniciar_assinatura = plano[0].assinatura_trial_days
        intervalo_assinatura = 'Monthly'
        if plano[0].frequencia_intervalo == 'Y':
            intervalo_assinatura = 'Annual'
        print('na via dias iniciar assinatura', dias_iniciar_assinatura,'plano tem assinatura', assinatura)
        print('dados compra',plano[0].id, cpf, tipo_processor, numero[-4:], validade_ano,
                                         validade_mes,
                                         nome, bandeira, email, usuario,
                                         plano[0].tipo, 1)
        final_result = process.order_buy(plano[0].id, cpf, tipo_processor, numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario,
                                         tipo=tipo_escolhido, parcelas=parcelas, desconto_a_vista=self.plano.desconto_a_vista,
                                         assinatura=assinatura, trial=plano[0].trial,
                                         dias_pra_inciar_assinatura=dias_iniciar_assinatura,
                                         periodicidade_assinatura=intervalo_assinatura, endereco=endq)
        print('final result eh', final_result)
        valor_real = plano[0].montante
        evedata = self.create_evecom_data(self.request)
        evedata.business = plano[0].entidade.email
        evedata.amount = "{0:.2f}".format(valor_real)
        evedata.email = email
        evedata.item_name = plano[0].nome_plano
        if self.request.GET.get('code'):
            evedata.invoice = self.request.GET.get('code')
        elif self.request.GET.get('invoice'):
            evedata.invoice = self.request.GET.get('invoice')
        else:
            evedata.invoice = plano[0].uuid_pagamento
        evedata.cpf = cpf
        evedata.parcelas = parcelas
        evedata.email=email
        if plano[0].entidade.integracao_terceiro:
            evedata.tipo_integracao = 2
            evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
        if plano[0].entidade.integracao_marketing:
            evedata.integracao_marketing = plano[0].entidade.integracao_marketing
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano[0]
        pedido.evecom_item = evedata
        pedido.nome = nome
        evedata.comprovante_url = 'https://planopago.com.br' + str(
            reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
        print('url comprovante', evedata.comprovante_url)
        evedata.save()
        pedido.save()
        if final_result['status'] == 'error':
            try:
                self.request.session['returncode'] = final_result['rawdata'][0]['Code']
                return super().form_valid(form)
            except TypeError:
                self.request.session['returncode'] = -1
                return super().form_valid(form)
            except KeyError:
                self.request.session['returncode'] = -1
                return super().form_valid(form)

        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('erro no get debit,', e)

        cielo_data = plano[0].cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano[0].nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        self.request.session['parcelas'] = parcelas

        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano[0].entidade.name
            if plano[0].entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
            self.request.session['negocio_cidade'] = plano[0].entidade.cidade
            self.request.session['negocio_CEP'] = plano[0].entidade.CEP
            self.request.session['negocio_UF'] = plano[0].entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
        self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
        self.request.session['plano_valor'] = "{0:.2f}".format(valor_real)
        if 'tipo' in self.request.GET:
            self.request.session['plano_tipo'] = self.request.GET['tipo']
        else:
            self.request.session['plano_tipo'] = plano[0].tipo
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2 or final_result['rawdata'].get('Payment').get('Status') == 20 or final_result['rawdata'].get('Payment').get('Status') == 1 :
            m1 = MailTemplate.objects.filter(
                situacao="assinatura").last()
            e1 = EmailProcessor()
            e1.set_template(m1)
            if intervalo_assinatura == 'Monthly':
                intervalo = 'mes'
            else:
                intervalo = 'ano'
            mailtemplatedict = {'total': "{0:.2f}".format(valor_real), 'nome': nome,
                                'nome_atividade': plano[0].nome_plano,
                                'evento_razaosocial': str(plano[0].entidade.name),
                                'evento_cnpj': str(plano[0].entidade.CNPJ),
                                'intervalo': intervalo,
                                'cpf': cpf}
            if not plano[0].entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1)
            #sending the code back
            self.request.session['google_tag_enabled'] = plano[0].entidade.google_tag_enabled
            self.request.session['google_tag_head'] = plano[0].entidade.google_tag_head
            self.request.session['google_tag_body'] = plano[0].entidade.google_tag_body
            self.request.session['google_tag_version'] = plano[0].entidade.google_tag_version
            self.request.session['invoice'] = plano[0].uuid_pagamento
            self.request.session['intervalo'] = intervalo
            data_to_send = {'code':evedata.invoice,
                    'type': 'subscription_activated'}
            try:
                req = requests.post('https://braintv.com.br/ws/webhooks/planopago',
                                    data=data_to_send, timeout=3)
                if req.status_code is not 200:
                    raise TimeoutError
                evedata.sent = True
                self.request.session['sincronizado'] = 1
            except TimeoutError:
                print('Timeout no webhook rits invoice', evedata.invoice)
            except Exception as e:
                print('erro no webhook rits invoice', evedata.invoice,e)
            evedata.paid = True
            evedata.save()
            if plano[0].entidade.sendevecom_sync == True:
                c = EvecomProcess()
                if c.connect()['status'] == 'OK':
                    c.send_data_db(evedata)
                    c.disconnect()
                    evedata.sent = True
                    evedata.save()
                else:
                    print('nao conectado')
            if plano[0].entidade.imprime_etiqueta == True:
                self.request.session['imprime_etiqueta'] = 1
                self.request.session['url_etiqueta'] = plano[0].entidade.url_etiqueta
                try:
                    self.request.session['etiqueta'] = evedata.invoice.split('_')[0]
                except Exception as e:
                    print('erro na etiqueta ', e)
            else:
                self.request.session['imprime_etiqueta'] = 0

            try:
                self.request.session['inscricao'] = evedata.invoice.split('_')[0]
            except Exception as e:
                print('erro na etiqueta ', e)

            print(' hotel nome', evedata.nome_hotel)
            if evedata.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = evedata.nome_hotel
                self.request.session['check_in'] = evedata.check_in
                self.request.session['check_out'] = evedata.check_out
                self.request.session['categoria'] = evedata.categoria
                self.request.session['data_entrada'] = evedata.data_entrada
                self.request.session['data_saida'] = evedata.data_saida
                self.request.session['id_reserva'] = evedata.id_reserva
                self.request.session['tipo_apto'] = evedata.tipo_apto
                self.request.session['numero_acompanhantes'] = evedata.numero_acompanhantes
                evedata.save()
        return super().form_valid(form)


class CancelaAssinaturaporUuid(generic.TemplateView):
    """
    cancela a assinatura recebendo o uuid por code
    """
    template_name = "associado/customizado/cancelamento/cancelamentobraintv.html"

    def render_to_response(self, context, **response_kwargs):
        uiid = self.kwargs['plano']
        try:
            print('procurando assinatura no PP')
            invoice = get_object_or_404(EvecomData.objects.filter(invoice=uiid, paid=True))
        except MultipleObjectsReturned:
            print('tenho assinaturas duplicadas para cancelar, selecionando a primeira')
            invoice = EvecomData.objects.filter(invoice=uiid, paid=True).first()
            print('assinatura selecionada', invoice.id, invoice.invoice)
        try:
            q = invoice.pedido_set.all()[0].transacao
            print('cancelando assinatura querysete invoice', q, invoice)
            c = OrderProcess(q.cielo_data)
            try:
                resultado = c.order_cancel(q)
                print(resultado)
                if resultado['status'] == 'OK':
                    q.status_code = 2
                    q.status = 'Cancelada'
                    q.save()
                    print('sucesso cancelamentono PP assinatura ', resultado)
                else:
                    print('falha cancelamento no pp assinatura', resultado)
            except CieloRequestError as e:
                print('Erro no cancelamento', e, q.cielo_transactions.tid)
        except Exception as e:
            print('erro ao cancelar pp,e')

        #c = OrderProcess(self.kwargs['convenio'])
        headerrits = {'Accept': 'application/json'}
        data_to_send = {'code': invoice.invoice,
                        'type': 'subscription_canceled'}
        try:
            req = requests.post('https://braintv.com.br/ws/webhooks/planopago',
                                data=data_to_send, headers=headerrits, timeout=5)
            if req.status_code is not 200:
                print('nao foi possivel cancella assinatura', invoice.id, invoice.invoice, req.status_code)
                raise TimeoutError
            print('cancelada assinatura:id, invoiceid', invoice.id,invoice.invoice)
            self.request.session['sincronizado'] = 1
        except TimeoutError:
            print('Timeout no webhook rits invoice', invoice.invoice)
            self.request.session['sincronizado'] = 1
            #return redirect(self.request.META.get('HTTP_REFERER', 'https://braintv.com.br'))
        except Exception as e:
            print('erro no webhook rits invoice', invoice.invoice, e)
            self.request.session['sincronizado'] = 1
            #return redirect(self.request.META.get('HTTP_REFERER', 'https://braintv.com.br'))

        m1 = MailTemplate.objects.get(html__contains='cancelamentobraintv.html')
        e1 = EmailProcessor()
        e1.set_template(m1)
        mailtemplatedict = {}
        e1.create_body(mailtemplatedict)
        e1.create_email_object(invoice.email, m1)
        print('cancelada assinatura perfeita', invoice.id, invoice.invoice, req.status_code)
        return super().render_to_response(context, **response_kwargs)



###
###
### DOACOES
###
###

class ClientePagarTabMontanteFlexivel(generic.FormView):
    """
    class for payment only plan id is sent.

    """
    form_class = CartaoForm
    template_name = "../templates/associado/customizado/pagamento/doacao/doacao_tabbed.html"
    success_url = "/cliente/resultadopagamento/"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cpf_is_sent = None

    @cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
    def dispatch(self, request, *args, **kwargs):
        self.plano = PlanosdePagamento.objects.get(uuid_pagamento=self.kwargs['plano'])
        return super().dispatch(request, *args, **kwargs)

    def render_to_response(self, context, **response_kwargs):
        context = super().get_context_data()
        result = {'cartao_ativado': True}
        ##planoid = self.kwargs['plano']
        ##plano = PlanosdePagamento.objects.get(id=planoid)
        plano = self.plano
        result['montante'] = plano.montante
        entidade = plano.entidade
        result['mensagem_rodape'] = entidade.template_evecom.mensagem_rodape #'World Congress on Brain, Behavior and Emotions - Verified by Planopago'
        result['cor_evento'] = entidade.template_evecom.cor_evento#'green-jungle'
        result['logo_evento'] = entidade.template_evecom.logo_evento#'/static/media/logos/AF-Marca-brain-2017-6-estrelas.png'
        result['nome_plano'] =plano.nome_plano
        result['parcelas'] = entidade.parcelas
        result['formato_cpf'] = entidade.template_evecom.formato_cpf

        try:
            result['logo_evento'] = plano.logo_evento.url
        except ValueError:
            print('sem logo definido',entidade)
        context['result'] = result
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"
        return super().render_to_response(context, **response_kwargs)

    def get_form(self, step=None, data=None, files=None):
        plano = self.plano
        entidade = plano.entidade
        form = super(ClientePagarTabMontanteFlexivel, self).get_form()
        #TODO fazer teste pra checkbox
        form.fields['valor'] = forms.IntegerField()
        if self.request.GET.get('cpf'):
            form.fields.pop('CPF')
            self.cpf_is_sent = True
        #if self.plano.entidade.integracao_terceiro:
        #    form.fields['Pagamento_terceiro'] = forms.BooleanField(label="Beneficiario é dono do cartão",initial=True, required=False)
        #    form.fields['nome_beneficiario'] = forms.CharField(label="Nome beneficiario", max_length=200, required=False)
        #TODO se ta escondendo a parcela pra que calcular ela abaixo?

        #TODO fazer as opcoes de parcelas nos convenios (cielodata)
        if str(plano.cielo_data.nome) == 'mfm':
            form.fields['parcelas'].widget = forms.HiddenInput()
            return form

        montante = Decimal(plano.montante)
        par = []
        #TODO testar o pagamentotab com os parcelamentos
        if entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria')).exists():
            print('tem parcelamento categoria',self.request.GET.get('categoria'))
            parcelas_num = entidade.planoparcelamento_set.filter(codigo=self.request.GET.get('categoria'))[0].parcelas
        else:
            if entidade.parcelas == 1 and plano.parcelas is  None:
                form.fields['parcelas'].widget = forms.HiddenInput()
                return form
            else:
                parcelas_num = entidade.parcelas
                print('tem parcelamento na categoria')
        if plano.parcelas:
            print('tem parcelas fixo', plano.parcelas)
            parcelas_num = plano.parcelas

        for i in range(1, parcelas_num+1):
            if i == 1 and plano.desconto_a_vista:
                par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(plano.valor_a_vista))))
            else:
                par.append((i, '{0}x de R${1:.2f}'.format(i, Decimal(montante)/Decimal(i))))
        # par={1:(1,'Vista:{0}'.format(montante)),2:(2,'2x de {0}'.format(montante/2)),
        #      3:(3,'3x de {0}'.format(montante/3)), 4:('4','4x de {0}'.format(montante/4))}
        parcelas = par
        form.fields['parcelas'] = forms.ChoiceField(choices=parcelas)
        print(parcelas)
        return form

    def create_evecom_data(self, request):
        """
        from request get the data to create all evecom data
        :param request:
        :return:
        """
        kwargs = {}
        tmp = EvecomData._meta.get_fields()
        fields = []
        for i in tmp:
            fields.append(i.name)
        for key, value in request.GET.items():
            if key not in fields:
                print('pass no', key, value)
            else:
                print(' ok no', key, value)
                kwargs[key] = value
        print(kwargs)
        q = EvecomData.objects.create(**kwargs)
        print(q)
        return q

    def get_plano(self, method='get', **kwargs ):
        """
        will get the right plan
        :param kwargs:
        :return:
        """
        print('entrei no get_plano')
        # if self.request.method == 'GET':
        print('procurando por GET method')
        plano = self.plano
        return {'status':'OK','planoid':plano.id, 'method':'get', 'planoqueryset':plano}



    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated():
            context['pagamento_template'] = "admin4/index.html"
        else:
            context['pagamento_template'] = "admin4/index_anonimo.html"

        try:
            print('procurando plano')
            # q = get_object_or_404(PlanosdePagamento.objects.filter(id=self.kwargs['plano']))
            ##plano = self.get_plano()
            plano = self.plano
            print('plano no context,get',plano)

            context['result'] = plano##['planoqueryset']
            context['plano'] = plano##['planoqueryset']
            context['negocio'] = plano.entidade

        except KeyError as e:
            print(' error finding plano:',e)
            return context
        return context

    def form_valid(self, form):
        print('form valid')
        numero = form.cleaned_data['numero_cartao'].replace(' ', '')
        validade_ano = form.cleaned_data['expiracao_ano']
        validade_mes = form.cleaned_data['expiracao_mes']
        seguranca = form.cleaned_data['codigo_seguranca']
        if self.cpf_is_sent:
            cpf = self.request.GET.get('cpf')
        else:
            cpf = form.cleaned_data['CPF']
        nome = form.cleaned_data['nome_cartao']
        bandeira = form.cleaned_data['bandeira'].lower()
        if form.cleaned_data.get('nome_beneficiario') is None:
            nome = form.cleaned_data['nome_cartao']
        else:
            nome = form.cleaned_data['nome_beneficiario']
        email = form.cleaned_data['Email']
        #plano = [self.get_plano()['planoqueryset']]
        plano = [self.plano]
        #plano[0].montante = form.cleaned_data['valor']
        #plano[0].save()
        tipo_escolhido = form.cleaned_data['tipo_escolhido']
        parcelas = int(form.cleaned_data['parcelas'])
        print('inicio')
        if self.request.user.is_authenticated():
            usuario = self.request.user.pessoa
        else:
            usuario = None
        process = OrderProcess(plano[0].cielo_data)
        print(process)
        if plano[0].valor_flexivel:
            valor = str(form.cleaned_data['valor'])+'00'
            # if '.' in str(valort):
            #     valor = int(str(valort).replace('.', ''))
            # if ',' in str(valort):
            #     valor = int(str(valort).replace('.', ''))

        else:
            valor = None
        print('dados compra',plano[0].id, cpf, 'cielo', numero[-4:], validade_ano,
                                         validade_mes,
                                         nome, bandeira, email, usuario,
                                         plano[0].tipo, 1)
        final_result = process.order_buy(plano[0].id, cpf, 'cielo', numero, validade_ano,
                                         validade_mes, seguranca,
                                         nome, bandeira, email, usuario=usuario,
                                         tipo=tipo_escolhido, parcelas=parcelas,
                                         desconto_a_vista=self.plano.desconto_a_vista, valorflexivel=valor)
        print('final result eh', final_result)
        valor_real = Decimal(final_result.get('rawdata').get('Payment').get('Amount')/100)
        evedata = self.create_evecom_data(self.request)
        evedata.business = plano[0].entidade.email
        evedata.amount = "{0:.2f}".format(valor_real)
        evedata.email = email
        evedata.item_name = plano[0].nome_plano
        evedata.invoice = plano[0].uuid_pagamento
        evedata.cpf = cpf
        evedata.parcelas = parcelas
        if plano[0].entidade.integracao_terceiro:
            evedata.tipo_integracao = 1
            evedata.integracao_terceiro = plano[0].entidade.integracao_terceiro
        if plano[0].entidade.integracao_marketing:
            evedata.integracao_marketing = plano[0].entidade.integracao_marketing
        pedido = final_result['pedido']
        pedido.plano_pagamento = plano[0]
        pedido.evecom_item = evedata
        pedido.nome = nome
        evedata.comprovante_url = 'https://planopago.com.br' + str(
            reverse_lazy('comprovante', kwargs={'pedido': pedido.num_pedido}))
        print('url comprovante', evedata.comprovante_url)
        evedata.save()
        pedido.save()
        try:
            if final_result.get("rawdata").get("Payment").get("Type") == "DebitCard":
                print('redirecionando debito')
                return redirect(to=final_result.get("rawdata").get("Payment").get("AuthenticationUrl"))
        except AttributeError as e:
            print('erro no get debit,', e)

        cielo_data = plano[0].cielo_data
        self.request.session['result'] = final_result['data']
        self.request.session['nome_plano'] = plano[0].nome_plano
        self.request.session['cliente_nome'] = nome
        self.request.session['cliente_cpf'] = cpf
        self.request.session['cliente_email'] = email
        if cielo_data.negocio is None:
            self.request.session['negocio_nome'] = plano[0].entidade.name
            if plano[0].entidade.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = plano[0].entidade.CNPJ
            self.request.session['negocio_logradouro'] = plano[0].entidade.logradouro
            self.request.session['negocio_cidade'] = plano[0].entidade.cidade
            self.request.session['negocio_CEP'] = plano[0].entidade.CEP
            self.request.session['negocio_UF'] = plano[0].entidade.UF
        else:
            self.request.session['negocio_nome'] = cielo_data.negocio.name
            if cielo_data.negocio.mostra_cnpj:
                self.request.session['negocio_CNPJ'] = cielo_data.negocio.CNPJ
            self.request.session['negocio_logradouro'] = cielo_data.negocio.logradouro
            self.request.session['negocio_cidade'] = cielo_data.negocio.cidade
            self.request.session['negocio_CEP'] = cielo_data.negocio.CEP
            self.request.session['negocio_UF'] = cielo_data.negocio.UF
        self.request.session['plano_descricao_curta'] = plano[0].descricao_curta
        self.request.session['plano_valor'] = "{0:.2f}".format(valor_real)
        if 'tipo' in self.request.GET:
            self.request.session['plano_tipo'] = self.request.GET['tipo']
        else:
            self.request.session['plano_tipo'] = plano[0].tipo
        self.request.session['returncode'] = final_result['rawdata'].get('Payment').get('Status')
        self.request.session['pedido_num'] = str(final_result['pedido'].num_pedido)

        if final_result['rawdata'].get('Payment').get('Status') == 2:
            m1 = MailTemplate.objects.filter(
                situacao="pagamento").first()
            e1 = EmailProcessor()
            e1.set_template(m1)
            mailtemplatedict = {'total': "{0:.2f}".format(valor_real), 'nome': nome,
                                'nome_atividade': plano[0].nome_plano,
                                'evento_razaosocial': str(plano[0].entidade.name),
                                'evento_cnpj': str(plano[0].entidade.CNPJ),
                                'cpf': cpf}
            if not plano[0].entidade.mostra_cnpj:
                mailtemplatedict['evento_cnpj'] = ''
            e1.create_body(mailtemplatedict)
            e1.create_email_object(email, m1)
            evedata.paid = True
            evedata.save()
            if plano[0].entidade.sendevecom_sync == True:
                c = EvecomProcess()
                if c.connect()['status'] == 'OK':
                    c.send_data_db(evedata)
                    c.disconnect()
                    evedata.sent = True
                    evedata.save()
                else:
                    print('nao conectado')
            if plano[0].entidade.imprime_etiqueta == True:
                self.request.session['imprime_etiqueta'] = 1
                self.request.session['url_etiqueta'] = plano[0].entidade.url_etiqueta
                try:
                    self.request.session['etiqueta'] = evedata.invoice.split('_')[0]
                except Exception as e:
                    print('erro na etiqueta ', e)
            else:
                self.request.session['imprime_etiqueta'] = 0

            try:
                self.request.session['inscricao'] = evedata.invoice.split('_')[0]
            except Exception as e:
                print('erro na etiqueta ', e)

            print(' hotel nome', evedata.nome_hotel)
            if evedata.nome_hotel is not None:
                print('eh hospedagem')
                self.request.session['tipo_hotel'] = True
                self.request.session['nome_hotel'] = evedata.nome_hotel
                self.request.session['check_in'] = evedata.check_in
                self.request.session['check_out'] = evedata.check_out
                self.request.session['categoria'] = evedata.categoria
                self.request.session['data_entrada'] = evedata.data_entrada
                self.request.session['data_saida'] = evedata.data_saida
                self.request.session['id_reserva'] = evedata.id_reserva
                self.request.session['tipo_apto'] = evedata.tipo_apto
                self.request.session['numero_acompanhantes'] = evedata.numero_acompanhantes
                evedata.save()
        return super().form_valid(form)


def gerar_pix(request):
    """
    View para gerar PIX usando a API da Cielo
    """
    if request.method == 'POST':
        try:
            valor = request.POST.get('valor')
            if not valor:
                return HttpResponse(json.dumps({
                    'success': False,
                    'message': 'Valor não informado'
                }), content_type='application/json')

            # Aqui você implementaria a integração com a API da Cielo para PIX
            # Por enquanto, vou simular a resposta

            # Exemplo de integração com Cielo PIX (você precisará implementar)
            # cielo_pix = CieloPixAPI()
            # response = cielo_pix.create_pix_payment(valor)

            # Simulação da resposta da API
            import uuid
            import time

            pix_key = "<EMAIL>"  # Chave PIX da empresa
            pix_code = f"00020126580014BR.GOV.BCB.PIX0136{uuid.uuid4()}5204000053039865802BR5925NOME DA EMPRESA6009SAO PAULO62070503***6304"

            # Em produção, você salvaria os dados do PIX no banco de dados
            # pix_payment = PixPayment.objects.create(
            #     valor=valor,
            #     pix_code=pix_code,
            #     pix_key=pix_key,
            #     status='pending'
            # )

            return HttpResponse(json.dumps({
                'success': True,
                'pix_code': pix_code,
                'pix_key': pix_key,
                'valor': valor,
                'expires_in': 1800  # 30 minutos
            }), content_type='application/json')

        except Exception as e:
            return HttpResponse(json.dumps({
                'success': False,
                'message': str(e)
            }), content_type='application/json')

    return HttpResponse(json.dumps({
        'success': False,
        'message': 'Método não permitido'
    }), content_type='application/json')
