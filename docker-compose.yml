version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=associado.settings
    depends_on:
      - db
    command: python manage.py runserver 0.0.0.0:8000

  db:
    image: mysql:5.7
    environment:
      MYSQL_DATABASE: associado_db
      MYSQL_USER: associado_user
      MYSQL_PASSWORD: associado_pass
      MYSQL_ROOT_PASSWORD: root_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
