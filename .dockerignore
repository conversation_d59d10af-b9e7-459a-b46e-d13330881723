# Ambientes virtuais
ppenv/
ppenv3/
ppenv_new/
ppenv_clean/
venv/
env/
.venv/

# Arquivos Python
__pycache__/
*.py[cod]
*$py.class
*.so
*.egg-info/
dist/
build/

# Arquivos de desenvolvimento
.git/
.gitignore
README.md
*.md
node_modules/

# Arquivos de IDE
.vscode/
.idea/
*.swp
*.swo
*.sublime-*

# Arquivos de sistema
.DS_Store
Thumbs.db
*.tmp
*.temp

# Logs e cache
*.log
.cache/
.pytest_cache/

# Arquivos temporários
fix_foreign_keys.py
Dockerfile.original
Dockerfile.alternative
Dockerfile.robust
docker-compose.simple.yml

# Arquivos de banco de dados local
*.sqlite3
*.db

# Arquivos de media/uploads
media/
uploads/

# Arquivos de configuração local
.env
.env.local
local_settings.py
