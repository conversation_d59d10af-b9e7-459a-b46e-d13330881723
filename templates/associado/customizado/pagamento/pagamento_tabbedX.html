{#{% extends pagamento_template %}#}
{% load i18n %}
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>Plano Plago</title>
		<!--BOOTSTRAP-->
		<link rel="stylesheet" href="{{ STATIC_URL }}associado/customizado/pagamento/braintv/css/bootstrap.min.css">
		<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}associado/customizado/pagamento/braintv/css/style.css">
		<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}associado/customizado/pagamento/braintv/css/responsive.css">
        {% if request.session.google_tag_enabled %}

    {{ request.session.google_tag_head|safe }}
    {% endif %}


	</head>
    <script src="{{ STATIC_URL }}card.js" xmlns="http://www.w3.org/1999/html"></script>

	<body>
{% if request.session.google_tag_enabled %}
    {{ request.session.google_tag_body|safe }}
    {% endif %}
    <section class="header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 {% if result.cor_evento %} {{ result.cor_evento }} {% else %}red {% endif %}">
                    <h1>PlanoPago : {{ result.nome_plano }}</h1>
                </div>
             </div>
        </div>
    </section>
    <section class="logo">
        <div class="container">
            <div class="row">
					<div class="col-lg-7">
                        <div class="logo_left">
                            <img src={{ STATIC_URL }}associado/customizado/pagamento/braintv/img/plano-pago-marca.png>
                        </div>
                    </div>
                <div class="col-lg-5">
						<div class="logo_right">
							<img src="https://planopago.com.br/static/media/logos/BrainTV-marca-horizontal.png" alt="">
						</div>
                </div>
            </div>
        </div>
    </section>
    <section class="logo-mid">
			<div class="container">
				<div class="row">
					<div class="col-lg-12 color-red">
						<h1>Dados de Pagamento</h1>
					</div>
				</div>
			</div>
    </section>


<section class="md">
<img src="{{ STATIC_URL}}media/ajax-loader.gif" id="gif" style="display: block; margin: 0 auto; width: 200px; visibility: hidden;">

			<div class="container">
				<div class="row">
					<div class="col-lg-7">
                        <form method="POST" action="" autocomplete="off" id="pagamento_form" class="form">
                        {% csrf_token %}
                            {{ form.non_field_errors }}

                            {{ form.source.errors }}
                                {{ form.source }}
                            <div class="row">
								<div class="col-xl-6">
									<label for="">Tipo de Cartão:</label>
								</div>
								<div class="col-xl-6">
									{{form.tipo_escolhido}}
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6">
									<label for="">Nome no Cartão:</label>
								</div>
								<div class="col-xl-6">
                                    {{form.nome_cartao}}
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6">
									<label for="">Número do Cartão:</label>
								</div>
								<div class="col-xl-6">
									{{form.numero_cartao}}
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6">
									<label for="">Validade (mm/aa)</label>
								</div>
								<div class="col-xl-3">
									{{ form.expiracao_mes }}
								</div>
								<div class="col-xl-3">
									{{ form.expiracao_ano }}
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6">
									<label for="">Bandeira:</label>
								</div>
								<div class="col-xl-6">
									{{form.bandeira}}
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6">
									<label for="">Código de segurança:</label>
								</div>
								<div class="col-xl-6">
									{{ form.codigo_seguranca }}
								</div>
							</div>
                            {% if  form.parcelas.is_hidden  %}
                                    {{form.parcelas}}
                            {% else %}
							<div class="row">
								<div class="col-xl-6">
									{{form.parcelas.label_tag}}
								</div>
								<div class="col-xl-6">
									{{form.parcelas}}
								</div>
							</div>
                            {% endif %}
							<div class="row">
								<div class="col-xl-6">
									<label for="">CPF:</label>
								</div>
								<div class="col-xl-6">
									{{ form.CPF }}
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6">
									<label for="">Email:</label>
								</div>
								<div class="col-xl-6">
									{{form.Email}}
								</div>
							</div>

{#                        {% if result.paymentprocessor == "stone" %}#}
{#                            <div class="row">#}
{#								<div class="col-xl-6">#}
{#									<label for="">Rua:</label>#}
{#								</div>#}
{#								<div class="col-xl-6">#}
{#									{{form.Rua}}#}
{#								</div>#}
{#							</div>#}
{#                            <div class="row">#}
{#								<div class="col-xl-6">#}
{#									<label for="">Numero:</label>#}
{#								</div>#}
{#								<div class="col-xl-6">#}
{#									{{form.Numero}}#}
{#								</div>#}
{#							</div>#}
{#                            <div class="row">#}
{#								<div class="col-xl-6">#}
{#									<label for="">Cep:</label>#}
{#								</div>#}
{#								<div class="col-xl-6">#}
{#									{{form.Cep}}#}
{#								</div>#}
{#							</div>#}
{#                            <div class="row">#}
{#								<div class="col-xl-6">#}
{#									<label for="">Cidade:</label>#}
{#								</div>#}
{#								<div class="col-xl-6">#}
{#									{{form.Cidade}}#}
{#								</div>#}
{#							</div>#}
{#                            <div class="row">#}
{#								<div class="col-xl-6">#}
{#									<label for="">Estado:</label>#}
{#								</div>#}
{#								<div class="col-xl-6">#}
{#									{{form.Estado}}#}
{#								</div>#}
{#                            </div>#}
{#                            <div class="row">#}
{#								<div class="col-xl-6">#}
{#									<label for="">Pais:</label>#}
{#								</div>#}
{#								<div class="col-xl-6">#}
{#									{{form.Pais}}#}
{#								</div>#}
{#                            </div>#}
{##}
{#                    {% endif %}#}


                        </div>
                    <div class="col-lg-5">
							 <div class='card-wrapper '></div>
						<div class="right_mid ">
							<h4>SUA COMPRA</h4>
                        <h3 class="text-center">Teste gratuito de {{ result.trial_days }} dias.</h3>
							<p class="text-center">Após o término  desse período será cobrado o valor da assinatura  {% if result.intervalo == "Y" %}
                                anual:
                                {% else %}
                                mensal:
                            {% endif %}</p>
								<h3 class="font-weight-bold text-center">Valor R${{ result.montante }}/{% if result.intervalo == "Y"  %}
                                    ano
                                    {% else %}
                                    mês
                                    {% endif %}
                                </h3>
						</div>
                        <input type="submit" class="right_btn" value="Assinar" title="Assinar">
                        						</form>
					</div>
                </div>
            </div>
</section>
<footer>
			<div class="container">
				<div class="row">
					<div class="col-xl-6">
						<p>BRAIN TV É UM PRODUTO DO INI - INSTITUTO DE CIÊNCIAS INTEGRADAS</p>
					</div>
					<div class="col-xl-6 text-right">
						<img src="{{ STATIC_URL }}associado/customizado/pagamento/braintv/img/CCM-Group.png" alt="">
					</div>
				</div>
			</div>
		</footer>

      <script src="/static/assets/global/plugins/jquery-1.12.4.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/js.cookie.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/uniform/jquery.uniform.min.js" type="text/javascript"></script>
            <script src="/static/assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>


<script>
    var card = new Card({
    // a selector or DOM element for the form where users will
    // be entering their information
    form: 'form', // *required*
    // a selector or DOM element for the container
    // where you want the card to appear
    container: '.card-wrapper', // *required*
//        nameInput: 'input[name="first-name"], input[name="last-name"]'

    formSelectors: {
        numberInput: 'input#id_numero_cartao', // optional — default input[name="number"]
        //expiryInput: ['input#expiracao_ano' , 'input#expiracao_mes'], // optional — default input[name="expiry"]
        cvcInput: 'input#id_codigo_seguranca', // optional — default input[name="cvc"]
        nameInput: 'input#id_nome_cartao' // optional - defaults input[name="name"]
    },

    //width: 200, // optional — default 350px
    formatting: true, // optional - default true

    // Strings for translation - optional
    messages: {
        validDate: 'valid\ndate', // optional - default 'valid\nthru'
        monthYear: 'mm/yyyy', // optional - default 'month/year'
    },

    // Default placeholders for rendered fields - optional
    placeholders: {
        number: '•••• •••• •••• ••••',
        name: 'NOME NO CARTAO',
        expiry: 'MM/AAAA',
        cvc: '•••'
    },

    // if true, will log helpful messages for setting up Card
    debug: true // optional - default false
});
    </script>






</body>
    <script src="/static/assets/global/plugins/jquery-1.12.4.min.js" type="text/javascript"></script>
<script src="{{ STATIC_URL }}jquery.maskedinput.js"></script>
 {% block quicksidebar %} {% endblock %}
<script type="application/javascript">
jQuery(document).ready(function() {
$('#id_nome_beneficiario').hide();
$(':submit',this).attr('disabled',false);
$('.right_btn').attr('disabled',false);
$('label[for=id_nome_beneficiario], input#id_nome_beneficiario').hide()
    $('#id_Pagamento_terceiro').click(function(){

        if ($('#id_nome_beneficiario').is(":visible"))
            {
                $('#id_nome_beneficiario').hide()
                $('label[for=id_nome_beneficiario], input#id_nome_beneficiario').hide()
            }
        else {

            $('#id_CPF_Beneficiario').show();
            $('label[for=id_nome_beneficiario], input#id_nome_beneficiario').show()
        }
//time for show
});



    {% if result.formato_cpf %}
                $("#id_CPF").mask("999.999.999-99");
                $('#tab_1_1_2 >form > p > input#id_CPF').mask("999.999.999-99");
    {% endif %}
                $('#pagamento_form').submit(function() {
                    $('#gif').css('visibility', 'visible');
                    $('#gif').css('position', 'absolute');
                    $('#gif').css('top', '50%');
                    $('#gif').css('left', '50%');
                    $('#gif').css('margin', '-50px 0px 0px -50px');
                    $(':submit',this).attr('disabled',true);
                    $('.right_btn').attr('disabled',true);

                });

        });
                </script>


</html>