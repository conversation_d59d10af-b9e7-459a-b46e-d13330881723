
<!DOCTYPE html>

<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" class="no-js">
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
<meta charset="utf-8"/>
<title>CCM | Painel de Controle</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport"/>
<meta content="Painel de Controle" name="description"/>
<meta content="Henrique Cano" name="author"/>
<!-- BEGIN GLOBAL MANDATORY STYLES -->
<link href="https://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/plugins/uniform/css/uniform.default.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css"/>
<!-- END GLOBAL MANDATORY STYLES -->
<!-- BEGIN PAGE LEVEL PLUGIN STYLES -->
<link rel="stylesheet" type="text/css" href="{{STATIC_URL}}assets/global/plugins/bootstrap-datepicker/css/datepicker.css"/>
<link rel="stylesheet" type="text/css" href="{{STATIC_URL}}assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css"/>
    <link rel="stylesheet" type="text/css" href="{{STATIC_URL}}assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css"/>
<link href="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/daterangepicker-bs3.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/plugins/fullcalendar/fullcalendar.min.css" rel="stylesheet" type="text/css"/>
{#<link href="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/jqvmap.css" rel="stylesheet" type="text/css"/>#}
<!-- END PAGE LEVEL PLUGIN STYLES -->
<!-- BEGIN PAGE STYLES -->
<link href="{{STATIC_URL}}assets/admin/pages/css/tasks.css" rel="stylesheet" type="text/css"/>
<!-- END PAGE STYLES -->
<!-- BEGIN THEME STYLES -->
<link href="{{STATIC_URL}}assets/global/css/components.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/global/css/plugins.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/admin/layout2/css/layout.css" rel="stylesheet" type="text/css"/>
<link href="{{STATIC_URL}}assets/admin/layout2/css/themes/grey.css" rel="stylesheet" type="text/css" id="style_color"/>
<link href="{{STATIC_URL}}assets/admin/layout2/css/custom.css" rel="stylesheet" type="text/css"/>
<!-- END THEME STYLES -->
<link rel="shortcut icon" href="favicon.ico"/>
        {% block head %}
    {% endblock %}
</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<!-- DOC: Apply "page-header-fixed-mobile" and "page-footer-fixed-mobile" class to body element to force fixed header or footer in mobile devices -->
<!-- DOC: Apply "page-sidebar-closed" class to the body and "page-sidebar-menu-closed" class to the sidebar menu element to hide the sidebar by default -->
<!-- DOC: Apply "page-sidebar-hide" class to the body to make the sidebar completely hidden on toggle -->
<!-- DOC: Apply "page-sidebar-closed-hide-logo" class to the body element to make the logo hidden on sidebar toggle -->
<!-- DOC: Apply "page-sidebar-hide" class to body element to completely hide the sidebar on sidebar toggle -->
<!-- DOC: Apply "page-sidebar-fixed" class to have fixed sidebar -->
<!-- DOC: Apply "page-footer-fixed" class to the body element to have fixed footer -->
<!-- DOC: Apply "page-sidebar-reversed" class to put the sidebar on the right side -->
<!-- DOC: Apply "page-full-width" class to the body element to have full width page without the sidebar menu -->
<body class="page-boxed page-header-fixed page-sidebar-closed-hide-logo page-container-bg-solid page-sidebar-closed-hide-logo">
<!-- BEGIN HEADER -->
<div class="page-header navbar navbar-fixed-top">
	<!-- BEGIN HEADER INNER -->
	<div class="page-header-inner container">
		<!-- BEGIN LOGO -->
		<div class="page-logo">
{#			<a href="index.html">#}
{#                <img src="{{ STATIC_URL }}/assets/pages/img/PlanoPago.png" alt="logo" class="logo-default">#}
{#			</a>#}
			<div class="menu-toggler sidebar-toggler">
				<!-- DOC: Remove the above "hide" to enable the sidebar toggler button on header -->
			</div>
		</div>
		<!-- END LOGO -->
		<!-- BEGIN RESPONSIVE MENU TOGGLER -->
		<a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse">
		</a>
		<!-- END RESPONSIVE MENU TOGGLER -->
		<!-- BEGIN PAGE ACTIONS -->
		<!-- DOC: Remove "hide" class to enable the page header actions -->

		<!-- END PAGE ACTIONS -->
		<!-- BEGIN PAGE TOP -->

{#			<!-- BEGIN HEADER SEARCH BOX -->#}
{#			<!-- DOC: Apply "search-form-expanded" right after the "search-form" class to have half expanded search box -->#}
{#			<form class="search-form search-form-expanded" action="extra_search.html" method="GET">#}
{#				<div class="input-group">#}
{#					<input type="text" class="form-control" placeholder="Search..." name="query">#}
{#					<span class="input-group-btn">#}
{#					<a href="javascript:;" class="btn submit"><i class="icon-magnifier"></i></a>#}
{#					</span>#}
{#				</div>#}
{#			</form>#}
{#			<!-- END HEADER SEARCH BOX -->#}
			<!-- BEGIN TOP NAVIGATION MENU -->
			<div class="top-menu">
				<ul class="nav navbar-nav pull-right">
					<!-- BEGIN NOTIFICATION DROPDOWN -->

					<!-- END INBOX DROPDOWN -->
					<!-- BEGIN TODO DROPDOWN -->

					<!-- END TODO DROPDOWN -->
					<!-- BEGIN QUICK SIDEBAR TOGGLER -->
					<li class="dropdown dropdown-quick-sidebar-toggler hide">
						<a href="javascript:;" class="dropdown-toggle">
						<i class="icon-logout"></i>
						</a>
					</li>
					<!-- END QUICK SIDEBAR TOGGLER -->
					<!-- BEGIN USER LOGIN DROPDOWN -->

					<!-- END USER LOGIN DROPDOWN -->
				</ul>

			</div>
  <h1 class="font-green bold text-center"> PlanoPago</h1>
			<!-- END TOP NAVIGATION MENU -->

		</div>

		<!-- END PAGE TOP -->
	</div>
	<!-- END HEADER INNER -->
</div>
<!-- END HEADER -->
<div class="clearfix">
</div>
<!-- BEGIN CONTAINER -->
<div class="container">
	<div class="page-container">
		<!-- BEGIN SIDEBAR -->
{#		<div class="page-sidebar-wrapper">#}
{#			<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->#}
{#			<!-- DOC: Change data-auto-speed="200" to adjust the sub menu slide up/down speed -->#}
{#			<div class="page-sidebar navbar-collapse collapse">#}
{#				<!-- BEGIN SIDEBAR MENU -->#}
{#				<ul class="page-sidebar-menu page-sidebar-menu-hover-submenu " data-auto-scroll="true" data-slide-speed="200">#}
{#					<li class="start active ">#}
{#						<a href="{% url "indexassociados" %}">#}
{#						<i class="icon-home"></i>#}
{#						<span class="title">Inicio</span>#}
{#						<span class="selected"></span>#}
{#						</a>#}
{#					</li>#}
{##}
{#                    <li >#}
{#                        {% if user.is_authenticated %}#}
{#						<a href="{% url "editacliente" user.pessoa.id %}">#}
{#						<i class="icon-user"></i>#}
{#						<span class="title">Meus Dados</span>#}
{#                                {% endif %}#}
{#						<span class="selected"></span>#}
{#						</a>#}
{##}
{#					</li>#}
{#                <li >#}
{#                        {% if user.is_authenticated %}#}
{#						<a href="{% url "clienteplanospagamentos" %}">#}
{#						<i class="fa-money"></i>#}
{#						<span class="title">Planos</span>#}
{#                            {% else %}#}
{##}
{#                                {% endif %}#}
{#						<span class="selected"></span>#}
{#						</a>#}
{##}
{#					</li>#}
{#                <li >#}
{#                        {% if user.is_authenticated %}#}
{#						<a href="{% url "extratoassociado" %}">#}
{#						<i class="fa fa-money"></i>#}
{#						<span class="title">Extrato</span>#}
{#                            {% else %}#}
{##}
{#                                {% endif %}#}
{#						<span class="selected"></span>#}
{#						</a>#}
{##}
{#					</li>#}
{#				</ul>#}
{#				<!-- END SIDEBAR MENU -->#}
{#			</div>#}
		</div>
		<!-- END SIDEBAR -->
		<!-- BEGIN CONTENT -->
		<div class="page-content-wrapper">
            {% block content %}


            {% endblock %}
		<!-- END CONTENT -->
		<!-- BEGIN QUICK SIDEBAR -->
		<!--Cooming Soon...-->
		<!-- END QUICK SIDEBAR -->
	</div>
	<!-- END CONTAINER -->
	<!-- BEGIN FOOTER -->
{% include "footer.html" %}
	<!-- END FOOTER -->
</div>
<!-- BEGIN JAVASCRIPTS(Load javascripts at bottom, this will reduce page load time) -->
<!-- BEGIN CORE PLUGINS -->
<!--[if lt IE 9]>
<script src="{{STATIC_URL}}assets/global/plugins/respond.min.js"></script>
<script src="{{STATIC_URL}}assets/global/plugins/excanvas.min.js"></script>
<![endif]-->
<script src="{{STATIC_URL}}assets/global/plugins/jquery.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/jquery-migrate.min.js" type="text/javascript"></script>
<!-- IMPORTANT! Load jquery-ui-1.10.3.custom.min.js before bootstrap.min.js to fix bootstrap tooltip conflict with jquery ui tooltip -->
<script src="{{STATIC_URL}}assets/global/plugins/jquery-ui/jquery-ui-1.10.3.custom.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/jquery.cokie.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/uniform/jquery.uniform.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
<!-- END CORE PLUGINS -->
<!-- BEGIN PAGE LEVEL PLUGINS -->
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/jquery.vmap.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/maps/jquery.vmap.russia.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/maps/jquery.vmap.world.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/maps/jquery.vmap.europe.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/maps/jquery.vmap.germany.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/maps/jquery.vmap.usa.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jqvmap/jqvmap/data/jquery.vmap.sampledata.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/flot/jquery.flot.min.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/flot/jquery.flot.resize.min.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/flot/jquery.flot.categories.min.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jquery.pulsate.min.js" type="text/javascript"></script>#}
<script src="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/moment.min.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/daterangepicker.js" type="text/javascript"></script>
<!-- IMPORTANT! fullcalendar depends on jquery-ui-1.10.3.custom.min.js for drag & drop support -->
{#<script src="{{STATIC_URL}}assets/global/plugins/fullcalendar/fullcalendar.min.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jquery-easypiechart/jquery.easypiechart.min.js" type="text/javascript"></script>#}
{#<script src="{{STATIC_URL}}assets/global/plugins/jquery.sparkline.min.js" type="text/javascript"></script>#}
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN PAGE LEVEL SCRIPTS -->
<script src="{{STATIC_URL}}assets/global/scripts/metronic.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/admin/layout2/scripts/layout.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/admin/layout2/scripts/demo.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/admin/pages/scripts/index.js" type="text/javascript"></script>
<script src="{{STATIC_URL}}assets/admin/pages/scripts/tasks.js" type="text/javascript"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/clockface/js/clockface.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<!-- END PAGE LEVEL SCRIPTS -->
{#<script>#}
{#jQuery(document).ready(function() {#}
{#   Metronic.init(); // init metronic core componets#}
{#   Layout.init(); // init layout#}
{#   Demo.init(); // init demo features#}
{#   Index.init();#}
{#   Index.initDashboardDaterange();#}
{#   Index.initJQVMAP(); // init index page's custom scripts#}
{#   Index.initCalendar(); // init index page's custom scripts#}
{#   Index.initCharts(); // init index page's custom scripts#}
{#   Index.initChat();#}
{#   Index.initMiniCharts();#}
{#   Tasks.initDashboardWidget();#}
{#});#}
{#</script>#}
<!-- END JAVASCRIPTS -->
<script src="{{ STATIC_URL }}card.js"></script>

</body>
<!-- END BODY -->
</html>
