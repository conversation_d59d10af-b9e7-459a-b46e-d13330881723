{% extends pagamento_template %}
{% load i18n %}
{% block sidebar %} {% endblock %}
{% block pageactions %}  {% endblock %}
{% block pagetop %}  {% endblock %}
{% block pagetoolbar %} {% endblock %}
{% block title %} Pagamento Plano Pago {% endblock %}
    {% block google_tag_head %}
        {% if negocio.google_tag_enabled %}
        {{ negocio.google_tag_head|safe }}
        {% endif %}
    {% endblock %}
    {% block google_tag_body %}
        {% if negocio.google_tag_enabled %}
        {{ negocio.google_tag_body|safe }}
        {% endif %}
    {% endblock %}
{% block content %}
<script src="{{ STATIC_URL }}card.js" xmlns="http://www.w3.org/1999/html"></script>

{#<div class="page-content">#}
    <div class="row">
    <div class="portlet box {% if result.cor_evento %} {{ result.cor_evento }} {% else %}blue {% endif %}">
        <div class="portlet-title">
            <h2>PlanoPago: {{ result.nome_plano }}</h2>
                <div class="caption">

                </div>

        </div>
    </div>
    </div>
    <div class="row" id="logos">
        <div class="col-md-6" id="logoevento">
            <img width="202ppx"  src={{result.logo_evento}}{{ logo_evento }}>
        </div>
        <div class="col-md-6" id="logosociedade">
            <img src="{{ STATIC_URL }}media/logos/CCM-logo.jpg">
        </div>


            <br>


    </div>

 <div class="row" id="order">

<div class="col-md-9">
    <div class="portlet box {% if result.cor_evento %} {{ result.cor_evento }} {% else %}blue {% endif %}">
                                <div class="portlet-title">
                                    <div class="caption">
                                    </i>{% trans "Formas de Pagamento" %} </div>

                                </div>
                                <div class="portlet-body">

                                <img src="{{ STATIC_URL}}media/ajax-loader.gif" id="gif" style="display: block; margin: 0 auto; width: 200px; visibility: hidden;">



                                    <div class="tabbable-custom nav-justified">
                                        <ul class="nav nav-tabs nav-justified">
                                            {% if result.cartao_ativado %}
                                            <li class="active">
                                                <a href="#tab_1_1_1" data-toggle="tab"> {% trans "Cartão de Crédito" %}</a>
                                            </li>
                                            {% endif %}
                                            {% if result.boleto_ativado %}
                                            <li>
                                                <a href="#tab_1_1_2" data-toggle="tab"> Boleto</a>
                                            </li>
                                            {% endif %}
                                            {% if result.deposito_ativado %}
                                            <li>
                                                <a href="#tab_1_1_3" data-toggle="tab"> Depósito Bancário</a>
                                            </li>
                                        {% endif %}
                                            {% if result.pix_ativado %}
                                            <li>
                                                <a href="#tab_1_1_4" data-toggle="tab"> PIX</a>
                                            </li>
                                        {% endif %}
                                        </ul>
                                        <div class="tab-content">
                                            <div class="tab-pane active" id="tab_1_1_1">

                                                 <div class="row">
{% if result.cartao_ativado %}
            <div class="col-md-5">
                <div class="portlet-body form justifyleft">
                    <form method="POST" action="" autocomplete="off" id="pagamento_form">
                        {% csrf_token %}
                         <div class="form-body">
                             {{ form.as_p }}
{#                            {% crispy form %}#}
                         </div>
                    <input type="submit" value={% trans "Pagar" %} class="btn {% if result.cor_evento %} {{ result.cor_evento }} {% else %}green {% endif %}"/>
{#                        <button type="submit" class="btn green">Pagar</button>#}
                    </form>
{#                    <img src="http://pro.imasters.com.br/cielo/selos_cielo_logos_credito.png" width="332" height="102" />#}
                </div>
            </div>
        <div class="col-md-5-1">
            <div class="portlet-body">
                <div class='card-wrapper'></div>
            </div>
        </div>

    </div>




<script>
    var card = new Card({
    // a selector or DOM element for the form where users will
    // be entering their information
    form: 'form', // *required*
    // a selector or DOM element for the container
    // where you want the card to appear
    container: '.card-wrapper', // *required*
//        nameInput: 'input[name="first-name"], input[name="last-name"]'

    formSelectors: {
        numberInput: 'input#id_numero_cartao', // optional — default input[name="number"]
        //expiryInput: ['input#expiracao_ano' , 'input#expiracao_mes'], // optional — default input[name="expiry"]
        cvcInput: 'input#id_codigo_seguranca', // optional — default input[name="cvc"]
        nameInput: 'input#id_nome_cartao' // optional - defaults input[name="name"]
    },

    width: 200, // optional — default 350px
    formatting: true, // optional - default true

    // Strings for translation - optional
    messages: {
        validDate: 'valid\ndate', // optional - default 'valid\nthru'
        monthYear: 'mm/yyyy', // optional - default 'month/year'
    },

    // Default placeholders for rendered fields - optional
    placeholders: {
        number: '•••• •••• •••• ••••',
        name: 'NOME NO CARTAO',
        expiry: 'MM/AAAA',
        cvc: '•••'
    },

    // if true, will log helpful messages for setting up Card
    debug: true // optional - default false
});
    </script>


                                            </div>
                                            {% endif %}
                                        {% if result.boleto_ativado %}
{########FIM TAB1#}
                                            <div class="tab-pane" id="tab_1_1_2">
                                                <p> Por favor digite os dados para enviar boleto para E-mail </p>
                                                <form action="{% url "criarboleto" %}" method="GET">
                                                    {% csrf_token %}
                                                    <p><label for="id_CPF">Nome</label> <input id="id_Nome" name="Nome" type="text" size="30"></p>
                                                <p><label for="id_CPF">Cpf:</label> <input id="id_CPF" name="CPF" type="text"></p>
                                                    <p><label for="id_CPF">E-mail</label> <input id="id_Email" name="Email" type="text"></p>
                                                    <p><label for="id_telefone">Telefone</label> <input id="id_telefone" name="Telefone" type="text"></p>
                                                    <input type="hidden" value="{{ plano.id }}" name="Plano" />
                                                <p>
                                                    <input type="submit" value="Gerar Boleto" class="btn green">
                                                </p>
                                                    </form>
                                            </div>
                                        {% endif %}
                                        {% if result.deposito_ativado %}
                                            <div class="tab-pane" id="tab_1_1_3">
                                                <p> Dados para Deposito Bancario </p>
                                                <p>Caso deseje fazer depósito bancário utilize esta conta </p>
                                                <p> Banco : 399 <p>
                                                <p> CC: 0252 138280 </p>
                                                <p> Nome : Sociedade Y </p>
                                                <p> CNPJ : 03432.3432./0001</p>
                                            </div>
                                        {% endif %}
                                        </div>
                                    </div>

                                </div>
    </div>

 </div>

 <div class="col-md-3">
      <div class="portlet box {% if result.cor_evento %} {{ result.cor_evento }} {% else %}blue {% endif %}">
                                <div class="portlet-title">
                                    <div class="caption">
                                    </i>{% trans "Total" %}</div>

                                </div>
                                <div class="portlet-body">
                                        <div class="col-md-5">
                                           {% trans "Total Compra" %}
                                        </div>
                                    <div class="col-md-3">
                                    {{ result.montante }}
                                    </div>
                                    <br>
                                </div>

 </div>

</div>

    {% endblock %}
{% block endbody %}
<script src="{{ STATIC_URL }}jquery.maskedinput.js"></script>
 {% block quicksidebar %} {% endblock %}
<script type="application/javascript">
jQuery(document).ready(function() {
$('#id_nome_beneficiario').hide();
$('label[for=id_nome_beneficiario], input#id_nome_beneficiario').hide()
    $('#id_Pagamento_terceiro').click(function(){

        if ($('#id_nome_beneficiario').is(":visible"))
            {
                $('#id_nome_beneficiario').hide()
                $('label[for=id_nome_beneficiario], input#id_nome_beneficiario').hide()
            }
        else {

            $('#id_CPF_Beneficiario').show();
            $('label[for=id_nome_beneficiario], input#id_nome_beneficiario').show()
        }
//time for show
});



    {% if result.formato_cpf %}
                $("#id_CPF").mask("999.999.999-99");
                $('#tab_1_1_2 >form > p > input#id_CPF').mask("999.999.999-99");
    {% endif %}
                $('#pagamento_form').submit(function() {
                    $('#gif').css('visibility', 'visible');
                    $('#gif').css('position', 'absolute');
                    $('#gif').css('top', '50%');
                    $('#gif').css('left', '50%');
                    $('#gif').css('margin', '-50px 0px 0px -50px');
                    $(':submit',this).attr('disabled','disabled');

                });

        });
                </script>
{% endblock %}

</div>