{% extends pagamento_template %}
{% load i18n %}
{% block sidebar %} {% endblock %}
{% block pageactions %}  {% endblock %}
{% block pagetop %}  {% endblock %}
{% block pagetoolbar %} {% endblock %}
{% block title %} Pagamento Plano Pago {% endblock %}
    {% block google_tag_head %}
        {% if negocio.google_tag_enabled %}
        {{ negocio.google_tag_head|safe }}
        {% endif %}
    {% endblock %}
    {% block google_tag_body %}
        {% if negocio.google_tag_enabled %}
        {{ negocio.google_tag_body|safe }}
        {% endif %}
    {% endblock %}
{% block content %}
<script src="{{ STATIC_URL }}card.js" xmlns="http://www.w3.org/1999/html"></script>
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

{#<div class="page-content">#}
    <div class="row">
    <div class="portlet box {% if result.cor_evento %} {{ result.cor_evento }} {% else %}blue {% endif %}">
        <div class="portlet-title">
            <h2>PlanoPago: {{ result.nome_plano }}</h2>
                <div class="caption">

                </div>

        </div>
    </div>
    </div>
    <div class="row" id="logos">
        <div class="col-md-6" id="logoevento">
            <img width="202ppx"  src={{result.logo_evento}}{{ logo_evento }}>
        </div>
        <div class="col-md-6" id="logosociedade">
            <img src="{{ STATIC_URL }}media/logos/CCM-logo.jpg">
        </div>


            <br>


    </div>

 <div class="row" id="order">

<div class="col-md-9">
    <div class="portlet box {% if result.cor_evento %} {{ result.cor_evento }} {% else %}blue {% endif %}">
                                <div class="portlet-title">
                                    <div class="caption">
                                    </i>{% trans "Formas de Pagamento" %} </div>

                                </div>
                                <div class="portlet-body">

                                <img src="{{ STATIC_URL}}media/ajax-loader.gif" id="gif" style="display: block; margin: 0 auto; width: 200px; visibility: hidden;">



                                    <div class="tabbable-custom nav-justified">
                                        <ul class="nav nav-tabs nav-justified">
                                            {% if result.cartao_ativado %}
                                            <li class="active">
                                                <a href="#tab_1_1_1" data-toggle="tab"> {% trans "Cartão de Crédito" %}</a>
                                            </li>
                                            {% endif %}
                                            {% if result.boleto_ativado %}
                                            <li>
                                                <a href="#tab_1_1_2" data-toggle="tab"> Boleto</a>
                                            </li>
                                            {% endif %}
                                            {% if result.deposito_ativado %}
                                            <li>
                                                <a href="#tab_1_1_3" data-toggle="tab"> Depósito Bancário</a>
                                            </li>
                                        {% endif %}
                                            {% if result.pix_ativado %}
                                            <li>
                                                <a href="#tab_1_1_4" data-toggle="tab"> PIX</a>
                                            </li>
                                        {% endif %}
                                        </ul>
                                        <div class="tab-content">
                                            <div class="tab-pane active" id="tab_1_1_1">

                                                 <div class="row">
{% if result.cartao_ativado %}
            <div class="col-md-5">
                <div class="portlet-body form justifyleft">
                    <form method="POST" action="" autocomplete="off" id="pagamento_form">
                        {% csrf_token %}
                         <div class="form-body">
                             {{ form.as_p }}
{#                            {% crispy form %}#}
                         </div>
                    <input type="submit" value={% trans "Pagar" %} class="btn {% if result.cor_evento %} {{ result.cor_evento }} {% else %}green {% endif %}"/>
{#                        <button type="submit" class="btn green">Pagar</button>#}
                    </form>
{#                    <img src="http://pro.imasters.com.br/cielo/selos_cielo_logos_credito.png" width="332" height="102" />#}
                </div>
            </div>
        <div class="col-md-5-1">
            <div class="portlet-body">
                <div class='card-wrapper'></div>
            </div>
        </div>

    </div>




<script>
    var card = new Card({
    // a selector or DOM element for the form where users will
    // be entering their information
    form: 'form', // *required*
    // a selector or DOM element for the container
    // where you want the card to appear
    container: '.card-wrapper', // *required*
//        nameInput: 'input[name="first-name"], input[name="last-name"]'

    formSelectors: {
        numberInput: 'input#id_numero_cartao', // optional — default input[name="number"]
        //expiryInput: ['input#expiracao_ano' , 'input#expiracao_mes'], // optional — default input[name="expiry"]
        cvcInput: 'input#id_codigo_seguranca', // optional — default input[name="cvc"]
        nameInput: 'input#id_nome_cartao' // optional - defaults input[name="name"]
    },

    width: 200, // optional — default 350px
    formatting: true, // optional - default true

    // Strings for translation - optional
    messages: {
        validDate: 'valid\ndate', // optional - default 'valid\nthru'
        monthYear: 'mm/yyyy', // optional - default 'month/year'
    },

    // Default placeholders for rendered fields - optional
    placeholders: {
        number: '•••• •••• •••• ••••',
        name: 'NOME NO CARTAO',
        expiry: 'MM/AAAA',
        cvc: '•••'
    },

    // if true, will log helpful messages for setting up Card
    debug: true // optional - default false
});
    </script>


                                            </div>
                                            {% endif %}
                                        {% if result.boleto_ativado %}
{########FIM TAB1#}
                                            <div class="tab-pane" id="tab_1_1_2">
                                                <p> Por favor digite os dados para enviar boleto para E-mail </p>
                                                <form action="{% url "criarboleto" %}" method="GET">
                                                    {% csrf_token %}
                                                    <p><label for="id_CPF">Nome</label> <input id="id_Nome" name="Nome" type="text" size="30"></p>
                                                <p><label for="id_CPF">Cpf:</label> <input id="id_CPF" name="CPF" type="text"></p>
                                                    <p><label for="id_CPF">E-mail</label> <input id="id_Email" name="Email" type="text"></p>
                                                    <p><label for="id_telefone">Telefone</label> <input id="id_telefone" name="Telefone" type="text"></p>
                                                    <input type="hidden" value="{{ plano.id }}" name="Plano" />
                                                <p>
                                                    <input type="submit" value="Gerar Boleto" class="btn green">
                                                </p>
                                                    </form>
                                            </div>
                                        {% endif %}
                                        {% if result.deposito_ativado %}
                                            <div class="tab-pane" id="tab_1_1_3">
                                                <p> Dados para Deposito Bancario </p>
                                                <p>Caso deseje fazer depósito bancário utilize esta conta </p>
                                                <p> Banco : 399 <p>
                                                <p> CC: 0252 138280 </p>
                                                <p> Nome : Sociedade Y </p>
                                                <p> CNPJ : 03432.3432./0001</p>
                                            </div>
                                        {% endif %}
                                        {% if result.pix_ativado %}
                                            <div class="tab-pane" id="tab_1_1_4">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h4>Pagamento via PIX</h4>
                                                        <p>Escaneie o QR Code abaixo com o aplicativo do seu banco:</p>
                                                        <div id="qr-code-container" style="text-align: center; margin: 20px 0;">
                                                            <div id="qr-code-placeholder" style="border: 2px dashed #ccc; padding: 40px; margin: 20px 0;">
                                                                <p>QR Code será gerado aqui</p>
                                                                <button type="button" id="gerar-pix" class="btn btn-primary">Gerar PIX</button>
                                                            </div>
                                                        </div>
                                                        <div id="pix-info" style="display: none;">
                                                            <h5>Informações do PIX:</h5>
                                                            <p><strong>Valor:</strong> R$ <span id="pix-valor">{{ result.montante }}</span></p>
                                                            <p><strong>Chave PIX:</strong> <span id="pix-chave"></span></p>
                                                            <p><strong>Código PIX:</strong></p>
                                                            <textarea id="pix-codigo" readonly style="width: 100%; height: 100px; font-size: 12px;"></textarea>
                                                            <button type="button" id="copiar-pix" class="btn btn-success">Copiar Código PIX</button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="alert alert-info">
                                                            <h5>Como pagar com PIX:</h5>
                                                            <ol>
                                                                <li>Clique em "Gerar PIX" para criar o código</li>
                                                                <li>Abra o aplicativo do seu banco</li>
                                                                <li>Escolha a opção PIX</li>
                                                                <li>Escaneie o QR Code ou copie e cole o código</li>
                                                                <li>Confirme o pagamento</li>
                                                            </ol>
                                                            <p><strong>Atenção:</strong> O PIX tem validade de 30 minutos.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                        </div>
                                    </div>

                                </div>
    </div>

 </div>

 <div class="col-md-3">
      <div class="portlet box {% if result.cor_evento %} {{ result.cor_evento }} {% else %}blue {% endif %}">
                                <div class="portlet-title">
                                    <div class="caption">
                                    </i>{% trans "Total" %}</div>

                                </div>
                                <div class="portlet-body">
                                        <div class="col-md-5">
                                           {% trans "Total Compra" %}
                                        </div>
                                    <div class="col-md-3">
                                    {{ result.montante }}
                                    </div>
                                    <br>
                                </div>

 </div>

</div>

    {% endblock %}
{% block endbody %}
<script src="{{ STATIC_URL }}jquery.maskedinput.js"></script>
 {% block quicksidebar %} {% endblock %}
<script type="application/javascript">
jQuery(document).ready(function() {
$('#id_nome_beneficiario').hide();
$('label[for=id_nome_beneficiario], input#id_nome_beneficiario').hide()
    $('#id_Pagamento_terceiro').click(function(){

        if ($('#id_nome_beneficiario').is(":visible"))
            {
                $('#id_nome_beneficiario').hide()
                $('label[for=id_nome_beneficiario], input#id_nome_beneficiario').hide()
            }
        else {

            $('#id_CPF_Beneficiario').show();
            $('label[for=id_nome_beneficiario], input#id_nome_beneficiario').show()
        }
//time for show
});



    {% if result.formato_cpf %}
                $("#id_CPF").mask("999.999.999-99");
                $('#tab_1_1_2 >form > p > input#id_CPF').mask("999.999.999-99");
    {% endif %}
                $('#pagamento_form').submit(function() {
                    $('#gif').css('visibility', 'visible');
                    $('#gif').css('position', 'absolute');
                    $('#gif').css('top', '50%');
                    $('#gif').css('left', '50%');
                    $('#gif').css('margin', '-50px 0px 0px -50px');
                    $(':submit',this).attr('disabled','disabled');

                });

    // Funcionalidade PIX
    $('#gerar-pix').click(function() {
        var valor = '{{ result.montante }}';
        var button = $(this);

        button.prop('disabled', true).text('Gerando...');

        // Obter dados do formulário de pagamento se existirem
        var nome = $('#id_nome_cartao').val() || 'Cliente PIX';
        var cpf = $('#id_CPF').val() || '';
        var email = $('#id_Email').val() || '';
        var plano_id = '{{ plano.id }}';  // Deve ser passado do contexto

        // Chamar API da Cielo para gerar PIX usando a arquitetura existente
        $.ajax({
            url: '{% url "gerar_pix" %}',
            method: 'POST',
            data: {
                'valor': valor,
                'plano_id': plano_id,
                'nome': nome,
                'cpf': cpf,
                'email': email,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    // Mostrar QR Code
                    $('#qr-code-placeholder').html('<canvas id="qrcode"></canvas>');

                    // Gerar QR Code usando a biblioteca qrcode.js
                    if (typeof QRCode !== 'undefined') {
                        QRCode.toCanvas(document.getElementById("qrcode"), response.pix_code, {
                            width: 200,
                            height: 200,
                            margin: 2
                        }, function (error) {
                            if (error) {
                                console.error(error);
                                $('#qr-code-placeholder').html('<p>Erro ao gerar QR Code</p>');
                            }
                        });
                    } else {
                        $('#qr-code-placeholder').html('<p>QR Code: ' + response.pix_code + '</p>');
                    }

                    $('#pix-chave').text(response.pix_key);
                    $('#pix-codigo').val(response.pix_code);
                    $('#pix-info').show();
                    button.hide();
                } else {
                    alert('Erro ao gerar PIX: ' + response.message);
                    button.prop('disabled', false).text('Gerar PIX');
                }
            },
            error: function() {
                alert('Erro ao conectar com o servidor');
                button.prop('disabled', false).text('Gerar PIX');
            }
        });
    });

    // Copiar código PIX
    $('#copiar-pix').click(function() {
        var pixCode = $('#pix-codigo');
        pixCode.select();
        document.execCommand('copy');

        var button = $(this);
        var originalText = button.text();
        button.text('Copiado!').addClass('btn-success');

        setTimeout(function() {
            button.text(originalText).removeClass('btn-success');
        }, 2000);
    });

        });
                </script>
{% endblock %}

</div>