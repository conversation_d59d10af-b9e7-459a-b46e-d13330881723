{% extends "admin4/index.html" %}
{% block title %} PlanoPago PLanos de Pagamento {% endblock %}

 {% block quicksidebar %} {% endblock %}
{% block pageactions %}  {% endblock %}
{% block pagetop %}  {% endblock %}
{% block pagetoolbar %} {% endblock %}
{% block head %}
<link href="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
        <link href="{{STATIC_URL}}assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
        <link href="{{STATIC_URL}}assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
        <link href="{{STATIC_URL}}assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
        <link href="{{STATIC_URL}}assets/global/plugins/clockface/css/clockface.css" rel="stylesheet" type="text/css" />
<script src="{{ STATIC_URL }}assets/pages/scripts/components-date-time-pickers.min.js" type="text/javascript"></script>

    <link rel="stylesheet" type="text/css" href="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/daterangepicker-bs3.css" />

{% endblock %}
{% block content %}
    <div class="portlet-body form">
            <form action="" class="form-horizontal" id="submit_form" method="POST" novalidate="novalidate">{% csrf_token %}
                <div class="form-wizard">
                    <div class="form-body">
                        <ul class="nav nav-pills nav-justified steps">
                            <li class="active">
                                <a href="#tab1" data-toggle="tab" class="step" aria-expanded="true">
                                    <span class="number"> 1 </span>
                                    <span class="desc">
                                        <i class="fa fa-check"></i> Tipo de Cobrança</span>
                                </a>
                            </li>
                            <li>
                                <a href="#tab2" data-toggle="tab" class="step">
                                    <span class="number"> 2 </span>
                                    <span class="desc">
                                        <i class="fa fa-check"></i> Grupo de Cobranca </span>
                                </a>
                            </li>
                            <li>
                                <a href="#tab3" data-toggle="tab" class="step active">
                                    <span class="number"> 3 </span>
                                    <span class="desc">
                                        <i class="fa fa-check"></i> Detalhes</span>
                                </a>
                            </li>
                            <li>
                                <a href="#tab4" data-toggle="tab" class="step">
                                    <span class="number"> 4 </span>
                                    <span class="desc">
                                        <i class="fa fa-check"></i> Confirmar </span>
                                </a>
                            </li>
                        </ul>
                        <div id="bar" class="progress progress-striped" role="progressbar">
                            <div class="progress-bar progress-bar-success" style="width: 25%;"> </div>
                        </div>
                        <div class="tab-content">
                            <div class="alert alert-danger display-none">
                                <button class="close" data-dismiss="alert"></button> You have some form errors. Please check below. </div>
                            <div class="alert alert-success display-none">
                                <button class="close" data-dismiss="alert"></button> Your form validation is successful! </div>
                            <div class="tab-pane active" id="tab1">
                                <h3 class="block">Criar Cobranca</h3>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Tipo Cobrança
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-4">
                                        <div class="form-md-radios">
                                        {{ form.tipo }}</div>
                                        <span class="help-block"> Escolha Tipo </span>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="tab2">
                                <h3 class="block">Defina Grupo de Cobranca</h3>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Grupo
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-2">
                                        <div class="form-control">
                                            <select id="{{ form.categoria.id_for_label }}" name="categoria">
                                            <option value="Novo">Novo...</option>
                                                {% for option in categorias %}

                                                <option value="{{option.0 }}">{{ option.0 }}</option>
                                                {% endfor %}
                                            </select>
{#                                            {{ form.grupo }}#}

                                        </div>
                                        <span class="help-block"> Escolha Grupo </span>
                                    </div>
                                </div>
                            <div class="form-group" id="novo-grupo-form">
                                    <label class="control-label col-md-3">Criar Novo
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="{{ form.novo_grupo.name   }}" id="{{ form.novo_grupo.id_for_label }}">
                                        <span class="help-block"> </span>
                                    </div>
                                </div>

                            </div>
                            <div class="tab-pane" id="tab3">
                                <h3 class="block">Entre com os detalhes</h3>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Nome da Cobrança
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" id="{{ form.nome.id_for_label }}" name="{{ form.nome.name   }}">
                                        <span class="help-block"> </span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Numero de parcelas
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="{{ form.parcelas.name }}">
                                        <span class="help-block"> </span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Montante
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-4">
                                        <input type="text" placeholder="" class="form-control" name="{{ form.montante.name }}">
                                        <span class="help-block"> </span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Data Inicio(DD/MM/AAAA)
                                        <span class="required" aria-required="true"> * </span>
                                    </label>
                                    <div class="col-md-4">
                                        <input type="text" placeholder="DD/MM/AAAA" maxlength="10" name="{{ form.data_inicio.name }}" class="form-control" >
                                        <span class="help-block"> ex. 15/11/2016 </span>
                                    </div>
                                </div>

                            </div>
                            <div class="tab-pane" id="tab4">
                                <h3 class="block">Confirme os dados</h3>
                                <h4 class="form-section">Tipo</h4>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Tipo</label>
                                    <div class="col-md-4">
                                        <p class="form-control-static" data-display="tipo"> </p>
                                    </div>
                                </div>

                                <h4 class="form-section">Grupo de Cobraça</h4>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Grupo:</label>
                                    <div class="col-md-4">
                                        <p class="form-control-static" data-display="categoria"> </p>
                                    </div>
                                </div>
                                <h4 class="form-section">Detalhes</h4>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Nome Cobrança:</label>
                                    <div class="col-md-4">
                                        <p class="form-control-static" data-display="nome"> </p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Numero Parcelas:</label>
                                    <div class="col-md-4">
                                        <p class="form-control-static" data-display="parcelas"> </p>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-md-3">Montante:</label>
                                    <div class="col-md-4">
                                        <p class="form-control-static" data-display="montante"> </p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-3">Data Inicio:</label>
                                    <div class="col-md-4">
                                        <p class="form-control-static" data-display="data_inicio"> </p>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="form-actions">

                        <div class="row">
                            <div class="col-md-offset-3 col-md-9">
                                <a href="javascript:;" class="btn default button-previous disabled" style="display: none;">
                                    <i class="fa fa-angle-left"></i> Voltar </a>
                                <a href="javascript:;" class="btn btn-outline green button-next" id="next"> Proximo
                                    <i class="fa fa-angle-right"></i>
                                </a>
                                <a href="javascript:;" class="btn green button-submit" style="display: none;"> Criar
                                    <i class="fa fa-check"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
{#        </div>#}
{#    </div>#}



{% endblock %}
{% block endbody %}
    <script src="{{ STATIC_URL }}assets/global/scripts/app.min.js" type="text/javascript"></script>

    <script src="{{ STATIC_URL }}assets/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script src="{{ STATIC_URL }}assets/global/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="{{ STATIC_URL }}assets/global/plugins/jquery-validation/js/additional-methods.min.js" type="text/javascript"></script>
    <script src="{{ STATIC_URL }}assets/global/plugins/bootstrap-wizard/jquery.bootstrap.wizard.min.js" type="text/javascript"></script>

    <script src="{{ STATIC_URL }}assets/pages/scripts/form-wizard.min.js" type="text/javascript"></script>
        <script type="text/javascript" src="{{ STATIC_URL }}assets/global/plugins/bootstrap-daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}assets/global/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}planoform.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}assets/global/plugins/bootstrap-wizard/jquery.bootstrap.wizard.min.js"></script>


{% endblock %}
