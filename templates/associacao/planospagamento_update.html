{% extends "admin4//index.html" %}

{% block title %} PlanoPago Editar Planos  {% endblock %}

{% block quicksidebar %}. {% endblock %}
{% block pageactions %} . {% endblock %}
{% block pagetop %} . {% endblock %}
{% block content %}
{#<div class="page-content">#}
<div class="portlet light bordered">
										<div class="portlet-title">
											<div class="caption font-red-sunglo">
												<i class="fa fa-gift"></i>{{ object.nome_plano }}
											</div>


										</div>
										<div class="portlet-body form">
											<!-- BEGIN FORM-->
											<form action="" method="POST" class="form-horizontal" >
                                                {{ form.non_field_errors }}
                                                {% csrf_token %}
												<div class="form-body">
													<h3 class="form-section">Definicoes de {{ object.nome_plano }}</h3>
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label col-md-3">Nome da <PERSON></label>
																<div class="col-md-9">
																	<input id="{{ form.nome_plano.id_for_label }}" name="nome_plano" type="text" class="form-control" placeholder="{{ object.nome_plano }}">
																	<span class="help-block">
																	Nome {{ form.nome_plano.errors }} </span>
																</div>
															</div>
														</div>
														<!--/span-->
                                                        <div class="col-md-6">
															<div class="form-group">
																<label class="control-label col-md-3">Parcelas</label>
																<div class="col-md-9">
																	<input id="{{ form.frequencia.id_for_label }}" name="frequencia" type="number" class="form-control" placeholder="{{ object.frequencia }}">
																	<span class="help-block">
																	Parcelas {{ form.frequencia.errors}}</span>
																</div>
															</div>
														</div>
{#										#}
														<!--/span-->
													</div>
													<!--/row-->
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label col-md-3">Tipo</label>
																<div class="col-md-9">
																	<select id="{{ form.frequencia_intervalo.id_for_label }}" name="frequencia_intervalo" class="form-control">
																		<option value="Y">Anual</option>
																		<option value="M">Mensal</option>
																	</select>
																	<span class="help-block">
																	Selecione forma de cobrança{{ form.frequencia_intervalo.errors}}</span>
																</div>
															</div>
														</div>
														<!--/span-->
														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label col-md-3">Data de Inicio Cobrança</label>
																<div class="col-md-9">
																	<input id="{{ form.data_ciclo.id_for_label }}" type="text" name="data_ciclo" class="form-control" placeholder="{{ object.data_ciclo }}">
																</div>
															</div>
														</div>
														<!--/span-->
													</div>
													<!--/row-->
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label col-md-3">Tipo</label>
																<div class="col-md-9">
																	<div class="select2-container select2_category form-control" id="s2id_autogen9">
                                                                        <select class="select2_category form-control select2-offscreen" data-placeholder="Escolha Tipo" tabindex="-1" title="">
																		<option value="Category 1">Anuidade</option>
																		<option value="Category 2">Eventos</option>
																		<option value="Category 3">Serviços</option>
																		<option value="Category 4">Cursos</option>
																	    </select>
                                                                    </div>
																</div>
															</div>
														</div>
														<!--/span-->
														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label col-md-3">Valor</label>
																<div class="col-md-9">
																	<input id="{{ form.montante.id_for_label }}" name="montante" type="number" class="form-control" placeholder="${{ object.montante }}">
																	<span class="help-block">
																	 </span>
																</div>
															</div>
														</div>
														<!--/span-->
													</div>

														<!--/span-->
													</div>
													<!--/row-->
												<div class="form-actions">
													<div class="row">
														<div class="col-md-6">
															<div class="row">
																<div class="col-md-offset-3 col-md-9">
																	<button type="submit" class="btn green">Submit</button>
																	<button type="button" class="btn default">Cancel</button>
																</div>
															</div>
														</div>
														<div class="col-md-6">
														</div>
													</div>
												</div>
											</form>
											<!-- END FORM-->
										</div>
									</div>
{#<form action="" method="post">{% csrf_token %}#}
{#    {{ form.as_p }}#}
{#    <input type="submit" value="Aplicar" />#}
{#</form>#}
    </div>
{% endblock %}
<script type="text/javascript" src="{{ STATIC_URL }}/assets/global/plugins/select2/select2.min.js"></script>