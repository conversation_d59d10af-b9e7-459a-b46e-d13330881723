{% extends "admin4/index.html" %}
{% load diasdesde %}
 {% block quicksidebar %} {% endblock %}
{% block pageactions %}  {% endblock %}
{% block pagetop %}  {% endblock %}
{% block pagetoolbar %} {% endblock %}
{% block title %} Extrato Financeiro {% endblock %}
{% block content %}
{#<div class="page-content">#}
<div class="btn-group open">
                                                                        <button class="btn btn-circle red btn-sm dropdown-toggle" type="button" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" aria-expanded="true"> Exportar
                                                                            <i class="fa fa-angle-down"></i>
                                                                        </button>
                                                                        <ul class="dropdown-menu pull-right" role="menu">

                                                                            <li>
                                                                                <a href="{% url "associacaoextratoxls" %}">Excel </a>
                                                                            </li>

                                                                        </ul>
                                                                    </div>                                <div class="portlet-body">
                                    <div class="table-scrollable">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    {% if not user.pessoa.visualiza_pagamentos %}
                                                    <th>#</th>
                                                    {% endif %}
                                                    <th>Remetente</th>
                                                    <th>Montante</th>
                                                    <th>Data</th>
                                                    <th>Status</th>
                                                    <th>Resgate</th>
                                                    <th>CPF</th>
                                                    <th>Plano</th>
                                                    <th>Incricao</th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                            {% for i in result %}
                                                <tr>
                                                {% if not user.pessoa.visualiza_pagamentos %}
                                                    <td><a href="{% url "detalhepagamento" i.id %}">{{ i.id }}</a> </td>
                                                    {% endif %}
                                                    <td> {{ i.pedido_set.all.0.nome }} </td>
                                                    <td> {{ i.montante }} </td>
                                                    <td> {{ i.data_hora}}</td>
                                                    <td>
                                                        {{ i.status}}
                                                    </td>
                                                    <td>
                                                        {{ i.data_hora|dias }}

                                                    </td>
                                                <td>{{ i.usuario.CPF }}</td>
                                                <td>{{ i.plano_pagamento.nome_plano}}</td>
                                                <td>{{ i.pedido_set.all.0.evecom_item.item_name }}</td>

                                                </tr>
                                            {% endfor %}

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <!-- END SAMPLE TABLE PORTLET-->
{% endblock %}
