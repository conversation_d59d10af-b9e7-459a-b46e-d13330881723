{% extends "admin4/index.html" %}
{% block quicksidebar %} {% endblock %}
{% block pageactions %}  {% endblock %}
{% block pagetop %}  {% endblock %}
{% block pagetoolbar %} {% endblock %}
{% block title %} Detalhes da Transação {% endblock %}
{% block content %}
{#<div class="page-content">#}

<div class="col-md-6 col-sm-12">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="fa fa-cogs"></i>Relatorio Conciliacao </div>
                    <div class="actions">
                        {% if result.status == 1 %}

                             <a href="#basic"  data-toggle="modal" class="btn btn-circle btn-default disabled-link">
                            <i class="fa fa-file-excel-o not_valid disabled"></i> Pendente </a>
                        {% else %}
                            <a href="{{ result.arquivo_output.url }}"  data-toggle="modal" class="btn btn-circle btn-default">
                        <i class="fa fa-file-excel-o valid big  "></i> Baixar </a>
                        {% endif %}
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row static-info">
                        <div class="col-md-5 name"> Chave acesso #: </div>
                        <div class="col-md-7 value"> {{ result.id }}
{#                            <span class="label label-info label-sm"> Confirmacao enviada por Email </span>#}
                        </div>
                    </div>
                    <div class="row static-info">
                        <div class="col-md-5 name"> Data e Hora: </div>
                        <div class="col-md-7 value"> {{ result.created_date }} </div>
                    </div>
                    <div class="row static-info">
                        <div class="col-md-5 name"> Status: </div>
                        <div class="col-md-7 value">
                            <span class="label label-success"> {{ result.get_status_display }} </span>
                        </div>
                    </div>
                    {% if result.status == 1 %}
                        <div class="row static-info">
                        <div class="col-md-7 value center label-danger"> Atualize a Pagina ate o Pendente mudar em Baixar </div>

                    </div>

                    {% endif %}
{#                    <div class="row static-info">#}
{#                        <div class="col-md-5 name"> Total: </div>#}
{#                        <div class="col-md-7 value"> {{ result.montante }} </div>#}
{#                    </div>#}
{#                  #}
                </div>
            </div>
</div>


</div>
                            <!-- END SAMPLE TABLE PORTLET--

{% endblock %}

{% block body_after %}
<script src="{{ STATIC_URL }}assets/pages/scripts/ui-modals.min.js" type="text/javascript"></script>

{% endblock %}