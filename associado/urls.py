"""associado URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/1.9/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  url(r'^$', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  url(r'^$', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.conf.urls import url, include
    2. Add a URL to urlpatterns:  url(r'^blog/', include('blog.urls'))
"""
from django.contrib.auth.views import LogoutView, LoginView
import django
from django.contrib import admin
from django.conf.urls import url, include
from associacao.views import Index, Associados, EditaAssociado, PlanosPagamentos,CancelaPagamento, \
    CriaPlanoPagamentos, EditaPlanoPagamentos, IndexAssociado, EditaPerfilProprioAssociado,\
    ClientePlanosPagamentos, ClientePagar, ResultadoPagamento, ProfileView, ExtratoAssociado, \
    ExtratoAssociacao, DetalhesDePagamento, ClientePagarTab, CriaCobrancaWizard, \
    AssociacaoRetornaEstatisticaJson, Criaboleto, ClientePagarTabEvecom,\
    ResultadoPagamentoPendente, MostraPagamentoComprovante, ExtratoAssociacaoExportaCsv,\
    PlanosPagamentosIfsoLac, ExtratoAssociacaoExportaXLS, ExtratoXLSTUDO, DetalheTransacaoGateway,\
    DetalheTransacaoGatewayForm, SubmeteArquivoConcilia, ListaImpersonateUsers, MostraConcilia,\
    PreFilterPayment, ClientePagarValidated, ClientePagarTabAssinatura, ResultadoPagamentoAssinatura,\
    CancelaAssinaturaporUuid, ClientePagarTabMontanteFlexivel
from django.conf.urls.static import static
from django.conf import settings
from django.views.decorators.cache import never_cache, cache_control
from django.contrib.auth.decorators import login_required


urlpatterns = [
    url(r'^admin/', admin.site.urls),
    url(r'^$', Index.as_view(), name='indexmain'),
    url(r'^cliente/$', IndexAssociado.as_view(), name='indexassociados'),
    url(r'^associadosedita/(?P<pk>[\w-]+)$', EditaAssociado.as_view(), name='editaassociado'),
    url(r'^associados/$', Associados.as_view(), name='associados'),
    url(r'associacaoextrato/$', ExtratoAssociacao.as_view(), name="associacaoextrato"),
    url(r'associacaoextratocsv/$', ExtratoAssociacaoExportaCsv.as_view(), name='associacaoextratocsv'),
    url(r'associacaoextratoxls/$', ExtratoAssociacaoExportaXLS.as_view(), name='associacaoextratoxls'),
    url(r'associacaoextratoxlstudo/$',ExtratoXLSTUDO.as_view(), name="associacaoextratoxlstudo"),


    url(r'^planospagamento/$', PlanosPagamentos.as_view(), name='planospagamentos'),
    url(r'^clienteplanospagamento/$', ClientePlanosPagamentos.as_view(), name='clienteplanospagamentos'),
    url(r'^planospagamento/criar/$', CriaPlanoPagamentos.as_view(), name='criaplanospagamentos'),
    url(r'^planospagamento/editar/(?P<pk>[\w-]+)/$', EditaPlanoPagamentos.as_view(), name='editaplanospagamentos'),
    url(r'^associacao/pagamento/concilia/resultado/(?P<pk>[-\w]+)/$', MostraConcilia.as_view(), name='mostraconcilia'),
    url(r'^associacao/pagamento/concilia/$', SubmeteArquivoConcilia.as_view(), name='conciliagatewayform'),
    url(r'^associacao/pagamento/detalhegateway/(?P<tid>[-\w]+)/$', DetalheTransacaoGateway.as_view(), name='detalhepagamentogateway'),
    url(r'^associacao/pagamento/buscadetalhegateway/$', DetalheTransacaoGatewayForm.as_view(),name='buscadetalhepagamentogateway'),
    url(r'^associacao/pagamento/cancelar/(?P<pk>[-\w]+)/$', CancelaPagamento.as_view(), name='cancelapagamento'),
    url(r'^associacao/pagamento/detalhe/(?P<pk>[-\w]+)/$', DetalhesDePagamento.as_view(),name='detalhepagamento'),

                  # url(r'^criaplanopagamento/$', CriaPLanoWizard.as_view(), name='wizardplano'),
    url(r'^criaplanopagamentoform/$', CriaCobrancaWizard.as_view(), name='wizardplanoform'),
    # url(r'^criaplanopagamentoform2/$', submit_recipe, name='wizardplanoform'),
    url(r'^boleto/criar/$', Criaboleto.as_view(), name='criarboleto'),
    url(r'comprovante/(?P<pedido>[0-9a-z-]+)/$', MostraPagamentoComprovante.as_view(), name='comprovante'),
    url(r'ifsolac/$', PlanosPagamentosIfsoLac.as_view(), name='ifsolac'),


    url(r'^cliente/pagar/(?P<uuid>[\w-]+)/$', never_cache(ClientePagar.as_view()), name='clientepagar'),
    url(r'^cliente/pagamentotab/(?P<plano>[\w-]+)/$', never_cache(ClientePagarTab.as_view()), name='clientepagartab',),
    url(r'^cliente/doacao/(?P<plano>[\w-]+)/$', never_cache(ClientePagarTabMontanteFlexivel.as_view()), name='doacaotab',),
    url(r'^(?P<convenio>[\w-]+)/pagamento/$', never_cache(ClientePagarTabEvecom.as_view()), name='clientepagartabCCM',),
    #url(r'^(?P<convenio>[\w-]+)/assinatura2/(?P<plano>[\w-]+)$', never_cache(ClientePagarTabAssinaturaToken.as_view()), name='clientepagarassinaturatokoen', ),
    url(r'^(?P<convenio>[\w-]+)/assinatura/(?P<plano>[\w-]+)$', never_cache(ClientePagarTabAssinatura.as_view()), name='clientepagarassinatura', ),
    url(r'^(?P<convenio>[\w-]+)/assinatura/cancelar/(?P<plano>[\w-]+)$',never_cache(CancelaAssinaturaporUuid.as_view()), name='clientecancelarassinatura', ),
    url(r'^cliente/resultadopagamento/$', ResultadoPagamento.as_view(), name='resultadpagamento'),
    url(r'^cliente/resultadoassinatura/$', ResultadoPagamentoAssinatura.as_view(), name='resultadoassinatura'),
    url(r'^cliente/extratofinanceiro/$', ExtratoAssociado.as_view(), name='extratoassociado'),
    url(r'^cliente/(?P<pk>[\w-]+)/$', EditaPerfilProprioAssociado.as_view(), name='editacliente'),
    url(r'^retorno/$', ResultadoPagamentoPendente.as_view(), name='retornocielo'),
    url(r'^accounts/', include('allauth.urls')),
    url(r'^accounts/profile/$', ProfileView.as_view(), name='profile'),
    url(r'^accounts/login/$', login, name='login'),
    url(r'^accounts/logout/$', logout, {'template_name': 'logout.html', 'extra_context': {'next': '/'}}, name='logout'),
    #ajax
    url(r'^estatistica/(?P<tipo>[0-9]+)/$', AssociacaoRetornaEstatisticaJson.as_view(), name='estatistica'),
    url(r'^gerar-pix/$', 'associacao.views.gerar_pix', name='gerar_pix'),

    #impersonate
    url(r'^impersonate/', include('impersonate.urls')),
    url(r'associacao/listarpersonalizar/', login_required(ListaImpersonateUsers.as_view()), name='listarpersonalizar'),

    #validation
    url(r'pagamento/(?P<uuid>[\w-]+)/$',PreFilterPayment.as_view(), name="prevalidar"),
    url(r'pagament/valid/', ClientePagarValidated.as_view(), name="prevalidado"),


] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
